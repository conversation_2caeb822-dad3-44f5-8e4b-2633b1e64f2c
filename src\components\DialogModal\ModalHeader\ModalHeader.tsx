import classNames from '@utils/classNames';

import styles from './ModalHeader.module.css';
import { ModalHeaderProps } from './ModalHeader.types';

const ModalHeader = ({
  children,
  wrapperClassName = null,
  wrapperContainerClassName = null,
  isHeadingStatic = false,
  headingStaticContent = null
}: ModalHeaderProps) => <>
    {isHeadingStatic && (
      <div className={styles.scrollHeading}>
        <span className={styles.scrollHeadingText}>{headingStaticContent}</span>
      </div>
    )}
    <div className={classNames(styles.containerWrapper, wrapperClassName)}>
      <div className={classNames(styles.containerScroll)}>
        <div
          className={classNames(styles.container, wrapperContainerClassName)}
        >
          {children}
        </div>
      </div>
    </div>
  </>;

export default ModalHeader;
