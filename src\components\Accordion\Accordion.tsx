import React, { ReactNode, useEffect, useState } from 'react';
import { SimpleTheme } from '@libtypes/common';
import classNames from '@utils/classNames';

import styles from './Accordion.module.css';
import AccordionListItem from './AccordionListItem';

export interface AccordionItem {
  label: string;
  left?: ReactNode;
  content: ReactNode;
  isDisabled?: boolean;
  isActive?: boolean;
}

export type AccordionTheme =
  | SimpleTheme
  | 'display'
  | 'primaryInverted'
  | 'secondaryInverted'
  | 'displayInverted'
  | 'displayV1'
  | 'displayV1Inverted'
  | 'primaryMain';

interface AccordionProps {
  items: Array<AccordionItem>;
  initialActiveIndex?: number;
  theme: AccordionTheme;
  labelClassName?: string;
  itemClassName?: string;
  isBorderless?: boolean;
  buttonClassName?: string;
  contentClassName?: string;
  hasPlusMinusIcon?: boolean;
  right?: ReactNode;
  activeIndex?: number;
  onActiveIndexChange?: (index: number, isOpen: boolean) => void;
}

const Accordion = ({
  items,
  initialActiveIndex = null,
  theme = 'primaryMain',
  labelClassName = null,
  itemClassName = null,
  isBorderless = false,
  buttonClassName = null,
  contentClassName = null,
  hasPlusMinusIcon = true,
  right = null,
  activeIndex: propsActiveIndex = undefined,
  onActiveIndexChange = () => {},
}: AccordionProps) => {
  const [activeIndex, setActiveIndex] = useState(initialActiveIndex);

  useEffect(() => {
    setActiveIndex(propsActiveIndex);
  }, [propsActiveIndex]);

  const handleClick = (index: number) => () => {
    if (activeIndex === index) {
      setActiveIndex(null);
      onActiveIndexChange(-1, true);
    } else {
      setActiveIndex(index);
      onActiveIndexChange(index, true);
    }
  };

  return (
    <ul className={classNames([styles.list, styles[theme]])}>
      {items.map((item, index) => (
        <AccordionListItem
          key={item.label}
          item={item}
          theme={theme}
          onClick={handleClick(index)}
          isOpen={(propsActiveIndex ?? activeIndex) === index}
          labelClassName={labelClassName}
          itemClassName={itemClassName}
          isBorderless={isBorderless}
          buttonClassName={buttonClassName}
          contentClassName={contentClassName}
          hasPlusMinusIcon={hasPlusMinusIcon}
          right={right}
        />
      ))}
    </ul>
  );
};

export default Accordion;
