import React from 'react';
import { render } from '@testing-library/react';
import Button from '@components/Button'; // Import the Button component (for testing)
import { DETAILED_CTA_DATA_TEST_ID } from '@constants/dataTestId';

import DetailedCTA from './DetailedCTA'; // Import the DetailedCTA component

describe('DetailedCTA Component', () => {
  it('renders with required props', () => {
    const { getByText, getByTestId } = render(
      <DetailedCTA
        illustration={<div data-testid="illustration">Illustration</div>}
        heading={<div>No Offers</div>}
        body="Once you start the process of selling a car through Peddle, your Offers will show up here."
        action={<Button theme="warning" label="Get started" />}
      />,
    );
    expect(getByText('No Offers')).toBeInTheDocument();
    expect(
      getByText(
        'Once you start the process of selling a car through Peddle, your Offers will show up here.',
      ),
    ).toBeInTheDocument();
    expect(getByText('Get started')).toBeInTheDocument();
    expect(getByTestId('illustration')).toBeInTheDocument();
  });

  it('renders with optional props', () => {
    const { getByText, getByTestId } = render(
      <DetailedCTA
        illustration={<div data-testid="illustration">Illustration</div>}
        heading={<div>No Offers</div>}
        body="Once you start the process of selling a car through Peddle, your Offers will show up here."
        action={<Button theme="warning" label="Get started" />}
        size="small"
        position="top"
      />,
    );
    expect(getByText('No Offers')).toBeInTheDocument();
    expect(
      getByText(
        'Once you start the process of selling a car through Peddle, your Offers will show up here.',
      ),
    ).toBeInTheDocument();
    expect(getByText('Get started')).toBeInTheDocument();
    expect(getByTestId('illustration')).toBeInTheDocument();
    expect(getByTestId(DETAILED_CTA_DATA_TEST_ID)).not.toHaveStyle(
      'alignSelf: center',
    );
  });

  it('renders in center', () => {
    const { getByTestId } = render(
      <DetailedCTA
        illustration={<div data-testid="illustration">Illustration</div>}
        heading={<div>No Offers</div>}
        body="Once you start the process of selling a car through Peddle, your Offers will show up here."
        action={<Button theme="warning" label="Get started" />}
        size="small"
        position="center"
      />,
    );
    expect(getByTestId(DETAILED_CTA_DATA_TEST_ID)).toHaveStyle(
      'alignSelf: center',
    );
    expect(getByTestId(DETAILED_CTA_DATA_TEST_ID)).not.toHaveStyle(
      'alignSelf: top',
    );
  });
});
