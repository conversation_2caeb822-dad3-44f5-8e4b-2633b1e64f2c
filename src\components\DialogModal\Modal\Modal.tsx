// import type { FC } from 'react';
// import { forwardRef } from 'react';
// import { XCircleIcon } from '@peddleon/ped-ux-react-icons';
// import Button from '@components/Button';
// import classNames from '@utils/classNames';

// import styles from './Modal.module.css';
// import { ModalProps } from './Modal.types';

// const Modal: FC<ModalProps> = forwardRef<HTMLDivElement, ModalProps>(
//   (
//     {
//       children,
//       onClose = () => {},
//       hasCloseButton = true,
//       className = null,
//       isShadowless = false,
//       hasPadding = false,
//       hasDialogWrapperStyles = false,
//       isScrollable = false,
//       hasMaxHeight = false,
//     },
//     ref,
//   ) => (
//     <div
//       className={classNames(
//         styles.modal,
//         className,
//         isShadowless && styles.isShadowless,
//         hasPadding && styles.hasPadding,
//         isScrollable && styles.isScrollable,
//         hasMaxHeight && styles.hasMaxHeight,
//       )}
//       ref={ref}
//     >
//       {hasCloseButton && (
//         <div className={styles.closeIconButtonWrapper}>
//           <Button theme="transparent" onClick={onClose}>
//             <XCircleIcon width={24} height={24} />
//           </Button>
//         </div>
//       )}

//       <div
//         className={classNames(hasDialogWrapperStyles && styles.dialogWrapper)}
//       >
//         {children}
//       </div>
//     </div>
//   ),
// );

// export default Modal;
