declare module '*.module.css' {
  const content: Record<string, string>;
  export default content;
}

declare module 'csstype' {
  interface Properties {
    '--baseDialogModalMaxHeightMobile'?: string;
    // other custom properties
  }
}

declare module '*.png' {
  const value: string;
  export default value;
}

declare module '*.jpg' {
  const value: string;
  export default value;
}

declare module '*.jpeg' {
  const value: string;
  export default value;
}

declare module '*.svg' {
  const value: string;
  export default value;
}

declare module '*.ttf' {
  const value: string;
  export default value;
}

