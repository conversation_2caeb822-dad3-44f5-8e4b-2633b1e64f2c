import VCard from 'vcard-creator';
import { BusinessCardFormData } from '../components/BusinessCard/BusinessCard.types';

export const generateVCard = (formData: BusinessCardFormData): string => {
    const vcard = new VCard();
    vcard
        .addName(formData.lastName, formData.firstName)
        .addPhoneNumber(formData.phoneNumber)
        .addEmail(formData.email)
        .addCompany(formData.organization)
        .addJobtitle(formData.title);
    return vcard.toString();
};

export const convertSVGToPNG = async (svgElement: SVGElement): Promise<string> => {
    const svgString = new XMLSerializer().serializeToString(svgElement);
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Could not get canvas context');

    const img = new window.Image();
    canvas.width = 256;
    canvas.height = 256;

    const svgBlob = new Blob([svgString], {
        type: 'image/svg+xml;charset=utf-8',
    });
    const url = URL.createObjectURL(svgBlob);

    return new Promise((resolve, reject) => {
        img.onload = () => {
            ctx.drawImage(img, 0, 0);
            URL.revokeObjectURL(url);
            resolve(canvas.toDataURL('image/png'));
        };
        img.onerror = () => reject(new Error('Failed to load image'));
        img.src = url;
    });
};

export const downloadPDFFile = (pdfUrl: string, fileName: string): void => {
    const link = document.createElement('a');
    link.href = pdfUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();

    setTimeout(() => {
        document.body.removeChild(link);
        URL.revokeObjectURL(pdfUrl);
    }, 100);
}; 