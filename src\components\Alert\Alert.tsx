// import { useEffect, useState } from 'react';
// import { XCircleIcon } from '@peddleon/ped-ux-react-icons';
// import Button from '@components/Button';
// import { colorThemeMapping } from '@constants/common';
// import classNames from '@utils/classNames';
// import themeStyles from '@styles/themes.module.css';

// import styles from './Alert.module.css';
// import { AlertProps } from './Alert.types';

// /**
//  * The Alert component provides a customizable alert box for displaying messages or notifications to users.
//  *
//  * @param {Object} props - The props for the Alert component.
//  * @param {string} props.body - The main content body of the alert.
//  * @param {AlertTheme} props.theme - The theme of the alert. Choose from the predefined set of themes.
//  * @param {ReactNode} [props.left=null] - Optional. An optional ReactNode to be displayed on the left side of the alert body.
//  * @param {boolean} [props.isCancellable=false] - Optional. Specifies whether the alert can be cancelled by the user.
//  * @param {(e: SyntheticEvent<Element, Event>) => void} [props.onCancelButtonClick] - Optional. Callback function triggered when the cancel button is clicked.
//  * @param {boolean} [props.isAnimated=false] - Optional. Specifies whether the alert should have animated effects.
//  * @returns {JSX.Element} - The rendered Alert component.
//  */

// const Alert = ({
//   theme,
//   body,
//   left = null,
//   isCancellable = false,
//   onCancelButtonClick,
//   isAnimated = false,
// }: AlertProps) => {
//   const [startAnimation, setStartAnimation] = useState(false);
//   useEffect(() => {
//     setStartAnimation(true);
//   }, [isAnimated]);
//   return (
//     <div
//       className={classNames(
//         styles.alert,
//         isCancellable &&
//           classNames(styles.isCancellable, styles.alignIconRight),
//         startAnimation && styles.animateColorLeftToRight,
//         themeStyles[`${theme}Theme`],
//       )}
//       style={{
//         '--animationThemeColorDark': colorThemeMapping[theme]?.dark,
//         '--animationThemeColor': colorThemeMapping[theme]?.light,
//         '--alertThemeBorderColor': colorThemeMapping[theme]?.border,
//       }}
//     >
//       <div
//         className={classNames(
//           styles.alertContent,
//           isCancellable && styles.oneSpacePadding,
//         )}
//       >
//         {left && <div className={styles.iconWrapper}>{left}</div>}
//         {body}
//       </div>
//       {isCancellable && (
//         <Button
//           className={styles.cancelButton}
//           onClick={onCancelButtonClick}
//           theme={theme}
//           size="xSmall"
//           id="cancel-button"
//           type="button"
//         >
//           <XCircleIcon height={24} width={24} />
//         </Button>
//       )}
//     </div>
//   );
// };

// export default Alert;
