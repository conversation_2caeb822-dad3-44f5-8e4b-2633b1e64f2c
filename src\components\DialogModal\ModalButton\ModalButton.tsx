import { useCallback } from 'react';
import Button from '@components/Button';
import classNames from '@utils/classNames';

import styles from './ModalButton.module.css';
import { ModalButtonProps } from './ModalButton.types';

const ModalButton = ({
  isFullWidthButton = true,
  buttonSize = 'large',
  onClick = () => {},
  buttonRight = null,
  buttonLeft = null,
  isDisabled = false,
  theme = 'warning',
  label = '',
  modalButtonActionType = '',
  isLoading = false,
  wrapperClassName = null,
  tracker = () => {},
  modalName = '',
}: ModalButtonProps) => {
  const buttonId = label ? label.toLowerCase().split(' ').join('-') : `button`;

  const onClickHandler = useCallback(
    event => {
      tracker({ modalName, modalActionType: modalButtonActionType });
      onClick(event);
    },
    [modalButtonActionType, modalName, onClick, tracker],
  );

  return (
    <div
      className={classNames(
        styles.modalButtonWrapper,
        isFullWidthButton && styles.isFullWidthButton,
        wrapperClassName,
      )}
    >
      <Button
        id={buttonId}
        label={label}
        theme={theme}
        size={buttonSize}
        onClick={onClickHandler}
        right={buttonRight}
        left={buttonLeft}
        isLoading={isLoading}
        isDisabled={isDisabled}
        isFullWidth={isFullWidthButton}
      />
    </div>
  );
};

export default ModalButton;
