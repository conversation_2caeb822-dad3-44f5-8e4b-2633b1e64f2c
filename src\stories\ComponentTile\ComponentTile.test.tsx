import { render } from '@testing-library/react';

import ComponentTile from './ComponentTile';

describe('ComponentTile component', () => {
  it('renders children correctly', () => {
    const { getByTestId } = render(
      <ComponentTile>
        <div data-testid="child">Child Component</div>
      </ComponentTile>,
    );

    expect(getByTestId('child')).toBeInTheDocument();
    expect(getByTestId('child')).toHaveTextContent('Child Component');
  });

  it('renders the label correctly', () => {
    const { getByText } = render(
      <ComponentTile label="Test Label">test</ComponentTile>,
    );
    expect(getByText('Test Label')).toBeInTheDocument();
  });
});
