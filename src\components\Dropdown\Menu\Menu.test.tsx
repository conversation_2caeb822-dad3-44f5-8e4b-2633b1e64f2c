import { render } from '@testing-library/react';
import { Menu as SelectInputMenu } from '@components/Dropdown';

describe('SelectInputMenu', () => {
  it('renders menu correctly', () => {
    const { getByText } = render(
      <SelectInputMenu isOpen>
        <li>Menu Item 1</li>
        <li>Menu Item 2</li>
      </SelectInputMenu>,
    );
    expect(getByText('Menu Item 1')).toBeInTheDocument();
    expect(getByText('Menu Item 2')).toBeInTheDocument();
  });

  it('applies custom classNames correctly', () => {
    const { container } = render(
      <SelectInputMenu
        isOpen
        className="custom-class"
        listClassName="custom-list"
      >
        <li>Menu Item 1</li>
        <li>Menu Item 2</li>
      </SelectInputMenu>,
    );
    const menu = container.firstChild;
    expect(menu).toHaveClass('custom-class');
  });

  // Add more test cases as needed for other functionalities
});
