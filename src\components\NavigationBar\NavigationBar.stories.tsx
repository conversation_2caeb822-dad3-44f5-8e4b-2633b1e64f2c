// import {
//   Car3Icon,
//   ChatIcon,
//   HelpCircleIcon,
//   SettingsIcon,
//   UserIcon,
// } from '@peddleon/ped-ux-react-icons';
import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Button } from '..';
import NavigationBar from './NavigationBar';
/**
 * ## Overview
 *
 * The `NavigationBar` component provides a navigation bar for the application, facilitating navigation between different sections or pages. It supports responsive design, allowing for seamless navigation on various devices.
 *
 * ## Usage
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { NavigationBar } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * ## Props
 *
 * | Prop             | Type                    | Default   | Description                                                                  |
 * |------------------|-------------------------|-----------|------------------------------------------------------------------------------|
 * | `leftNavItems`   | Array of `NavItem`      | Required  | An array of objects representing the items displayed on the left side of the navigation bar. See below for `NavItem` structure.  |
 * | `rightNavItems`  | Array of `NavItem`      | Required  | An array of objects representing the items displayed on the right side of the navigation bar. See below for `NavItem` structure. |
 * | `isMenuOpen`     | boolean                 | false     | Indicates whether the mobile navigation menu is open.                        |
 * | `logoUrl`        | string                  | ''        | URL for the logo image displayed in the navigation bar.                      |
 * | `isStickOnTop`   | boolean                 | false     | Indicates whether the navigation bar sticks to the top of the screen.        |
 * | `mobileNavButton`| Array of `ButtonProps`  | []        | An array of objects representing the buttons displayed in the mobile navigation menu. See below for `ButtonProps` structure. |
 * | `logo`           | ReactNode               | null      | Custom logo element to be displayed in the navigation bar.                   |
 *
 * ## NavItem Structure
 *
 *  label: string;
 *  icon?: ReactNode;
 *  isActive?: boolean;
 *  link?: string;
 *  listItems?: NavItem[];
 *  theme?: ButtonTheme;
 *  id?: string;
 *
 * | Key              | Type          | Description                                                                 |
 * |------------------|---------------|-----------------------------------------------------------------------------|
 * | `label`          | string        | The label/text for the navigation item.                                     |
 * | `isActive`       | boolean       | (Optional) Indicates whether the navigation item is active.                 |
 * | `link`           | string        | (Optional) URL to navigate when the item is clicked.                        |
 * | `isSubList`      | boolean       | (Optional) Indicates whether the item has a sub-menu.                       |
 * | `leftIcon`       | ReactNode     | (Optional) Icon or element to be displayed on the left side of the item.     |
 * | `rightIcon`      | ReactNode     | (Optional) Icon or element to be displayed on the right side of the item.    |
 * | `onClick`        | () => void    | (Optional) Callback function to be called when the item is clicked.          |
 * | `onLeftIconClick`| () => void    | (Optional) Callback function to be called when the left icon is clicked.     |
 * | `onRightIconClick`| () => void   | (Optional) Callback function to be called when the right icon is clicked.    |
 * | `className`      | string        | (Optional) Additional CSS class names for styling the navigation item.        |
 * | `labelClassName` | string        | (Optional) Additional CSS class names for styling the label/text of the item.|
 *
 * ## ButtonProps Structure
 *
 * | Key           | Type           | Description                                                                 |
 * |---------------|----------------|-----------------------------------------------------------------------------|
 * | `label`       | string         | The label/text for the button.                                             |
 * | `icon`        | ReactNode      | (Optional) Icon or element to be displayed on the button.                   |
 * | `theme`       | string         | (Optional) Theme or style for the button.                                   |
 * | `onClick`     | () => void     | (Optional) Callback function to be called when the button is clicked.       |
 * | `isFullWidth` | boolean        | (Optional) Indicates whether the button should span the full width.         |
 * | `size`        | string         | (Optional) Size of the button.                                             |
 * | `component`   | string or any | (Optional) The type of component to render for the button (e.g., 'a', 'button'). |
 *
 */
const meta: Meta<typeof NavigationBar> = {
  component: NavigationBar,
  title: 'Components/NavigationBar',
  tags: ['autodocs'],
};

type Story = StoryObj<typeof NavigationBar>;

export const Basic: Story = {
  args: {
    logo: '',
    logoUrl: '/',
    activeItemTheme: 'darkInverted',
    leftNavItems: [
      {
        isActive: true,
        label: 'Dashboard',
        id: 'Dashboard',
        link: '/',
      },
      {
        label: 'Leads',
        id: 'Leads',
        link: '/leads',
        theme: 'transparentInverted',
      },
      {
        label: 'Payments',
        id: 'Payments',
        theme: 'transparentInverted',
        listItems: [
          {
            label: 'Open invoices',
            link: 'open-invoices',
            id: 'Open invoices',
          },
          {
            label: 'Payment history',
            link: 'payment-history',
            id: 'Payment history',
          },
        ],
      },
      {
        label: 'Analytics',
        theme: 'transparentInverted',
        id: 'Analytics',
        listItems: [
          {
            label: 'Leads',
            link: 'open-invoices',
            id: 'Leads',
          },
          {
            label: 'Leads - Custom',
            id: 'Leads - Custom',
            link: 'open-invoices',
          },
          {
            label: 'Accepted offers',
            id: 'Accepted offers',
            link: 'open-invoices',
          },
          {
            label: 'Accepted offers - Custom',
            id: 'Accepted offers - Custom',
            link: 'open-invoices',
          },
          {
            label: 'Completed offers',
            id: 'Completed offers',
            link: 'open-invoices',
          },
          {
            label: 'Commission',
            id: 'Commission',
            link: 'open-invoices',
          },
          {
            label: 'Commission - Custom',
            id: 'Commission - Custom',
            link: 'open-invoices',
          },
          {
            label: 'Payments',
            id: 'Payments',
            link: 'open-invoices',
          },
        ],
      },
    ],
    rightNavItems: [
      // {
      //   icon: <HelpCircleIcon height="20px" width="20px" />,
      //   label: '',
      //   link: '/',
      //   theme: 'greyInverted',
      //   id: 'API help',
      //   listItems: [
      //     {
      //       label: 'API help',
      //       link: '/test',
      //       id: 'API help',
      //     },
      //     {
      //       label: 'Support center',
      //       id: 'Support center',
      //       link: '/support',
      //     },
      //   ],
      // },
      // {
      //   icon: <SettingsIcon width="20px" height="20px" />,
      //   link: '/',
      //   label: '',
      //   theme: 'greyInverted',
      //   id: 'Manage Users',
      //   listItems: [
      //     {
      //       label: 'Manage Users',
      //       id: 'Manage Users',
      //       link: '/test',
      //     },
      //     {
      //       label: 'Charity profile',
      //       id: 'Charity profile',
      //       link: '/support',
      //     },
      //   ],
      // },
      // {
      //   icon: <UserIcon width="20px" height="20px" />,
      //   label: 'Rohan Vachheta',
      //   id: 'Rohan Vachheta',
      //   link: '/users',
      //   theme: 'greyInverted',
      // },
      {
        label: 'Get Offer',
        id: 'Get Offer',
        link: '/profile',
        theme: 'lightInverted',
      },
    ],
    mobileNavButton: [
      // {
      //   icon: <ChatIcon width={24} height={24} />,
      //   theme: 'greyInverted',
      //   id: 'greyInverted',
      // },
      // {
      //   icon: <Car3Icon height={24} width={24} />,
      //   theme: 'greyInverted',
      //   id: 'Car3Icon',
      // },
    ],
    mobileMenuButtons: (
      <>
        <Button
          id="get-offer-button"
          isFullWidth
          size="small"
          theme="warning"
          label="Get an offer"
          component="a"
        />
        <div
          style={{
            marginTop: '16px',
          }}
        />
        <Button
          id="get-offer-button"
          size="small"
          isFullWidth
          theme="grey"
          label="Test Button"
          right={<div />}
          component="a"
        />
      </>
    ),
    onNavigationClick: () => {},
  },
};

export default meta;
