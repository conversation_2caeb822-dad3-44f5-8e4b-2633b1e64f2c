import { ChangeEvent, useState } from 'react';
import { render, screen } from '@testing-library/react';
// eslint-disable-next-line import/no-extraneous-dependencies
import userEvent from '@testing-library/user-event';
import { TEXT_INPUT_DATA_TEST_ID } from '@constants/dataTestId';

import TextInput from './TextInput';

// Mocking the scrollIntoView function
window.HTMLElement.prototype.scrollIntoView = function mock () {};

// Mocking the useDebouncedCallback hook
jest.mock('@hooks/useDebouncedCallback', () => callback => callback);

// Component wrapper for testing the TextInput component
const TextInputWrapper = ({
  value = '',
  label = 'Label',
  placeholder = 'Placeholder',
  ...props
}) => {
  const [state, setState] = useState(value);
  return (
    <TextInput
      label={label}
      placeholder={placeholder}
      value={state}
      onChange={(event: ChangeEvent<HTMLInputElement>) =>
        setState(event.target.value)
      }
      {...props}
    />
  );
};

describe('TextInput Component', () => {
  it('should render the text input with default placeholder, label, value, etc.', async () => {
    // Render the component with some props
    render(<TextInputWrapper />);

    // Expect the placeholder and label to be in the document
    expect(screen.getByPlaceholderText('Placeholder')).toBeInTheDocument();
    expect(screen.getByText('Label')).toBeInTheDocument();

    // Expect the input to be visible in the browser
    expect(screen.getByTestId(TEXT_INPUT_DATA_TEST_ID)).toBeVisible();
  });

  it('renders with a preselected value', async () => {
    // Render the component with a preselected value
    render(<TextInputWrapper value="peddle" />);

    const input = screen.getByTestId(TEXT_INPUT_DATA_TEST_ID);

    // Expect the input to have 'peddle' value set by default
    expect(input).toHaveValue('peddle');
  });

  it('should trigger validation', async () => {
    // Render the component with a maximum length and error message
    const errorMessage = 'Input must have a maximum of 10 characters';
    render(<TextInputWrapper maxLength={10} error={errorMessage} />);

    const input = screen.getByTestId(TEXT_INPUT_DATA_TEST_ID);

    // Type 'The SelectInput component' which exceeds the maximum length
    await userEvent.type(input, 'The SelectInput component');

    // Expect the error message to be displayed with the 'error' class
    expect(screen.getByText(errorMessage)).toHaveClass('error');
  });
});
