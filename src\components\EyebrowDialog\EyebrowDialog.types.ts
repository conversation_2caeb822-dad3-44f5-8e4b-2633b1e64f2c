import { ReactNode } from 'react';
import { ButtonTheme } from '@components/Button/Button.types';
import { ModalTracker } from '@libtypes/modal';

interface EyebrowDialogConfig {
  maxWidth?: number | string;
  isActive: boolean;
  modalAcceptActionType?: string;
  modalDeclineActionType?: string;
  hasFullWidthButtons?: boolean;
  isAcceptLoading?: boolean;
  isDeclineLoading?: boolean;
  declineButtonTheme?: ButtonTheme;
  acceptButtonTheme?: ButtonTheme;
  modalName?: string;
  acceptButtonDisabled?: boolean;
  declineButtonDisabled?: boolean;
  isAnimated?: boolean;
  hasCloseButton?: boolean;
  maxHeightMobile?: number | string;
  hasDialogWrapperStyles?: boolean;
  isMobileCentered?: boolean;
  isHeadingStatic?: boolean;
  hasNoPaddingAroundForBody?: boolean;
  maxBodyHeight?: string | number;
}
interface EyebrowDialogContent {
  illustrationContent?: ReactNode;
  preHeadingContent?: ReactNode;
  headingContent?: ReactNode;
  postHeadingContent?: ReactNode;
  bodyContent?: ReactNode;
  acceptLabel?: string;
  acceptButtonRight?: ReactNode;
  declineLabel?: string;
  declineButtonRight?: ReactNode;
  declineButtonLeft?: ReactNode;
  acceptButtonLeft?: ReactNode;
  headerwrapperContent?: ReactNode;
}
interface EyebrowDialogActions {
  onClose?: () => void;
  onAccept?: () => void;
  onDecline?: () => void;
  trackModalAction?: ModalTracker;
}

interface EyebrowDialogClassNames {
  dialogWrapperClassName?: string;
  headerWrapperClassName?: string;
  headerWrapperContainerClassName?: string;
  bodyWrapperClassName?: string;
  bodyWrapperContainerScrollClassName?: string;
}

export interface EyebrowDialogTypes {
  /**
   *  modalConfig prop is a object used to define configuration and basic setting for the modal.
   *
   * --*-- Object's properties description --*--
   *
   * - `maxWidth`: To set the maximum width of the dialog wrapper for mobile devices.
   *
   * - `maxBodyHeight`: To set the maximum height for the body content.
   *
   * - `isActive`: To prevent animation flickering, we set a default opacity to zero using this prop.
   *
   * - `modalAcceptActionType`: Type of tracking while something gets accepted by the accept button.
   *
   * - `modalDeclineActionType`: Type of tracking while something gets declined by the decline button.
   *
   * - `hasFullWidthButtons`: To set buttons in full width respective to modal max width.
   *
   * - `isAcceptLoading`: To set loader while something is happening on accepting.
   *
   * - `isDeclineLoading`: To set loader while something is happening on declining.
   *
   * - `declineButtonTheme`: To set the theme of the decline button.
   *
   * - `acceptButtonTheme`: To set the theme of the accept button.
   *
   * - `modalName`: To set the modal name which later can be used to track the modal action.
   *
   * - `acceptButtonDisabled`: To disable the accept button.
   *
   * - `declineButtonDisabled`: To disable the decline button.
   *
   * - `isAnimated`: To prevent animation flickering, we set a default opacity to zero using this prop.
   *
   * - `hasCloseButton`: To hide/show the close icon button on the top right of the modal.
   *
   * - `maxHeightMobile`: To set the maximum height of the dialog wrapper for mobile devices.
   *
   * - `hasDialogWrapperStyles`: This property can be set to provide external CSS to a wrapper.
   *
   * - `isMobileCentered` : To center the modal on the device when it's opened on a mobile screen.
   *
   * - `isHeadingStatic` : To set heading staticlly at top of the modal with cross icon.
   *
   *
   */
  modalConfig: EyebrowDialogConfig;
  /**
   * modalContent prop is an object used to set modal content such as headings, button labels...
   *
   * --*-- Object's properties description --*--
   *
   * - `illustrationContent`: Used to set illustration in the modal.
   *
   * - `preHeadingContent`: Used to set pre-heading above the heading or can say eyebrow text.
   *
   * - `headingContent`: Used to set the main heading for the modal.
   *
   * - `postHeadingContent`: Post-heading will be displayed below the heading.
   *
   * - `bodyContent`: Body content will be displayed as part of the body of the modal representing large content or statements.
   *
   * - `acceptLabel`: Label will be set for the accept button.
   *
   * - `acceptButtonRight`: Any icon or element can be set right to the label of the accept button.
   *
   * - `declineLabel`: Label will be set for the decline button.
   *
   * - `declineButtonRight`: Any icon or element can be set right to the label of the decline button.
   *
   * - `acceptButtonLeft` : Any icon or element can be set left to the label of the accept button.
   *
   * - `declineButtonLeft` : Any icon or element can be set left to the label of the decline button.
   *
   */
  modalContent?: EyebrowDialogContent;
  /**
   * modalActions prop is an object used to set modal actions or we can say action handler.
   *
   * --*-- Object's properties description --*--
   *
   * - `onClose`: Triggered whenever the modal gets closed; once when someone clicks on the close icon and another when clicked on the backdrop of the modal.
   *
   * - `onAccept`: Triggered whenever the accept button gets clicked.
   *
   * - `onDecline`: Triggered whenever the decline button gets clicked.
   *
   * - `trackModalAction`: Triggered whenever any kind of modal action happens, such as view, accept, decline, etc.
   *
   */
  modalActions?: EyebrowDialogActions;
  /**
   * modalClassNames prop containing class names for styling various parts of the modal.
   *
   * --*-- Object's properties description --*--
   *
   * - `dialogWrapperClassName`: Class name for the dialog wrapper.
   *
   * - `headerWrapperClassName`: Class name for the header wrapper.
   *
   * - `headerWrapperContainerClassName`: Class name for the container of the header wrapper.
   *
   * - `bodyWrapperClassName`: Class name for the body wrapper.
   *
   * - `bodyWrapperContainerScrollClassName`: Class name for the scroll container of the body wrapper.
   */
  modalClassNames?: EyebrowDialogClassNames;
}
