import { render } from '@testing-library/react';

import AdminFrame from './AdminFrame';

describe('AdminFrame component', () => {
  it('renders with default props', () => {
    const { getAllByTestId } = render(<AdminFrame />);
    const labels = getAllByTestId('frame-label');
    expect(labels.length).toBeGreaterThan(0);
    expect(labels[0]).toHaveTextContent('Editing');
  });

  it('renders with custom label', () => {
    const { getAllByTestId } = render(<AdminFrame label="Custom Label" />);
    const labels = getAllByTestId('frame-label');
    expect(labels.length).toBeGreaterThan(0);
    expect(labels[0]).toHaveTextContent('Custom Label');
  });
});
