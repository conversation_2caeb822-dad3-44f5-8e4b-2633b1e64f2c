/* eslint-disable react/jsx-no-useless-fragment */
import { FC } from 'react';

import classNames from '../../utils/classNames';
import styles from './PageLayout.module.css';
import { PageLayoutProps } from './PageLayout.types';

const PageLayout: FC<PageLayoutProps> = ({
  children,
  backgroundColor,
  isOverflowHidden,
  hasBackgroundTransition,
  sidebar,
  footer,
  hasLeftRightPadding = true,
}) => (
  <>
    {sidebar || footer ? (
      <div
        className={classNames(
          styles.page,
          hasLeftRightPadding && styles.leftRightPadding,
        )}
      >
        {sidebar && (
          <aside className={styles.sidebar}>
            <div className={styles.sidebarContainerWrapper}>{sidebar}</div>
          </aside>
        )}
        <main className={styles.main}>
          <div className={styles.childrenWrapper}>
            {children}
            {footer && (
              <div className={styles.footerWrapper}>
                <div className={styles.footerPortalRoot}>{footer}</div>
              </div>
            )}
          </div>
        </main>
      </div>
    ) : (
      <div
        className={classNames(
          styles.layout,
          hasBackgroundTransition && styles.backgroundTransition,
          isOverflowHidden && styles.isOverflowHidden,
        )}
        style={{
          backgroundColor,
        }}
      >
        <div className={styles.childrenWrapper}>{children}</div>
      </div>
    )}
  </>
);

export default PageLayout;
