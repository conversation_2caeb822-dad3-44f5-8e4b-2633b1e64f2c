import { Font } from '@react-pdf/renderer';
import AventaRegular from '../assets/fonts/Aventa-Regular.ttf';
import AventaMedium from '../assets/fonts/Aventa-Bold.ttf';
import AventaBold from '../assets/fonts/Aventa-Extra-Bold.ttf';
import AventaBlack from '../assets/fonts/Aventa-Black.ttf';

let fontsRegistered = false;

export const registerFonts = async () => {
    if (fontsRegistered) {
        return;
    }

    try {
        const fonts = [
            {
                src: AventaRegular,
                fontWeight: 400 as const,
                fontStyle: 'normal' as const,
            },
            {
                src: AventaMedium,
                fontWeight: 500 as const,
                fontStyle: 'normal' as const,
            },
            {
                src: AventaBold,
                fontWeight: 700 as const,
                fontStyle: 'normal' as const,
            },
            {
                src: AventaBlack,
                fontWeight: 900 as const,
                fontStyle: 'normal' as const,
            },
        ];

        Font.register({
            family: 'Aventa',
            fonts,
        });

        fontsRegistered = true;
        console.log('Fonts registered successfully');
    } catch (error) {
        console.error('Error registering fonts:', error);
        throw error;
    }
};
