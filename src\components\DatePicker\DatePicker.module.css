.dayButton {
  color: black;
  height: 44px;
  width: 44px;

  &.dayButtonToday:not(.dayButtonSelected):not(.dayButtonRangeStartEnd) {
    color: primary;
  }
}

.dayButtonFocused {
  border: 2px solid primary;
}

.dayButtonSelected {
  border: none !important;
  background-color: primary !important;
  color: white !important;
}

.dayButtonMiddleSelected {
  color: black !important;
  background-color: primaryFade !important;

  &.dayButtonRangeStartEnd {
    border: none !important;
    background-color: primary !important;
    color: white !important;
  }

  &.dayButtonToday:not(.dayButtonSelected) {
    color: primary !important;
  }
}

.weekday {
  height: 48px;
  font-family: Aventa;
  font-size: captionFontSize;
  font-weight: 700;
  line-height: 20px;
  letter-spacing: 0.01em;
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: neutralDarkest;
  opacity: 0.5;
}

.footerWrapper {
  width: 100%;
  padding: halfSpace;
  display: flex;
  justify-content: center;
  align-items: center;
}
