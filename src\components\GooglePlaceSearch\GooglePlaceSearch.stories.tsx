/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/naming-convention */
import React, { useState } from 'react';
import { Meta } from '@storybook/react';
import useAddressSuggestions from '@hooks/useAddressSuggestion';

import GooglePlaceSearch from './GooglePlaceSearch';
/**
 * ### Overview
 *
 * The `GooglePlaceSearch` component provides an input field for searching Google Places.
 * It utilizes Google's Places API for autocomplete suggestions, enhancing the user experience with relevant location-based data.
 *
 *
 * ### Usage
 *
 * #### Importing the Component
 *
 * To use the `GooglePlaceSearch` component in your React application, import it from the `@peddleon/ped-ux-react-library`:
 *
 * ```jsx
 * import { GooglePlaceSearch } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * #### Implementing the Component
 *
 * Include the `GooglePlaceSearch` component in your JSX with the desired props. Here’s a basic example:
 *
 * ```jsx
 * <GooglePlaceSearch
 *   id="streetID"
 *   inputValue="" // initial input field value
 *   items={[]} // items to be displayed as menu list
 *   label="Street Address"
 *   onBlur={() => {}}
 *   onChange={() => {}}
 *   onFocus={() => {}}
 *   onInputValueChange={() => {}} // on input/search value change
 *   onInvalid={() => {}} // on menu item gets selected
 *   required
 *   value={null} // selected menu item value
 * />
 * ```
 *
 * This example will render a `GooglePlaceSearch` component with the specified default values.
 *
 * ### Extending Functionality
 *
 * #### Using Address Suggestions
 *
 * This component allows users to provide the `useAddressSuggestions` hook externally to fetch address suggestions. By importing and using this hook, you can extend the behavior of the `GooglePlaceSearch` component to retrieve initial data from the Google Places API.
 *
 * Here's how you can use the `useAddressSuggestions` hook:
 *
 * ```jsx
 * const { suggestedAddresses } = useAddressSuggestions({
 *   address,
 *   street,
 *   ZIPCode,
 * });
 * ```
 *
 * #### Notes
 *
 * - Ensure you have provided the necessary information for the `useAddressSuggestions` hook to function correctly. This hook will utilize the `address`, `street`, and `ZIPCode` props to fetch relevant suggestions from the Google Places API.
 * - Check your console for more information about any errors that arise from the Google Places API, such as "geocoded not found" or "exceed maximum retrieve limit".
 */

const meta: Meta<typeof GooglePlaceSearch> = {
  title: 'Components/GooglePlaceSearch',
  tags: ['autodocs'],
  component: GooglePlaceSearch,
};

export const GooglePlaceSearchStory = ({
  ZIPCode = '10001',
  googlePlacesAPIKey = '',
}) => {
  const [address, setAddress] = useState(null);
  const [street, setStreet] = useState('');

  // Fetch address suggestions using useAddressSuggestions hook
  const { suggestedAddresses } = useAddressSuggestions({
    address,
    street,
    ZIPCode,
  });

  return (
    <div
      style={{
        width: '500px',
        height: '450px',
      }}
    >
      <GooglePlaceSearch
        id="streetID"
        label="Street Address"
        items={suggestedAddresses}
        inputValue={street}
        value={address}
        onChange={val => {
          setAddress(val);
        }}
        onInputValueChange={({ inputValue }) => {
          setStreet(inputValue);
        }}
        required
        onInvalid={() => {
          console.log('On Invalid');
        }}
        onFocus={() => console.log('On Focus')}
        onBlur={() => console.log('On Blur')}
        googlePlacesAPIKey={googlePlacesAPIKey}
      />
    </div>
  );
};

GooglePlaceSearchStory.args = {
  googlePlacesAPIKey: null,
};

export default meta;
