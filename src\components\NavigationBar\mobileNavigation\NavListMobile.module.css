@value variables: "../../../styles/variables.module.css";
@value oneSpace, threeSpace, twoSpace, white, neutralDarkest, neutralDarker, black10, smallWidth from variables;
@value typography: "../../../styles/typography.module.css";
@value bodyLarge, bodyBaseStrong from typography;
@value utility: "../../../styles/utility.module.css";
@value responsivePageGutters from utility;

.navMenuWrapper {
  padding: twoSpace;
}

.navListItemBack {
  composes: bodyBaseStrong;
  padding: oneSpace oneSpace oneSpace 0 !important;
  list-style: none;
  text-transform: uppercase;
  color: neutralDarker;
  display: flex;
  align-items: center;
}

.menuButtonContainer {
  border-top: 1px solid black10;
  padding: threeSpace 0;
}
