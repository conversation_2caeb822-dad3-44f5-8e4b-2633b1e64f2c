import { useState } from 'react';
import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta } from '@storybook/react';

import { Button } from '..';
import ErrorBoundryComponent from './ErrorBoundary';

/**
 * ## Overview
 *
 * The `ErrorBoundaryComponent` is designed to catch JavaScript errors anywhere in the component tree, log those errors, and display a fallback UI. This helps prevent the entire application from crashing due to errors in a specific part of the UI.
 *
 * ## Usage
 *
 * To use the `ErrorBoundaryComponent`, follow these steps:
 *
 * 1. Import the component into your React application:
 *
 *    ```jsx
 *    import { ErrorBoundary } from '@peddleon/ped-ux-react-library';
 *    ```
 *
 * 2. Wrap your component tree with the `ErrorBoundary` component. You can provide a function to the `registerError` prop to handle errors:
 *
 *    ```jsx
 *    <ErrorBoundary registerError={(error) => { console.log(error); }} />
 *    ```
 * */
const meta: Meta<typeof ErrorBoundryComponent> = {
  component: ErrorBoundryComponent,
  title: 'Components/ErrorBoundry',
  tags: ['autodocs'],
  parameters: {
    docs: {
      toc: true,
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
};

const Demo = () => {
  const [showError, setShowError] = useState(false);

  // eslint-disable-next-line react/jsx-no-useless-fragment
  if (showError) return <>{new Error('')}</>;
  return (
    <div className="p-4">
      <Button
        onClick={() => {
          setShowError(true);
        }}
        type="button"
        label="Click here to see Error boundry component"
      />
    </div>
  );
};

export const ErrorBoundry = () => (
  <ErrorBoundryComponent>
    <Demo />
  </ErrorBoundryComponent>
);

export default meta;
