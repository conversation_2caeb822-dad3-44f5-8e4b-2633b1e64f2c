@value variables: "variables.module.css";
@value black, blackTrue, black5, black10, black70, black90, black95 from variables;
@value white, white5, white10, white70, neutralLighter from variables;
@value primary, primaryDark, primaryFade, primary5, primary10 from variables;
@value danger, dangerDark, dangerFade, danger5, danger10 from variables;
@value warning, warningDark, warningFade, warning5, warning10 from variables;
@value success, success5, success10, successDark from variables;

.darkTheme {
  background-color: black;
  color: white;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: blackTrue;
    }
  }
  &:active {
    background-color: blackTrue;
  }
}

.darkInvertedTheme {
  background-color: white;
  color: black;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: neutralLighter;
    }
  }
  &:active {
    background-color: neutralLighter;
  }
}

.lightTheme {
  border-width: 1px;
  border-style: solid;
  border-color: black10;
  color: black;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: black5;
    }
  }
  &:active {
    background-color: black5;
  }
}

.lightInvertedTheme {
  background-color: black;
  border-width: 2px;
  border-style: solid;
  border-color: white10;
  color: white;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: white5;
    }
  }
  &:active {
    background-color: white5;
  }
}

.greyTheme {
  background-color: black5;
  color: black;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: black10;
    }
  }
  &:active {
    background-color: black10;
  }
}

.greyInvertedTheme {
  background-color: white5;
  color: white;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: white10;
    }
  }
  &:active {
    background-color: white10;
  }
}

.transparentTheme {
  background-color: transparent;
  color: black70;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      color: black;
    }
  }
  &:active {
    color: black;
  }
}

.transparentInvertedTheme {
  background-color: transparent;
  color: white;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      color: white70;
    }
  }
  &:active {
    color: white70;
  }
}

.primaryTheme {
  background-color: primary;
  color: white;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: primaryDark;
    }
  }
  &:active {
    background-color: primaryDark;
  }
}

.primaryFadedTheme {
  background-color: primary5;
  color: primary;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: primary10;
    }
  }
  &:active {
    background-color: primary10;
  }
}

.primaryInvertedTheme {
  background-color: white;
  color: primary;
  border-width: 1px;
  border-style: solid;
  border-color: inherit;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      color: primaryDark;
    }
  }
  &:active {
    color: primaryDark;
  }

  &:disabled {
    color: primaryDark !important;
    border-style: solid !important;
    border-color: inherit !important;
  }
}

.dangerTheme {
  background-color: danger;
  color: white;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: dangerDark;
    }
  }
  &:active {
    background-color: dangerDark;
  }
}

.dangerFadedTheme {
  background-color: danger5;
  color: danger;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: danger10;
    }
  }
  &:active {
    background-color: danger10;
  }
}

.dangerInvertedTheme {
  background-color: white;
  color: danger;
  border-width: 1px;
  border-style: solid;
  border-color: inherit;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      color: dangerDark;
    }
  }
  &:active {
    color: dangerDark;
  }

  &:disabled {
    color: danger !important;
    border-style: solid !important;
    border-color: inherit !important;
  }
}

.warningTheme {
  background-color: warning;
  color: black;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: warningDark;
    }
  }
  &:active {
    background-color: warningDark;
  }
}

.warningInvertedTheme {
  background-color: white;
  color: warning;
  border-width: 1px;
  border-style: solid;
  border-color: inherit;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      color: warningDark;
    }
  }
  &:active {
    color: warningDark;
  }

  &:disabled {
    color: warning !important;
    border-style: solid !important;
    border-color: inherit !important;
  }
}

.warningFadedTheme {
  background-color: warning5;
  color: warning;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: warning10;
    }
  }
  &:active {
    background-color: warning10;
  }
}

.successTheme {
  background-color: success;
  color: white;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: successDark;
    }
  }

  &:active {
    background-color: successDark;
  }
}

.successInvertedTheme {
  background-color: white;
  color: success;
  border-width: 1px;
  border-style: solid;
  border-color: inherit;

  &:disabled {
    color: success !important;
    border-style: solid !important;
    border-color: inherit !important;
  }
}

.successFadedTheme {
  background-color: success5;
  color: success;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      background-color: success10;
    }
  }
  &:active {
    background-color: success10;
  }
}

.outlineTheme {
  background-color: transparent;
  border-width: 2px;
  border-style: solid;
  border-color: black;
  color: black;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      color: black70;
      border-color: black70;
    }
  }
  &:active {
    color: black70;
    border-color: black70;
  }
}

.outlineInvertedTheme {
  background-color: transparent;
  border-width: 2px;
  border-style: solid;
  border-color: white;
  color: white;

  @media (hover: hover) and (pointer: fine) {
    &:hover {
      color: white70;
    }
  }
  &:active {
    color: white70;
  }
}
