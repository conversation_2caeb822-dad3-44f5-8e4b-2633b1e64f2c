// import React, { forwardRef } from 'react';
// import { SearchIcon, XIcon } from '@peddleon/ped-ux-react-icons';
// import { ButtonSize } from '@components/Button/Button.types';

// import { Button, TextInput } from '..';
// import styles from './SearchInput.module.css';

// interface SearchInputProps {
//   id?: string;
//   isOpen: boolean;
//   onOpen: (isOpen: boolean) => void;
//   value?: string;
//   onChange: (value: string) => void;
//   onSearch: (value: string) => void;
//   searchIconColor?: string;
//   maxLength?: number;
//   btnSize?: ButtonSize;
//   hideClearButton?: boolean;
// }

// const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(
//   (
//     {
//       id = null,
//       isOpen,
//       onOpen,
//       value = '',
//       onChange,
//       onSearch,
//       searchIconColor = 'currentColor',
//       maxLength = null,
//       btnSize = 'small',
//       hideClearButton = false,
//     },
//     ref,
//   ) => (
//     <div className={styles.searchInputContainer}>
//       {isOpen ? (
//         <form
//           onSubmit={event => {
//             event.preventDefault();
//             onSearch(value);
//           }}
//           id={id ? `${id}-form` : 'search-input-form'}
//         >
//           <TextInput
//             id={id}
//             isAutoFocused
//             type="search"
//             left={<SearchIcon height="20px" width="20px" />}
//             placeholder="Search"
//             className={styles.input}
//             value={value}
//             onChange={event => onChange(event.currentTarget.value)}
//             maxLength={maxLength}
//             right={
//               !hideClearButton && (
//                 <Button
//                   theme="light"
//                   size="small"
//                   onClick={() => {
//                     onChange('');
//                     onOpen(false);
//                   }}
//                 >
//                   <XIcon height="20px" width="20px" />
//                 </Button>
//               )
//             }
//             ref={ref}
//           />
//         </form>
//       ) : (
//         <Button
//           theme="light"
//           size={btnSize || 'small'}
//           onClick={() => onOpen(true)}
//           id={id}
//         >
//           <SearchIcon height="20px" width="20px" color={searchIconColor} />
//         </Button>
//       )}
//     </div>
//   ),
// );

// export default SearchInput;
