@value variables: "../../../styles/variables.module.css";
@value eightSpace, smallWidth, higherModalXZIndex, black35 from variables;

.modalPortal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  z-index: higherModalXZIndex;
  pointer-events: none;

  @media (max-width: smallWidth) {
    &.isFixedInMobile {
      justify-content: flex-end;
      padding-bottom: eightSpace;
    }
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: black35;
  z-index: 0;
  pointer-events: auto;
}
