export const parseDollars = (formattedString: string): number | null => {
  if (!formattedString) return null;

  if (!Number.isNaN(Number(formattedString))) return Number(formattedString);
  // Remove the dollar sign and any commas
  const numericString = formattedString.replace(/[$,]/g, '');

  // Parse the resulting string into a number
  const parsedValue = parseFloat(numericString);

  // Return null if parsing fails
  return Number.isNaN(parsedValue) ? null : parsedValue;
};

export const formatDollars = (dollarsInt: number): string => {
  if (!dollarsInt && dollarsInt !== 0) return '';

  return `$${dollarsInt % 1 !== 0 ? dollarsInt.toFixed(2) : dollarsInt.toLocaleString()}`;
};

export function formatCurrency(amount, currencyCode = 'USD', locale = 'en-US') {
  // Round the amount to 2 decimal places
  const roundedAmount = Math.round(amount * 100) / 100;

  // Check if the number has a non-zero fractional part
  const hasFraction = roundedAmount % 1 !== 0;

  const formattingOptions = {
    style: 'currency',
    currency: currencyCode,
    minimumFractionDigits: hasFraction ? 2 : 0,
    maximumFractionDigits: 2,
  };

  return new Intl.NumberFormat(locale, formattingOptions).format(roundedAmount);
}
