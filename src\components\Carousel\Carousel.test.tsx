import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { CAROUSEL_SLIDER_DATA_TEST_ID } from '@constants/dataTestId';

import Carousel from './Carousel';

describe('Carousel component', () => {
  const items = [
    { id: 1, title: 'Slide 1' },
    { id: 2, title: 'Slide 2' },
    { id: 3, title: 'Slide 3' },
  ];

  const renderSlide = item => <h2>{item.title}</h2>;

  test('renders the correct number of slides', () => {
    render(<Carousel items={items} renderSlide={renderSlide} />);
    const slides = screen.getAllByRole('heading');
    expect(slides.length).toBe(items.length);
  });

  test('renders the first slide as default current slide', () => {
    render(<Carousel items={items} renderSlide={renderSlide} />);
    const firstSlide = screen.getByText('Slide 1');
    expect(firstSlide).toBeInTheDocument();
  });

  test('navigates to the next slide when clicking the "next" button', () => {
    render(<Carousel items={items} renderSlide={renderSlide} />);
    const nextButton = screen.getByRole('button', { name: /next/i });
    fireEvent.click(nextButton);
    const secondSlide = screen.getByText('Slide 2');
    expect(secondSlide).toBeInTheDocument();
  });

  test('navigates to the previous slide when clicking the "back" button', () => {
    render(
      <Carousel items={items} renderSlide={renderSlide} currentSlide={2} />,
    );
    const backButton = screen.getByRole('button', { name: 'previous' });
    fireEvent.click(backButton);
    const firstSlide = screen.getByText('Slide 1');
    expect(firstSlide).toBeInTheDocument();
  });

  test('renders the correct number of visible slides based on props', () => {
    render(
      <Carousel items={items} renderSlide={renderSlide} visibleSlides={2} />,
    );
    const slider = screen.getByTestId(CAROUSEL_SLIDER_DATA_TEST_ID);
    const visibleSlides = slider.querySelectorAll('[aria-selected="true"]');
    expect(visibleSlides.length).toBe(2);
  });
});
