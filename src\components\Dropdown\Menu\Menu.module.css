@value variables: "../../../styles/variables.module.css";
@value shadow, neutralDark, buttonLabelSmallFontSize from variables;
@value black, black10, ease-out-expo from variables;

.menu {
  z-index: 1;
  background-color: white;
  border-radius: 8px;
  width: max-content;
  min-width: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-out-expo;

  &.visible {
    opacity: 1;
    border: 1px solid black10;
    box-shadow: shadow;
  }

  &.alignedRight {
    left: auto;
    right: 0;
  }
}

.container {
  position: relative;
  border-radius: 8px;
}

.list {
  max-height: 33vh;
  overflow-y: auto;
  text-align: left;
  list-style: none;

  &:-moz-focus-inner {
    border: 0;
  }
}
