import React, { useEffect, useRef, useState } from 'react';
import useResizeObserver from '@hooks/useResizeObserver';

interface LogoProps {
  color?: string;
  isFullWidth?: boolean;
}

const Logo = ({ color = 'currentColor', isFullWidth = false }: LogoProps) => {
  const [isLogoSmall, setIsLogoSmall] = useState(false);
  const THRESHOLD = 100;
  const wrapper = useRef(null);
  const size = useResizeObserver(wrapper);
  const width = isFullWidth ? '100%' : `${THRESHOLD}px`;

  const wrapperStyle = {
    maxWidth: width,
  };

  const svgStyle = {
    display: 'block',
  };

  useEffect(() => {
    if (size) {
      const {
        contentRect: { width: rectWidth },
      } = size;

      setIsLogoSmall(rectWidth < THRESHOLD);
    }
  }, [size]);

  const renderSVG = isLogoSmall ? (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 100 48"
      width="100%"
      fill={color}
      style={svgStyle}
    >
      <path d="M78.7 12.2l-3.5 19.3c0 .2.1.4.3.4 1 .2 1.9.2 3 .1.2 0 .3-.2.3-.3l3.6-19.3c0-.2-.1-.4-.3-.4-1-.2-2-.2-3.1 0-.2-.1-.3.1-.3.2zm-6 0l-.9 5.6c0 .1-.1.1-.1.1-.9-.7-2-1.2-3.2-1.4-4.3-.7-8.8 2.2-10.1 6.6-1.3 4.4 1.1 8.5 5.4 9.2 4.3.7 9.6-1.3 10.5-8.4l1.9-11.6c0-.2-.1-.4-.3-.4-.9-.2-1.9-.2-2.9 0-.1 0-.2.2-.3.3zM70 25.1c-.7 2.4-2.9 4-5.2 3.6-2.3-.4-3.3-2.7-2.5-5.1.7-2.4 2.9-4 5.2-3.6 2.3.4 3.3 2.7 2.5 5.1zM54.8 12.2l-.9 5.6c0 .1-.1.1-.1.1-.9-.7-2-1.2-3.2-1.4-4.3-.7-8.8 2.2-10.1 6.6-1.3 4.4 1.1 8.5 5.4 9.2 4.3.7 9.6-1.3 10.5-8.4l1.9-11.6c0-.2-.1-.4-.3-.4-.9-.2-1.9-.2-2.9 0-.1 0-.3.2-.3.3zm-2.7 12.9c-.7 2.4-2.9 4-5.2 3.6-2.3-.4-3.3-2.7-2.5-5.1.7-2.4 2.9-4 5.2-3.6 2.3.4 3.3 2.7 2.5 5.1zM33 16.4c-4.3-.7-8.8 2.2-10.1 6.6-1.3 4.4 1.1 8.5 5.4 9.3 2.3.4 4.3-.2 5.9-1.2 1.4-.9 2.5-2 3.2-3.2.1-.1 0-.2-.1-.2h-4.4c-.1 0-.1 0-.2.1-1 .8-2.3 1.2-3.4 1-1.5-.3-2.5-1.4-2.7-2.9h11.7s.1 0 .1-.1c1.3-4.4-1.1-8.6-5.4-9.4zm1.6 6.4h-7.4c-.1 0-.1-.1-.1-.1 1-1.9 2.9-3 4.9-2.6 1.5.3 2.4 1.3 2.7 2.7 0-.1-.1 0-.1 0zm57.6-6.4c-4.3-.7-8.8 2.2-10.1 6.6-1.3 4.4 1.1 8.5 5.4 9.3 2.3.4 4.3-.2 5.9-1.2 1.4-.9 2.5-2 3.2-3.2.1-.1 0-.2-.1-.2h-4.4c-.1 0-.1 0-.2.1-1 .8-2.3 1.2-3.4 1-1.5-.3-2.5-1.4-2.7-2.9h11.7s.1 0 .1-.1c1.3-4.4-1.1-8.6-5.4-9.4zm1.6 6.4h-7.4c-.1 0-.1-.1-.1-.1 1-1.9 2.9-3 4.9-2.6 1.5.3 2.4 1.3 2.7 2.7 0-.1-.1 0-.1 0zm-78.7-6.4c-4.1-.7-8.4 2-9.9 6.1h-.1c-.4-.8-.7-1.4-1.1-2.1-.1-.1-.2-.1-.3 0-.8 1-1.4 1.9-1.8 2.9-.1.1-.1.3 0 .4.3.6 1 2.2 2.2 3.8 0 .1.1.2.1.2L2.8 36c0 .2.1.4.3.4.9.1 1.9.1 2.8 0 .2 0 .3-.2.3-.3L7.8 27c.8-4.9 3.3-7.5 6.3-7 2.4.4 3.3 2.8 2.5 5.2-.7 2.1-2.4 3.6-4.4 3.6-1.1 0-1.8-.4-2.4-.8-.1-.1-.3 0-.3.1-.5 1.8-.7 3.1-.8 3.6 0 .1 0 .2.1.2.6.2.9.3 1.6.5 4.4.6 8.8-1.4 10.2-6.6 1.3-4.4-1.2-8.7-5.5-9.4z" />
    </svg>
  ) : (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 1000 300"
      width="100%"
      fill={color}
      style={svgStyle}
    >
      <path d="M326.4 67.3c-45.9-7.7-94.3 24-108.1 70.9-13.8 46.9 12.2 91.6 58.2 99.3 24.2 4 45.7-2.1 63-13 16.4-10.2 29.1-24.7 37.3-38.9.5-.9-.2-2.1-1.3-2.1H330c-.7 0-1.3.3-1.8.8-10.8 12-27.1 17.1-41 14.6-16.5-3.1-26.7-15.5-29.2-30.8h125.3c.5 0 .9-.3 1-.7C398.8 120 372.7 75 326.4 67.3zm17.3 68h-78.9c-.7 0-1.1-.7-.8-1.4 10.3-20 30.8-32.3 52.1-28.3 15.8 2.9 25.7 14.1 28.7 28.4 0 .7-.5 1.3-1.1 1.3zm591.7-68c-45.9-7.7-94.3 24-108.1 70.9-13.8 46.9 12.2 91.6 58.2 99.3 24.2 4 45.7-2.1 63-13 16.4-10.2 29.1-24.7 37.3-38.9.5-.9-.2-2.1-1.3-2.1H939c-.7 0-1.3.3-1.8.8-10.8 12-27.1 17.1-41 14.6-16.5-3.1-26.7-15.5-29.2-30.8h125.3c.5 0 .9-.3 1-.7 14.5-47.4-11.6-92.4-57.9-100.1zm17.3 68h-78.9c-.7 0-1.1-.7-.8-1.4 10.3-20 30.8-32.3 52.1-28.3 15.8 2.9 25.7 14.1 28.7 28.4.1.7-.5 1.3-1.1 1.3zM143.2 67.1c-43.8-7.4-89.9 21.6-106 65-.2.4-.7.5-.9.1-4.6-8.2-7.6-14.8-11.3-22.5-.6-1.3-2.5-1.4-3.5-.2-8.3 10.5-14.7 21-19.3 31.6-.7 1.6-.7 3.3-.1 4.8 2.7 6.4 11.1 23.8 23.2 41.2.5.8.7 1.7.6 2.7l-14.6 88.7c-.3 2 .9 3.9 2.9 4.2 10.1 1.5 20.3 1.4 30.5-.1 1.8-.3 3.2-1.7 3.5-3.5l17-98.5c8.6-52.7 35.7-80.6 67.2-75.1 26.1 4.8 35.3 29.9 27.3 55.6-7.1 22.8-26.3 38.2-47.7 38.2-12.6 0-21.8-5-28.4-9.7-1.2-.9-2.9-.3-3.3 1.2-4.9 18.7-7.5 33.2-8.3 38.2-.2 1 .3 1.9 1.2 2.3 6.4 3.1 12.6 5 20 6.2 47.5 6.8 94.6-15 109.2-70.5 13.7-47-13.3-92.1-59.2-99.9zM793.9 22l-36.5 207.1c-.4 2 .9 3.9 3 4.2 11.2 1.6 20.3 1.8 31.8.4 1.8-.2 3.3-1.7 3.6-3.5l37.9-207.6c.3-2-.9-3.9-2.9-4.2-11.3-1.8-21.9-1.6-33.4.1-1.7.3-3.2 1.8-3.5 3.5zm-55 0l-9.8 59.9c-.1.6-.9 1-1.3.6-9.5-7.6-21.1-12.9-34.4-15.2-45.9-7.7-94.3 24-108.1 70.9-13.8 46.9 12.2 91.2 58.2 99 45.9 7.7 102.8-13.6 112.6-89.7l20-124.9c.3-2-.9-3.8-2.8-4.1-10.1-1.7-20.5-1.7-30.8 0-2 .3-3.4 1.8-3.6 3.5zm-29 138c-8 25.6-31.2 43.1-55.8 38.5s-35.3-29.1-27.3-54.7c8-25.6 31.6-42.7 56.1-38.1 24.6 4.5 35 28.6 27 54.3zM554.4 22l-9.8 59.9c-.1.6-.9 1-1.3.6-9.5-7.6-21.1-12.9-34.4-15.2-45.9-7.7-94.3 24-108.1 70.9-13.8 46.9 12.2 91.2 58.2 99 45.9 7.7 102.8-13.6 112.6-89.7l20-124.9c.3-2-.9-3.8-2.8-4.1-10.1-1.7-20.5-1.7-30.8 0-1.9.3-3.3 1.8-3.6 3.5zm-28.9 138c-8 25.6-31.2 43.1-55.8 38.5s-35.3-29.1-27.3-54.7c8-25.6 31.6-42.7 56.1-38.1 24.6 4.5 35 28.6 27 54.3z" />
    </svg>
  );

  return (
    <div ref={wrapper} style={wrapperStyle}>
      {renderSVG}
    </div>
  );
};

export default Logo;
