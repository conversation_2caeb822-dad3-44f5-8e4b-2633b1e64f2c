import { FC, forwardRef } from 'react';
import { ANIMATED_LOADER } from '@constants/dataTestId';

import styles from './AnimatedLoader.module.css';
import { AnimatedLoaderProps } from './AnimatedLoader.types';

const AnimatedLoader: FC<AnimatedLoaderProps> = forwardRef<
  SVGSVGElement,
  AnimatedLoaderProps
>(
  (
    { color = 'currentColor', theme = 'dark', size = 24, isDisabled = false },
    ref,
  ) => (
    <svg
      className={[
        styles.loaderIcon,
        styles[theme],
        isDisabled && styles.disabled,
      ].join(' ')}
      ref={ref}
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      stroke={color}
      data-testid={ANIMATED_LOADER}
    >
      <circle
        className={styles.path}
        fill="none"
        strokeWidth="2"
        strokeLinecap="round"
        cx="12"
        cy="12"
        r="10"
      />
    </svg>
  ),
);

AnimatedLoader.displayName = 'AnimatedLoader';

export default AnimatedLoader;
