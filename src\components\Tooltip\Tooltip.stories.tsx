import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

import TooltipComponent from './Tooltip';

export default {
  title: 'Components/Tooltip',
  component: TooltipComponent,
  argTypes: {
    placement: {
      control: 'select',
      options: ['top', 'bottom', 'left', 'right'],
    },
  },
};

const Template = args => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
      margin: variableStyles.fiveSpace,
    }}
  >
    <ComponentGrid countColumns={4}>
      <ComponentTile label="[hover on content to view tooltip]">
        <div className="d-flex justify-content-center mt-4">
          <TooltipComponent {...args}>content</TooltipComponent>
        </div>
      </ComponentTile>
    </ComponentGrid>
  </div>
);

export const Tooltip = Template.bind({});
Tooltip.args = {
  text: 'Tooltip text',
  placement: 'top',
};
