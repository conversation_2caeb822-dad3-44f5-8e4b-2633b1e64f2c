/* eslint-disable no-console */
// import { DiscIcon } from '@peddleon/ped-ux-react-icons';
import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta } from '@storybook/react';
import Button from '@components/Button';
import SectionWrapper from '@components/SectionWrapper';

import MultipleSectionWrapper from './MultipleSectionWrapper';
/**
 *
 * <br/>

## Overview

The Section Wrapper component is a versatile container for organizing content within a UI section.
It offers features like customizable headings, icons, and action buttons.
To construct the section wrapper from an integration perspective,
developers need to provide props as shown in the Storybook and as described in the props documentation.

## Usage

Import the component into your React application:

```jsx
import { MultipleSectionWrapper } from '@peddleon/ped-ux-react-library';```

 */

const meta: Meta<typeof MultipleSectionWrapper> = {
  component: MultipleSectionWrapper,
  title: 'Components/MultiSectionWrapper',
  tags: ['autodocs'],
  parameters: {
    docs: {
      toc: true,
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
};

export const SectionWrapperStory = () => (
  <MultipleSectionWrapper>
    <SectionWrapper
      actions={[
        [
          <Button
            key="button1"
            label="Button 1"
            onClick={() => {}}
            theme="light"
          />,
        ],
      ]}
      headingText="Publisher Peddle Integration"
      // icon={<DiscIcon width={24} height={24} />}
      options={{
        actionDirection: 'right',
        dividerTheme: 'grey',
        hasDevider: true,
        headingTag: 'h2',
      }}
    >
      <div>
        Lorem Ipsum is simply dummy text of the printing and typesetting dummy
        text ever since the 1500s, when an unknown printer took a galley of type
        and scrambled it to make a type specimen book. It has survived not only
        five centuries, but also the leap into electronic typesetting, remaining
        essentially unchanged.
      </div>
    </SectionWrapper>
    <SectionWrapper
      actions={[
        [
          <Button
            key="button1"
            label="Button 1"
            onClick={() => {}}
            theme="light"
          />,
        ],
      ]}
      headingText="Publisher Peddle Integration"
      // icon={<DiscIcon width={24} height={24} />}
      options={{
        actionDirection: 'right',
        dividerTheme: 'grey',
        hasDevider: false,
        headingTag: 'h2',
      }}
    >
      <div>
        Lorem Ipsum is simply dummy text of the printing and typesetting dummy
        text ever since the 1500s, when an unknown printer took a galley of type
        and scrambled it to make a type specimen book. It has survived not only
        five centuries, but also the leap into electronic typesetting, remaining
        essentially unchanged.
      </div>
    </SectionWrapper>
  </MultipleSectionWrapper>
);

export default meta;
