name: Publish pr package

on:
  pull_request:
    types: [opened, synchronize]

jobs:
  test:
    name: Release pr package
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'
          registry-url: https://npm.pkg.github.com/
          cache: 'npm'

      - name: Install dependencies
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GIT_TOKEN }}

      - name: Publish pr package
        run: |
          sudo apt-get update
          sudo apt-get install -y moreutils
          VERSION=$(cat package.json | jq -r '.version')
          TAG="pr-$PR.$GITHUB_RUN_NUMBER"
          jq ".version = \"$VERSION-$TAG\"" package.json | sponge package.json
          npm publish --tag pr-$PR --registry=https://npm.pkg.github.com/
        env:
          PR: ${{ github.event.pull_request.number }}
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
