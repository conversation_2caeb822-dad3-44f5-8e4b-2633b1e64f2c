import { Mode } from 'react-day-picker';

/**
 * Defines the types and props for the DatePicker component.
 *
 * @template T - The mode of the date picker (`'single'`, `'range'`, or `'multiple'`).
 */
export type SetToOptions<T extends Mode> =
  | 'today'
  | 'tomorrow'
  | (T extends 'range' | 'multiple' ? 'this week' | 'this month' : never);

/**
 * Represents the date type based on the picker mode.
 *
 * @template T - The mode of the date picker.
 */
export type DateType<T extends Mode> = T extends 'range'
  ? { from: Date; to: Date }
  : T extends 'multiple'
    ? Date[]
    : Date;

/**
 * Specifies the type for disabled dates in the DatePicker component.
 *
 * @template T - The mode of the date picker.
 */
export type DisabledDate = Date[] | { before: Date; after: Date } | Date;

/**
 * Defines the props for the DatePicker component.
 *
 * @template T - The mode of the date picker (`'single'`, `'range'`, or `'multiple'`).
 */
export type DatePickerPropTypes<T extends Mode> = {
  /**
   * A unique identifier for the DatePicker component.
   */
  id: string;

  /**
   * The mode of the date picker, defining the type of date selection.
   */
  pickerType: T;

  /**
   * Specifies whether the DatePicker should be rendered inline.
   */
  isInline?: boolean;

  /**
   * Callback function triggered when the selected date or date range changes.
   *
   * @param {DateType<T>} date - The newly selected date or date range.
   */
  onDateChange?: (date: DateType<T>) => void;

  /**
   * Defines the dates that should be disabled in the picker.
   */
  disabledDates?: DisabledDate;

  /**
   * Predefined ranges or dates to initialize the picker.
   *
   * Example: `'today'`, `'this week'`.
   */
  setTo?: Exclude<
    SetToOptions<T>,
    T extends 'single' ? 'multiple' | 'range' : never
  >;

  /**
   * Indicates whether the DatePicker input is required.
   */
  isRequired?: boolean;

  /**
   * Specifies whether disabled dates should be excluded from the date picker selection.
   */
  excludeDisabled?: boolean;

  /**
   * Specifies the timezone for the DatePicker.
   *
   * This can be useful for applications that need to handle dates in different timezones.
   */
  timezone?: string;

  /**
   * The value for the DatePicker component. This can be used to set the initial date or date range.
   */
  value?: DateType<T>;

  /**
   * The minimum date that can be selected in the DatePicker.
   */
  hasFooter?: boolean;

  /**
   * Callback function triggered when the "Today" button in the footer is clicked.
   */
  onFooterTodayButtonClick?: (date) => void;
};
