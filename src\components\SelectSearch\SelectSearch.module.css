@value variables: "../../styles/variables.module.css";
@value h6FontSize, buoyantZIndex, halfSpace, twoSpace, oneSpace, black, black5, black10, black35, black50 from variables;
@value danger, white, primary, success, threeSpace, twoSpace, eightSpace,neutralLighter, black35Opaque, h3FontSize from variables;
@value ease-out-expo, smallWidth from variables;

.comboboxWrapper {
  position: relative;

  & input {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }
}

.inputWrapper {
  @media (max-width: smallWidth) {
    &:focus-within {
      & label {
        color: black !important;
        font-family: Aventa;
        font-weight: 700;
        line-height: 20px; /* 166.667% */
        letter-spacing: 0.12px;
      }
    }
  }
}

.helveticaFontsWrapper {
  @media (max-width: smallWidth) {
    &:focus-within {
      & label {
        color: black35Opaque !important;
        font-family: Helvetica Now Var;
        font-size: 21px;
        line-height: 18px;
        font-weight: 900;
      }
    }
  }
}

.combobox {
  & .chevronIconWrapper {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;

    &:focus-visible,
    &:focus-visible {
      box-shadow: none;
    }
  }
}

&:hover {
  & .container {
    border-color: black35;
  }
}

.disabled {
  pointer-events: none;

  & .container {
    pointer-events: none;
  }

  & .button {
    pointer-events: none;
    background-color: black5;
    color: black35;
  }
}

.borderless {
  & .container {
    border: none;
  }
}

.comboboxWrapper.errored {
  & .button {
    border-color: danger;

    &:focus {
      border-color: danger;

      & .label {
        color: black;
      }

      & .leftWrapper {
        color: black;
      }

      & .rightWrapper {
        color: black;
      }

      & .chevronIconWrapper {
        color: black;
      }
    }
  }
}

.combobox.errored {
  & .container {
    border-color: danger;
  }

  &.focused {
    & .container {
      border-color: danger;
    }

    & .label {
      color: black;
    }

    & .leftWrapper {
      color: black;
    }

    & .rightWrapper {
      color: black;
    }

    & .chevronIconWrapper {
      color: black;
    }
  }
}

.error {
  color: danger;
}

.combobox.focused {
  & .container {
    border-color: primary;
  }

  & .label {
    color: primary;
  }

  & .leftWrapper {
    color: primary;
  }

  & .rightWrapper {
    color: black;
  }

  & .chevronIconWrapper {
    color: primary;
    transform: rotate(180deg);
  }
}

.focused,
.filled {
  & .label {
    top: 0;
    transform: translateY(8px) scale(0.75);
    line-height: 20px;
  }

  & .input::placeholder {
    opacity: 1;
  }

  @media (max-width: smallWidth) {
    & .label {
      top: 0;
      transform: translateY(8px) scale(0.85);
      line-height: 20px;
      font-weight: bold;
      color: black;
    }
  }
}

.inputWrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

.wrapper {
  line-height: 0;
}

.leftWrapper {
  composes: wrapper;
  margin-right: oneSpace;
  color: black50;
  transition: color 0.5s ease-out-expo;
}

.rightWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  display: flex;
  flex-flow: row;
  align-items: center;
  color: black50;

  & > * {
    line-height: 0;
  }
}

.errorIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: danger;
}

.successIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: success;
}

.fetchingIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
}

.label {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0.01em;
  color: black50;
  transform-origin: left center;
  will-change: top, transform, color;
  transition:
    color 0.5s ease-out-expo,
    top 0.5s ease-out-expo,
    transform 0.5s ease-out-expo;
  pointer-events: none;
  user-select: none;
}

.input:focus {
  &.label {
    top: 10%;
    transform: translateY(-50%) scale(0.75);
  }
}

.input {
  width: 100%;
  height: 100%;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.01em;
  color: black;
  align-self: flex-end;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;

  &::placeholder {
    opacity: 0;
    color: black50;
    font-weight: 400;
    letter-spacing: 0.01em;
    transition: opacity 0.5s ease-out-expo;
  }

  &.hasLabel {
    padding-top: twoSpace;
  }

  @media (max-width: smallWidth) {
    padding-bottom: oneSpace;
  }
}

.footer {
  text-align: left;
  padding: halfSpace twoSpace 0 twoSpace;
  font-size: 12px;
  line-height: 20px;
}

.active .menuWrapper {
  display: block;
}

.menuWrapper {
  display: none;
  line-height: 0;
  position: relative;
  z-index: buoyantZIndex;
}

.menuSelectInput {
  z-index: 3 !important;

  &:not(.fixedListWrapper) {
    @media (min-width: smallWidth) {
      position: absolute !important;
    }
  }
}

.chevronIconWrapper {
  margin-left: oneSpace;
  transform-origin: center center;
  transition: transform 0.5s ease-out-expo;
  color: black50;
}

.optionListMatchedCharacters {
  font-weight: bold;
}

.dialogModalWrapper {
  &.bottom {
    justify-content: flex-end;
    padding-bottom: eightSpace;
  }
  &.center {
    justify-content: center;
  }
  &.top {
    justify-content: flex-start;
    padding-top: eightSpace;
  }
}

.modalHeader {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 85%;

  & input {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  & .inputWrapper {
    & .label {
      transform: translateY(-8px) scale(0.75);
    }
  }
}

.modalHeaderContainer {
  /* to override the modal default styles */
  padding-left: twoSpace !important;

  @media (max-width: smallWidth) {
    padding: 0 !important;
  }
}

.modalForCombo {
  border: 0;

  & .containerScroll {
    overflow: unset !important;
  }
}

.modalContainer {
  min-height: var(--scrollMinHeight);
}

.buttonContainer {
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
}

.button {
  cursor: pointer;
  width: 100%;
  height: 64px;
  background: white;
  border: solid 1px;
  border-color: black10;
  border-radius: 8px;
  transition: border-color 0.5s ease-out-expo;
  padding-left: twoSpace;
  padding-right: twoSpace;
}

.centerWrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

.wrapper {
  line-height: 0;
}

.leftWrapper {
  composes: wrapper;
  margin-right: oneSpace;
  color: black50;
  transition: color 0.5s ease-out-expo;
}

.rightWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  display: flex;
  flex-flow: row;
  align-items: center;
  color: black50;

  & > * {
    line-height: 0;
  }
}

.errorIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: danger;
}

.successIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: success;
}

.fetchingIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
}

.label {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0.01em;
  color: black50;
  transform-origin: left center;
  will-change: top, transform, color;
  transition:
    color 0.5s ease-out-expo,
    top 0.5s ease-out-expo,
    transform 0.5s ease-out-expo;
  pointer-events: none;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selected {
  position: absolute;
  bottom: 12px;
  left: 0;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.01em;
  color: black;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menuWrapper {
  line-height: 0;
  position: relative;
  z-index: buoyantZIndex;
}

.footer {
  display: flex;
  flex-direction: column;
  text-align: left;
  padding: halfSpace twoSpace 0 twoSpace;
  font-size: 12px;
  line-height: 20px;
}

.error {
  color: danger;
}

.fallbackSelect {
  display: none;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.caption {
  color: black70;
}

.notMatchFound {
  cursor: default;

  & li {
    cursor: default;
  }
}

.inputMenuModal {
  z-index: 0 !important;
  position: relative !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0;

  & > * > * {
    max-height: none !important;
  }

  @media (max-width: smallWidth) {
    border-top: 1px solid neutralLighter !important;
  }
}

.overlayCloseBtn {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 1;
  cursor: pointer;
}

.inputRightNavigation {
  position: relative;
}

.fallbackSelect {
  display: none;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.selectInput {
  &:hover {
    & .button {
      border-color: black35;
    }
  }
}

.selectInput.errored {
  & .button {
    border-color: danger;

    &:focus {
      border-color: danger;

      & .label {
        color: black;
      }

      & .leftWrapper {
        color: black;
      }

      & .rightWrapper {
        color: black;
      }

      & .chevronIconWrapper {
        color: black;
      }
    }
  }
}

.selectInput {
  & .button {
    &:focus {
      outline: none;
      border-color: primary;

      & .label {
        color: primary;
      }

      & .leftWrapper {
        color: primary;
      }

      & .rightWrapper {
        color: black;
      }

      & .chevronIconWrapper {
        color: primary;
      }
    }

    &:focus-visible {
      box-shadow: none;
    }
  }
}

.inputMenuModalContainer {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.container {
  position: relative;
}

.fixedListWrapper {
  min-width: unset;
  position: fixed;
}

.group {
  margin-top: twoSpace;

  @media (max-width: smallWidth) {
    margin-top: halfSpace;

    &:first-child {
      margin-top: oneSpace;
    }
  }
}

.fixedListWrapper .group {
  margin-top: halfSpace;

  @media (max-width: smallWidth) {
    margin-top: halfSpace;

    &:first-child {
      margin-top: oneSpace;
    }
  }
}

.menuSelectInput .group:first-of-type {
  &:first-of-type {
    margin-top: 20px;
  }
}

.fixedListWrapper.menuSelectInput .group:first-of-type {
  &:first-of-type {
    margin-top: oneSpace;
  }
}

.menuSelectInput .group:last-of-type {
  &:last-of-type {
    margin-bottom: twoSpace;
  }
}

.groupList {
  list-style: none;
  padding-left: twoSpace;

  @media (max-width: smallWidth) {
    padding-top: 0;
  }
}

.groupHeader {
  font-weight: 600;
  padding: halfSpace oneSpace;
  pointer-events: none;
  color: black;
  padding-left: twoSpace;
}

.groupList {
  list-style: none;
  padding-left: twoSpace;
  padding-top: 12px;
  @media (max-width: smallWidth) {
    padding-top: 0;
  }
}

.fixedListWrapper .groupList {
  padding-top: 0;
}

.groupList li {
  padding: oneSpace;
  padding-left: twoSpace;
  font-size: h6FontSize;
  line-height: 1.5;
  color: black;
  cursor: pointer;
}

.groupHeaderMatched {
  font-weight: bold;
}

.helveticaFonts {
  & .button {
    height: 72px;

    @media (max-width: smallWidth) {
      height: 56px;
    }
  }

  & .label {
    text-transform: uppercase;
    font-family: Helvetica Now Var;
    font-size: h3FontSize;
    font-weight: 900;
    line-height: fourSpace;
    letter-spacing: 0.02em;
    font-variation-settings:
      'wght' 900,
      'wdth' 50,
      'opsz' 30;
    color: black35;

    @media (max-width: smallWidth) {
      font-size: 20px;
      line-height: 20px;
    }
  }

  & .focused {
    & .container {
      border-color: black;
    }

    & .label {
      color: black;
    }

    & .leftWrapper {
      color: black;
    }

    & .rightWrapper {
      color: black;
    }

    & .chevronIconWrapper {
      color: black;
      transform: rotate(180deg);
    }
  }

  &.filled:not(.focused) {
    & .label {
      color: black35;
    }
  }

  &.focused,
  &.filled {
    & .label {
      font-family: Helvetica Now Var;
      font-size: 19px;
      line-height: 19px;
      color: black50;
      font-weight: 900;
      letter-spacing: 0.02em;
    }

    & .selected {
      line-height: fourSpace;
      height: 30px;
    }

    @media (max-width: smallWidth) {
      & .label {
        font-size: 17px;
        line-height: 14px;
      }

      & .selected {
        line-height: 15px;
        height: unset;
      }
    }
  }

  & .selected {
    font-family: Helvetica Now Var;
    font-size: h3FontSize;
    font-weight: 900;
    letter-spacing: 0.02em;
    line-height: 30px;
    color: black;
    font-variation-settings:
      'wght' 900,
      'wdth' 50,
      'opsz' 30;
    text-transform: uppercase;

    &::placeholder {
      color: black35;
      text-transform: uppercase;
    }

    @media (max-width: smallWidth) {
      font-size: 20px;
      line-height: 20px;
    }
  }
}
