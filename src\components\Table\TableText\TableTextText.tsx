import React, { CSSProperties, ReactNode, useMemo } from 'react';
import { EMPTY_TABLE_VALUE } from '@constants/common';
import classNames from '@utils/classNames';

import styles from './TableText.module.css';

interface TableTextTextProps {
  isInverted?: boolean;
  isActive?: boolean;
  customStyle?: CSSProperties;
  className?: string;
  children: ReactNode;
}

const TableTextText: React.FC<TableTextTextProps> = ({
  isInverted,
  isActive,
  customStyle,
  className,
  children,
}) => {
  const title = useMemo(
    () => (typeof children === 'string' ? (children as string) : ''),
    [children],
  );
  return (
    <div
      className={classNames(
        styles.text,
        isActive && styles.active,
        isInverted && styles.isInverted,
        className,
      )}
      style={customStyle}
      title={title}
    >
      {children || EMPTY_TABLE_VALUE}
    </div>
  );
};

export default TableTextText;
