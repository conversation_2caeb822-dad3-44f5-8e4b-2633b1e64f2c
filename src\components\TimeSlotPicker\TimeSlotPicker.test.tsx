import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { TIME_SLOT_PICKER_HEADER_DATA_TEST_ID } from '@constants/dataTestId';

import TimeSlotPicker from './TimeSlotPicker';
import { TimeSlotPickerRowPropTypes } from './TimeSlotPicker.types';

describe('TimeSlotPicker', () => {
  const rowItems: TimeSlotPickerRowPropTypes[] = [
    {
      isDisabled: false,
      date: '2024-04-16T07:00',
      items: [
        {
          id: '2024-04-16T07:00',
          startAt: '2024-04-16T07:00',
          endAt: '2024-04-16T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-17T07:00',
      items: [
        {
          id: '2024-04-17T07:00',
          startAt: '2024-04-17T07:00',
          endAt: '2024-04-17T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
        {
          id: '2024-04-17T11:00',
          startAt: '2024-04-17T11:00',
          endAt: '2024-04-17T15:00',
          timeStartAt: '11:00',
          timeEndAt: '15:00',
        },
        {
          id: '2024-04-17T15:00',
          startAt: '2024-04-17T15:00',
          endAt: '2024-04-17T20:00',
          timeStartAt: '15:00',
          timeEndAt: '20:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-18T07:00',
      items: [],
    },
  ];

  it('renders time slots correctly', () => {
    render(
      <TimeSlotPicker
        rowItems={rowItems}
        timeZone="UTC"
        value="2024-04-16T09:00"
        onSelect={() => {}}
      />,
    );

    // Check if the time slots are rendered
    const timeSlotElement = screen.getAllByText('7:00am - 11:00am UTC');
    expect(timeSlotElement[0]).toBeInTheDocument();
    expect(timeSlotElement[1]).toBeInTheDocument();
  });

  it('calls onSelect when a time slot is clicked', () => {
    const onSelectMock = jest.fn();
    render(
      <TimeSlotPicker
        rowItems={rowItems}
        timeZone="UTC"
        value="2024-04-16T09:00"
        onSelect={onSelectMock}
      />,
    );

    // Click on a time slot
    const timeSlotElement = screen.getAllByText('7:00am - 11:00am UTC')[0];
    fireEvent.click(timeSlotElement);

    // Check if onSelect is called with correct parameters
    expect(onSelectMock).toHaveBeenCalledWith({
      id: '2024-04-16T07:00',
      startAt: '2024-04-16T07:00',
      endAt: '2024-04-16T11:00',
      timeStartAt: '07:00',
      timeEndAt: '11:00',
    });
  });

  it('disables time slots when isDisabled prop is true', () => {
    render(
      <TimeSlotPicker
        rowItems={rowItems}
        timeZone="UTC"
        value="2024-04-16T09:00"
        onSelect={() => {}}
        isDisabled
      />,
    );

    // Check if the time slots are disabled
    const timeSlotElement = screen.getAllByText('7:00am - 11:00am UTC')[0];
    expect(
      timeSlotElement.parentElement.getElementsByTagName('input')[0].disabled,
    ).toBeTruthy();
  });

  it('renders loading state correctly', () => {
    render(
      <TimeSlotPicker
        rowItems={[]}
        timeZone="UTC"
        value=""
        onSelect={() => {}}
        isLoading
      />,
    );

    // Check if the loading state is rendered
    const loadingElement = screen.getByTestId('animated-loader-testId');
    expect(loadingElement).toBeInTheDocument();
  });

  it('renders empty state correctly', () => {
    render(
      <TimeSlotPicker
        rowItems={[]}
        timeZone="UTC"
        value=""
        onSelect={() => {}}
        renderEmptyState="There are no pickup slots available."
      />,
    );

    // Check if the empty state is rendered
    const emptyStateElement = screen.getByText(
      'There are no pickup slots available.',
    );
    expect(emptyStateElement).toBeInTheDocument();
  });

  it('renders date carousel header when hasDateCarouselHeader prop is true', () => {
    render(
      <TimeSlotPicker
        rowItems={rowItems}
        timeZone="UTC"
        value=""
        onSelect={() => {}}
        hasDateCarouselHeader
      />,
    );

    // Check if the date carousel header is rendered
    const headerElement = screen.getByTestId(
      TIME_SLOT_PICKER_HEADER_DATA_TEST_ID,
    );
    const firstAnchor = headerElement.getElementsByTagName('a')[0];
    expect(firstAnchor.getAttribute('href')).toBe('#4-16-24');
  });
});
