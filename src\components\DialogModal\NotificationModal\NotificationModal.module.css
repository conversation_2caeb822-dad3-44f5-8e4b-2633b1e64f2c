@value variables: "../../../styles/variables.module.css";
@value smallWidth, oneSpace from variables;

.notificationModal {
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: row wrap;
  pointer-events: none;
  position: relative;

  &.bottomRight {
    justify-content: flex-end;
    align-items: flex-end;
  }

  &.bottomLeft {
    justify-content: flex-start;
    align-items: flex-end;
  }

  &.bottomCenter {
    justify-content: center;
    align-content: flex-end;
  }

  &.topRight {
    justify-content: flex-end;
    align-content: flex-start;
  }

  &.topLeft {
    justify-content: flex-start;
    align-content: flex-start;
  }

  &.topCenter {
    justify-content: center;
    align-content: flex-start;
  }
}

.notificationWrapper {
  flex: 0 0 auto;
  padding: oneSpace;
  position: absolute;
  top: 74px;
  min-width: 240px;

  @media (max-width: smallWidth) {
    top: 150px;
  }
}

.defaultToastStyle {
  width: auto;
  position: initial;
}
