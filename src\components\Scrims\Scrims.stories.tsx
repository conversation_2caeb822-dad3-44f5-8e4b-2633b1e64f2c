/* eslint-disable react/jsx-max-depth */
import { useRef } from 'react';
import { Meta } from '@storybook/react';
import { PEDDLE_PUBLIC_ASSETS_URI } from '@constants/application';
import ComponentGrid from '@stories/ComponentGrid';
import variableStyles from '@styles/variables.module.css';

import Scrims from './Scrims';

/**
 * The Scrims component adds gradient overlays to indicate overflow in a scrollable container.
 * It dynamically detects overflow in the specified container and applies gradient overlays accordingly.
 *
 * ## Overview
 *
 * The Scrims component is used to visually indicate overflow in a scrollable container. It provides gradient overlays at the edges of the container, making it clear to users that there is more content to scroll to.
 *
 * ## Usage
 *
 * To use the Scrims component in your React application, import it from the appropriate directory and include it in your JSX.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { Scrims } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the Scrims component within your scrollable container:
 *
 * ```jsx
 * import React, { useRef } from 'react';
 *
 * const MyScrollableComponent = () => {
 *   const scrollRef = useRef();
 *
 *   return (
 *     <>
 *       <div ref={scrollRef}> scrollable content will be placed here </div>
 *       <Scrims overflowRef={scrollRef} />
 *     </>
 *   );
 * };
 * ```
 *
 * This will render gradient overlays at the edges of the scrollable container to indicate overflow.
 */
const meta: Meta<typeof Scrims> = {
  title: 'Utils/Scrims',
  tags: ['autodocs'],
  component: Scrims,
};

export const VerticalScrim = ({
  isInverted,
  isHorizontal,
}: {
  isInverted?: boolean;
  isHorizontal?: boolean;
}) => {
  const textOverflowRef = useRef();

  const bodyText = (
    <div
      style={{
        height: '200%',
        width: '100%',
      }}
    >
      <p
        style={{
          paddingBottom: variableStyles.twoSpace,
        }}
      >
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
        tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
        veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
        commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
        velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint
        occaecat cupidatat non proident, sunt in culpa qui officia deserunt
        mollit anim id est laborum.
      </p>
      <p
        style={{
          paddingBottom: variableStyles.twoSpace,
        }}
      >
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
        tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
        veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
        commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
        velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint
        occaecat cupidatat non proident, sunt in culpa qui officia deserunt
        mollit anim id est laborum.
      </p>
      <p
        style={{
          paddingBottom: variableStyles.twoSpace,
        }}
      >
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
        tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
        veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
        commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
        velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint
        occaecat cupidatat non proident, sunt in culpa qui officia deserunt
        mollit anim id est laborum.
      </p>
      <p
        style={{
          paddingBottom: variableStyles.twoSpace,
        }}
      >
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
        tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
        veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
        commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
        velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint
        occaecat cupidatat non proident, sunt in culpa qui officia deserunt
        mollit anim id est laborum.
      </p>
    </div>
  );

  return (
    <div
      style={{
        padding: variableStyles.eightSpace,
        backgroundColor: isInverted
          ? variableStyles.black
          : variableStyles.white,
        color: isInverted ? variableStyles.white : variableStyles.black,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <ComponentGrid countColumns={1}>
        <div
          style={{
            height: '400px',
            display: 'flex',
            justifyContent: 'center',
            paddingLeft: variableStyles.threeSpace,
            paddingRight: variableStyles.threeSpace,
            position: 'relative',
          }}
        >
          <div
            style={{ height: '100%', overflowY: 'auto' }}
            ref={textOverflowRef}
          >
            {bodyText}
          </div>
          <Scrims
            overflowRef={textOverflowRef}
            isInverted={isInverted}
            isHorizontal={isHorizontal}
          />
        </div>
      </ComponentGrid>
    </div>
  );
};

VerticalScrim.args = {
  isInverted: false,
  isHorizontal: false,
};

export const HorizontalScrim = ({ isInverted }: { isInverted?: boolean }) => {
  const imageOverflowRef = useRef();

  const renderPhotos = (
    <div
      style={{
        width: '250%',
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: isInverted
          ? variableStyles.black
          : variableStyles.white,
      }}
    >
      <img
        src={`${PEDDLE_PUBLIC_ASSETS_URI}/vehicle-views/sedan-driver-side.png?trim=color`}
        alt="sedan driver side view"
        style={{ objectFit: 'contain' }}
      />
      <img
        src={`${PEDDLE_PUBLIC_ASSETS_URI}/vehicle-views/sedan-driver-side.png?trim=color`}
        alt="sedan driver side view"
        style={{ objectFit: 'contain' }}
      />
      <img
        src={`${PEDDLE_PUBLIC_ASSETS_URI}/vehicle-views/sedan-driver-side.png?trim=color`}
        alt="sedan driver side view"
        style={{ objectFit: 'contain' }}
      />
    </div>
  );

  return (
    <div
      style={{
        padding: variableStyles.eightSpace,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <div
        style={{
          position: 'relative',
        }}
      >
        <div
          style={{
            overflowX: 'auto',
          }}
          ref={imageOverflowRef}
        >
          {renderPhotos}
        </div>
        <Scrims
          overflowRef={imageOverflowRef}
          isInverted={isInverted}
          isHorizontal
        />
      </div>
    </div>
  );
};

HorizontalScrim.args = {
  isInverted: false,
};

export default meta;
