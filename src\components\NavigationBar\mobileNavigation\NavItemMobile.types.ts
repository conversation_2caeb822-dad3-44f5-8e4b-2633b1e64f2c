import { ReactNode, SyntheticEvent } from 'react';

export interface NavItemMobileProps {
  /** label to be display */
  label: string;
  /** Whether the current list is active or not */
  isActive?: boolean;
  /** link for nav list */
  link?: string;
  /** Whether the list contains the sublist in it */
  isSubList?: boolean;
  /** Icon to be displayed at the left of label */
  leftIcon?: ReactNode;
  /** Icon to be displayed at the right of label */
  rightIcon?: ReactNode;
  /** Function to be called when the list has been clicked */
  onClick?: (event: SyntheticEvent<Element, Event>) => void;
  /** Function to be called when the left icon is clicked */
  onLeftIconClick?: (event: SyntheticEvent<Element, Event>) => void;
  /** Function to be called when the right icon is clicked */
  onRightIconClick?: (event: SyntheticEvent<Element, Event>) => void;
  /** Additional CSS class for styling */
  className?: string;
  /** Additional CSS class to style the button label */
  labelClassName?: string;
}
