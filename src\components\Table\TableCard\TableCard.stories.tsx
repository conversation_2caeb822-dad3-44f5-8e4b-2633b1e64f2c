import { Meta } from '@storybook/react';
import Button from '@components/Button';

import TableCard from './TableCard';
import styles from './TableCard.module.css';

export default {
  title: 'Components/Table/TableCard',
  tags: ['autodocs'],
  component: TableCard,
  argTypes: {
    statusTheme: {
      control: { type: 'select' },
      options: [
        'dark',
        'primary',
        'danger',
        'success',
        'warning',
        'neutralDark',
        'neutralDarker',
      ],
    },
  },
} as Meta;

export const TableCardStroy = args => <TableCard {...args} />;

TableCardStroy.args = {
  cardHeaderLeft: (
    <Button
      className={styles.badgeClassName}
      size="xSmall"
      theme="warning"
      label="Default"
    />
  ),
  bodyTitle: 'Check',
  bodySubTitle: 'Park Avenue, NY, 10001, USA',
  footerLeftContent: 'NOV 16, 2024',
  footerRightContent: 'Smit B<PERSON>kadiya',
  id: 'CheckId',
  actionItems: {
    lists: [
      { label: 'Option 1 Option' },
      { label: 'Option 2' },
      { label: 'Option 3' },
    ],
  },
  isLoading: false,
};
