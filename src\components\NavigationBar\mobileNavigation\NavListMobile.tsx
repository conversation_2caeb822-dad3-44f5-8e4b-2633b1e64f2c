// import React, { ReactNode, SyntheticEvent, useState } from 'react';
// import {
//   ChevronLeftIcon,
//   ChevronRightIcon,
// } from '@peddleon/ped-ux-react-icons';
// import variableStyles from '@styles/variables.module.css';

// import NavItemMobile from './NavItemMobile';
// import styles from './NavListMobile.module.css';

// interface NavigationItem {
//   label: string;
//   isActive?: boolean;
//   link?: string;
//   listItems?: NavigationItem[];
//   id?: string | number;
// }

// interface NavListMobileProps {
//   navigationMenuList: NavigationItem[];
//   mobileMenuButtons?: ReactNode;
//   onClick?: (
//     event: SyntheticEvent<Element, Event>,
//     id?: string | number,
//   ) => void;
//   renderMobileMenu: () => ReactNode;
//   hasCustomMenuMobile: boolean;
// }

// const NavListMobile: React.FC<NavListMobileProps> = ({
//   navigationMenuList,
//   mobileMenuButtons,
//   onClick,
//   hasCustomMenuMobile,
//   renderMobileMenu,
// }) => {
//   const [currentIndex, setCurrentIndex] = useState<number>(-1);

//   const handleItemClick = (index: number) => {
//     setCurrentIndex(index);
//   };

//   const handleBackClick = () => {
//     setCurrentIndex(-1);
//   };

//   const renderMenuItems = (menuList: NavigationItem[], isMainMenu = false) =>
//     menuList.map((item, index) => {
//       const { listItems, ...itemProps } = item;

//       return (
//         <NavItemMobile
//           key={item.id || `${index}-main`}
//           label={item.label}
//           isActive={item.isActive}
//           isSubList={currentIndex !== -1}
//           link={item.link}
//           id={item.id}
//           handleClick={e => {
//             if (!menuList[index].listItems) {
//               onClick?.(e, item?.id);
//             } else {
//               setCurrentIndex(index);
//             }
//           }}
//           onClick={() => listItems && handleItemClick(index)}
//           rightIcon={listItems && <ChevronRightIcon height={24} width={24} />}
//           leftIcon={
//             currentIndex === index &&
//             isMainMenu && <ChevronLeftIcon height={24} width={24} />
//           }
//           {...itemProps}
//         />
//       );
//     });

//   if (hasCustomMenuMobile) {
//     return <div className={styles.navMenuWrapper}>{renderMobileMenu()}</div>;
//   }

//   const handleClick = event => {
//     if (
//       event.target.tagName.toLowerCase() === 'button' ||
//       event.target.tagName.toLowerCase() === 'a'
//     ) {
//       // Your if condition
//       onClick?.(event, 'mobileMenu');
//     }
//   };

//   return (
//     <ul className={styles.navMenuWrapper}>
//       {currentIndex === -1 ? (
//         renderMenuItems(navigationMenuList, true)
//       ) : (
//         <>
//           <NavItemMobile
//             label={navigationMenuList[currentIndex]?.label}
//             leftIcon={<ChevronLeftIcon color={variableStyles.neutralDarker} />}
//             onLeftIconClick={handleBackClick}
//             className={styles.navListItemBack}
//           />
//           {renderMenuItems(navigationMenuList[currentIndex]?.listItems || [])}
//         </>
//       )}
//       {mobileMenuButtons && (
//         // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
//         <div className={styles.menuButtonContainer} onClick={handleClick}>
//           {mobileMenuButtons}
//         </div>
//       )}
//     </ul>
//   );
// };

// export default NavListMobile;
