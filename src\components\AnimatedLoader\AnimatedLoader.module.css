@value variables: "../../styles/variables.module.css";
@value black, primary, warning, danger, black20 from variables;

@value offset: 62;
@value duration: 1.4s;

.loaderIcon {
  animation: rotation duration linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(270deg);
  }
}

.dark {
  & .path {
    animation: dash duration ease-in-out infinite;
  }
}

.light {
  & .path {
    animation:
      dash duration ease-in-out infinite,
      colors 11.2s ease-in-out infinite;
  }
}

.path {
  stroke-dasharray: offset;
  stroke-dashoffset: 0;
  transform-origin: center;
}

@keyframes colors {
  0% {
    stroke: black;
  }
  25% {
    stroke: primary;
  }
  50% {
    stroke: warning;
  }
  75% {
    stroke: danger;
  }
  100% {
    stroke: black;
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: offset;
  }
  50% {
    stroke-dashoffset: 15.5; /* offset / 4 */
    transform: rotate(135deg);
  }
  100% {
    stroke-dashoffset: 62;
    transform: rotate(450deg);
  }
}

.disabled {
  stroke: black20;
  animation: none;
  & .path {
    animation: none;
  }
}
