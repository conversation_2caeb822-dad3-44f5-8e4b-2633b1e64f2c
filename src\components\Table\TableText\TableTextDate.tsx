import React from 'react';
import { EMPTY_TABLE_VALUE } from '@constants/common';
import classNames from '@utils/classNames';
import { formatDateTable } from '@utils/date';

import styles from './TableText.module.css';

interface TableTextDateProps {
  isInverted?: boolean;
  isActive?: boolean;
  customStyle?: React.CSSProperties;
  className?: string;
  date: string;
}

const TableTextDate: React.FC<TableTextDateProps> = ({
  isInverted,
  isActive,
  customStyle,
  className,
  date,
}) => {
  const formattedDate = formatDateTable(date);

  const isInvalidDate = formattedDate === 'Invalid Date';

  return (
    <div
      className={classNames(
        styles.text,
        isActive && styles.active,
        isInverted && styles.isInverted,
        className,
      )}
      style={customStyle}
    >
      {isInvalidDate || !date ? EMPTY_TABLE_VALUE : formattedDate}
    </div>
  );
};

export default TableTextDate;
