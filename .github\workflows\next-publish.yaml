name: Publish next package

on:
  push:
    branches:
      - develop

jobs:
  test:
    name: Publish next package
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Delete old next packages
        uses: smartsquaregmbh/delete-old-packages@v0.4.0
        with:
          keep: 20
          dry-run: false
          ## Next releases!
          version-pattern: "^\\d+\\.\\d+\\.\\d+-next\\.\\d+$"
          names: |
            apex

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'
          registry-url: https://npm.pkg.github.com/
          cache: 'npm'

      - name: Install dependencies
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GIT_TOKEN }}

      - name: Publish next package
        run: |
          sudo apt-get update
          sudo apt-get install -y moreutils
          VERSION=$(cat package.json | jq -r '.version')
          TAG="next.$GITHUB_RUN_NUMBER"
          jq ".version = \"$VERSION-$TAG\"" package.json | sponge package.json
          npm publish --tag next
        env:
          PR: ${{ github.event.pull_request.number }}
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
