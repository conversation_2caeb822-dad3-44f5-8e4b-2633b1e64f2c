import { FC, useEffect, useRef } from 'react';
import Modal from '@components/DialogModal/Modal';
import ModalBody from '@components/DialogModal/ModalBody';
import ModalButton from '@components/DialogModal/ModalButton';
import ModalFooter from '@components/DialogModal/ModalFooter';
import ModalHeader from '@components/DialogModal/ModalHeader';
import ModalHeading from '@components/DialogModal/ModalHeading';
import ModalIllustration from '@components/DialogModal/ModalIllustration';
import ModalPortal from '@components/DialogModal/ModalPortal';
import ModalSubHeading from '@components/DialogModal/ModalSubHeading';
import ModalTitle from '@components/DialogModal/ModalTitle';
import { ModalAction } from '@constants/components';
import useCSSTransition from '@hooks/useCSSTransition';
import useIsomorphicLayoutEffect from '@hooks/useIsomorphicLayoutEffect';
import classNames from '@utils/classNames';

import styles from './DialogModal.module.css';
import { DialogModalProps } from './DialogModal.types';

const DialogModal: FC<DialogModalProps> = ({
  name = '',
  maxWidth = 'none',
  maxHeightMobile = 'calc(100vh - 164px)',
  isAnimated = false,
  isMobileCentered = false,
  isActive,
  tracker = () => {},
  onModalOutsideClick = () => {},
  wrapperClassName = '',
  children,
}) => {
  const overlayRef = useRef<HTMLDivElement>(null);
  const dialogRef = useRef<HTMLDivElement>(null);

  const { isEntered, isExited, isEntering, isExiting } = useCSSTransition({
    isActive: isAnimated && isActive,
    enterRef: dialogRef,
    exitRef: overlayRef,
    enterPropertyName: 'transform',
    exitPropertyName: 'opacity',
  });

  useIsomorphicLayoutEffect(() => {
    if (isActive) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
  }, [isActive]);

  useEffect(() => {
    if (isActive) {
      tracker({ modalName: name, modalActionType: ModalAction.VIEW });
    }
  }, [isActive, name, tracker]);

  if ((!isActive && !isAnimated) || (!isActive && isAnimated && isExited)) {
    return null;
  }

  return (
    <ModalPortal
      onClose={onModalOutsideClick}
      className={classNames(
        styles.modal,
        isAnimated && styles.isAnimated,
        (isEntering || isEntered) && styles.isEntering,
        isExiting && styles.isExiting,
        wrapperClassName,
      )}
      overlayClassName={styles.overlay}
      overlayRef={overlayRef}
    >
      <div
        className={classNames(
          styles.dialogModal,
          isMobileCentered && styles.isMobileCentered,
          'dialogModalWrapper',
        )}
        style={{
          maxWidth,
        }}
      >
        <div
          ref={dialogRef}
          style={{ '--baseDialogModalMaxHeightMobile': maxHeightMobile }}
          className={styles.dialogWrapper}
        >
          {children}
        </div>
      </div>
    </ModalPortal>
  );
};

export default Object.assign(DialogModal, {
  Modal,
  Header: ModalHeader,
  Body: ModalBody,
  Illustration: ModalIllustration,
  SubHeading: ModalSubHeading,
  Heading: ModalHeading,
  Footer: ModalFooter,
  Button: ModalButton,
  Title: ModalTitle,
});
