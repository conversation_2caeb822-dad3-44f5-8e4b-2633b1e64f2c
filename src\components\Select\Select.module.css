@value variables: "../../styles/variables.module.css";
@value eightSpace, halfSpace, oneSpace, oneSpace, twoSpace, black, black5, black10, black35, black50, black70 from variables;
@value danger, white, primary, success from variables;
@value ease-out-expo, shadow, smallWidth, buoyantZIndex from variables;

.selectInput {
  &:hover {
    & .button {
      border-color: black35;
    }
  }
}

.disabled {
  pointer-events: none;

  & .button {
    pointer-events: none;
    background-color: black5;
    color: black35;
  }

  & .selected {
    color: black50;
  }
}

.borderless {
  height: eightSpace;
  & .container {
    height: 100%;
  }
  & .button {
    border: none;
  }
}

.selectInput.errored {
  & .button {
    border-color: danger;

    &:focus {
      border-color: danger;

      & .label {
        color: black;
      }

      & .leftWrapper {
        color: black;
      }

      & .rightWrapper {
        color: black;
      }

      & .chevronIconWrapper {
        color: black;
      }
    }
  }
}

.filled {
  & .label {
    top: 0;
    transform: translateY(8px) scale(0.75);
    line-height: 20px;
  }
}

.active {
  & .button {
    border-color: primary;

    &:hover {
      border-color: primary;
    }
  }

  & .label {
    color: primary;
  }

  & .leftWrapper {
    color: primary;
  }

  & .rightWrapper {
    color: black;
  }

  & .chevronIconWrapper {
    color: primary;
    transform: rotate(180deg);
  }
}

.selectInput {
  & .button {
    &:focus {
      outline: none;
      border-color: primary;

      & .label {
        color: primary;
      }

      & .leftWrapper {
        color: primary;
      }

      & .rightWrapper {
        color: black;
      }

      & .chevronIconWrapper {
        color: primary;
      }
    }

    &:focus-visible {
      box-shadow: none;
    }
  }
}

.container {
  position: relative;
}

.buttonContainer {
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
}

.button {
  cursor: pointer;
  width: 100%;
  height: eightSpace;
  background: white;
  border: solid 1px;
  border-color: black10;
  border-radius: 8px;
  transition: border-color 0.5s ease-out-expo;
  padding-left: twoSpace;
  padding-right: twoSpace;
}

.centerWrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

.wrapper {
  line-height: 0;
}

.leftWrapper {
  composes: wrapper;
  margin-right: oneSpace;
  color: black50;
  transition: color 0.5s ease-out-expo;
}

.rightWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  display: flex;
  flex-flow: row;
  align-items: center;
  color: black50;

  & > * {
    line-height: 0;
  }
}

.errorIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: danger;
}

.successIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: success;
}

.fetchingIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
}

.chevronIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  transform-origin: center center;
  transition: transform 0.5s ease-out-expo;
}

.label {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0.01em;
  color: black50;
  transform-origin: left center;
  will-change: top, transform, color;
  transition:
    color 0.5s ease-out-expo,
    top 0.5s ease-out-expo,
    transform 0.5s ease-out-expo;
  pointer-events: none;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selected {
  position: absolute;
  bottom: 12px;
  left: 0;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.01em;
  color: black;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menuWrapper {
  line-height: 0;
  position: relative;
  z-index: buoyantZIndex;
}

.footer {
  display: flex;
  flex-direction: column;
  text-align: left;
  padding: 0 twoSpace 0 twoSpace;
  font-size: 12px;
  line-height: 20px;
  padding-left: 0;
}

.error {
  color: danger;
}

.fallbackSelect {
  display: none;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.caption {
  color: black70;
}

.inputMenuModalContainer {
  padding-right: 0 !important;
  padding-left: 0 !important;
}
.inputMenuModal {
  z-index: 0 !important;
  position: relative !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: none;
  & > * > * {
    max-height: none !important;
  }
}

.modalHeader {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 85%;

  & input {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  & .inputWrapper {
    & .label {
      transform: translateY(-8px) scale(0.75);
    }
  }
}

.modalHeaderContainer {
  /* to override the modal default styles */
  padding-left: twoSpace !important;

  @media (max-width: smallWidth) {
    padding: 0 !important;
  }
}

.modalForCombo {
  border: 0;

  & .containerScroll {
    overflow: unset !important;
  }
}

.modalContainer {
  min-height: var(--scrollMinHeight);
}

.dialogModalWrapper {
  &.bottom {
    justify-content: flex-end;
    padding-bottom: eightSpace;
  }
  &.center {
    justify-content: center;
  }
  &.top {
    justify-content: flex-start;
    padding-top: eightSpace;
  }
}

.inputMenu:not(.fixedListWrapper) {
  @media (max-width: smallWidth) {
    top: 0 !important;
  }
}

.fixedListWrapper {
  min-width: unset;
  position: fixed;
}
