import { MutableRefObject } from 'react';

export interface ScrimsPropTypes {
  /**
   * A reference to the scrollable container that the Scrims component should monitor for overflow.
   */
  overflowRef?: MutableRefObject<HTMLElement>;
  /**
   * Specifies whether the gradient overlays should be inverted (i.e., light overlay on a dark background).
   */
  isInverted?: boolean;
  /**
   * Specifies whether the Scrims component should indicate horizontal overflow instead of vertical overflow.
   */
  isHorizontal?: boolean;
  /**
   * To add additional styling from top
   */
  topClassName?: string;
  /**
   * To add additional styling from bottom
   */
  bottomClassName?: string;
  /**
   * To add additional styling from left
   */
  leftClassName?: string;
  /**
   * To add additional styling from right
   */
  rightClassName?: string;
}
