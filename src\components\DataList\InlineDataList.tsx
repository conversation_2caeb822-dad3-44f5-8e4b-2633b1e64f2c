// import React, { CSSProperties, ReactNode } from 'react';
// import { EditIcon } from '@peddleon/ped-ux-react-icons';
// import Button from '@components/Button';
// import classNames from '@utils/classNames';

// import sharedStyles from './InlineDataList.module.css';

// interface InlineDataListProps {
//   children?: ReactNode;
//   isEditable?: boolean;
//   onEdit?: () => void;
//   isInverted?: boolean;
//   theme?: 'primary' | 'secondary';
//   withBorders?: boolean;
//   hasTopBorder?: boolean;
//   termColumnWidth?: string;
//   detailsColumnWidth?: string;
// }

// const InlineDataList: React.FC<InlineDataListProps> = ({
//   children,
//   isEditable = false,
//   onEdit,
//   isInverted = false,
//   theme = 'secondary',
//   withBorders = false,
//   hasTopBorder = false,
//   termColumnWidth = '50%',
//   detailsColumnWidth = 'auto',
// }) => {
//   const styles = {
//     '--termColumnWidth': termColumnWidth,
//     '--detailsColumnWidth': detailsColumnWidth,
//   } as CSSProperties;

//   return (
//     <dl
//       className={classNames(
//         sharedStyles.inlineDataList,
//         isInverted && sharedStyles.inverted,
//         sharedStyles[theme],
//         withBorders && sharedStyles.withBorders,
//         hasTopBorder && sharedStyles.hasTopBorder,
//       )}
//       style={styles}
//     >
//       {React.Children.map(children, child => {
//         if (child) {
//           return <div className={sharedStyles.childWrapper}>{child}</div>;
//         }
//         return null;
//       })}
//       {isEditable && (
//         <div className={sharedStyles.editButtonContainer}>
//           <Button
//             label="Edit"
//             size="small"
//             theme={isInverted ? 'greyInverted' : 'grey'}
//             right={<EditIcon height="16px" width="16px" />}
//             onClick={onEdit}
//           />
//         </div>
//       )}
//     </dl>
//   );
// };

// export default InlineDataList;
