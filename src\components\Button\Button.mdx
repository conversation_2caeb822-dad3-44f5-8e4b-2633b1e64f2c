import { <PERSON><PERSON>, <PERSON>a, <PERSON>, Controls } from '@storybook/blocks';
import * as ButtonStories from './Button.stories';

<Meta of={ButtonStories} />
<div className="markdown-content">

# Button Variants

Button is an actionable component with multiple variants. Let go through different variants along with their specifications and usage applicable in our products.


<Story />
<Controls />

### Primary Button

---

- Use the Primary Button for the key action on a page, such as submitting a form or advancing a process (e.g., Submit, Save). 
- Limit to one per view or section to maintain its emphasis.

#### **Button with icon**

- This button will display the button name along with icon.
- Ideal for buttons like “Export”, “Add” or “Schedule donation.”

#### **Implmentation**

```jsx
// Importing the Button component
import { Button } from '@peddleon/pex-ux-react-library';
```

<Canvas of={ButtonStories.Primary} />{' '}

### Transparent Button

---

- For confirmation buttons, use a transparent button for secondary actions.
- Ideal for confirmations actions like “Save and Cancel” where “Cancel” is considered as secondary.
- This is mostly used for pages with sections.

#### **Implmentation**

```jsx
// Importing the Button component
import { Button } from '@peddleon/pex-ux-react-library';
```

<Canvas of={ButtonStories.Transparent} />{' '}

### Light Button

---

- It can be used when you want to offer an alternative action without drawing too much attention.
- Use the Light Button for secondary actions, such as Cancel, Back or secondary options.
- It should have less visual weight compared to the Primary Button, by using an empty fill.

#### **Implmentation**

```jsx
// Importing the Button component
import { Button } from '@peddleon/pex-ux-react-library';
```

<Canvas of={ButtonStories.Light} />{' '}

### Small Button

---

- Use the Small Button for secondary or non-primary actions that are not central to the user's flow, like ‘Edit', 'Delete’ or 'More Info'. 
- This button will have reduced padding and font size for compact areas, typically on mobile.


#### **Implmentation**

```jsx
// Importing the Button component
import { Button } from '@peddleon/pex-ux-react-library';
```

<Canvas of={ButtonStories.Small} />{' '}

### Large Button

---

- Always use the Large Button for actions that require prominent visibility and easy access.
- Ideal for key actions such as "Start Now" "Join Us" or "Get Started."
- Best suited for desktop or larger screen layouts where a larger button is necessary to grab attention.


#### **Implmentation**

```jsx
// Importing the Button component
import { Button } from '@peddleon/pex-ux-react-library';
```

<Canvas of={ButtonStories.Large} />{' '}

### Disabled Button

---

- Use the Disabled Button to indicate that an action is currently unavailable.
- It helps to prevent users from interacting with a button that cannot perform its action.
- Ideal for actions that depend on certain conditions being met before they can be executed.


#### **Implmentation**

```jsx
// Importing the Button component
import { Button } from '@peddleon/pex-ux-react-library';
```

<Canvas of={ButtonStories.Disabled} />{' '}

### Loading Button

---

- Use the Loading Button when an action is in progress.
- It provides feedback to the user that their action is being processed - ideal for actions like Submitting," "Loading," or "Processing.”
- While isLoading is true, the button indicates as such to prevent repeated submissions.

#### **Implmentation**

```jsx
// Importing the Button component
import { Button } from '@peddleon/pex-ux-react-library';
```

<Canvas of={ButtonStories.Loading} />{' '}

### Square Button

---

- Use the Square Button when the action can be represented by an icon or for text-based actions ('e.g., Add' or 'Remove') in space-constrained areas, such as toolbars or grids.
- The button will be resized to ensure equal height and width.


#### **Implmentation**

```jsx
// Importing the Button component
import { Button } from '@peddleon/pex-ux-react-library';
```

<Canvas of={ButtonStories.Square} />{' '}

</div>
