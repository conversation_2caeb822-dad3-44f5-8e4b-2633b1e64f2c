export const MOCK_VEHICLE_YEAR_OPTIONS = [...new Array(70)]
  .map((_, index) => index + 1950)
  .map(year => ({
    label: String(year),
    value: String(year),
  }));

export const MOCK_GROUP_OPTIONS = [
  {
    label: 'Today',
    options: [
      {
        label: '9 am - 12 am CT',
        displayLabel: 'Today, 9 am - 12 am CT',
        value: 'today9Am12AmCt',
      },
      {
        label: '11 am - 1 pm CT',
        displayLabel: 'Today, 11 am - 1 pm CT',
        value: 'today11Am1PmCt',
      },
    ],
  },
  {
    label: 'Tomorrow',
    options: [
      {
        label: '9 am - 11 am CT',
        displayLabel: 'Tomorrow, 9 am - 11 am CT',
        value: 'tomorrow9Am11AmCt',
      },
      {
        label: '12 pm - 3 pm CT',
        displayLabel: 'Tomorrow, 12 pm - 3 pm CT',
        value: 'tomorrow12Pm3PmCt',
      },
    ],
  },
  {
    label: 'Sunday, 11/24/2024',
    options: [
      {
        label: '10 am - 1 pm CT',
        displayLabel: 'Sunday, 11/24/2024 10 am - 1 pm CT',
        value: 'sunday1124202410Am1PmCt',
      },
      {
        label: '2 pm - 5 pm CT',
        displayLabel: 'Sunday, 11/24/2024 2 pm - 5 pm CT',
        value: 'sunday112420242Pm5PmCt',
      },
    ],
  },
  {
    label: 'Monday, 11/25/2024',
    options: [
      {
        label: '3 pm - 6 pm CT',
        displayLabel: 'Monday, 11/25/2024 3 pm - 6 pm CT',
        value: 'monday112520243Pm6PmCt',
      },
      {
        label: '7 pm - 9 pm CT',
        displayLabel: 'Monday, 11/25/2024 7 pm - 9 pm CT',
        value: 'monday112520247Pm9PmCt',
      },
    ],
  },
  {
    label: 'Tuesday, 11/26/2024',
    options: [
      {
        label: '8 am - 10 am CT',
        displayLabel: 'Tuesday, 11/26/2024 8 am - 10 am CT',
        value: 'tuesday112620248Am10AmCt',
      },
      {
        label: '11 am - 2 pm CT',
        displayLabel: 'Tuesday, 11/26/2024 11 am - 2 pm CT',
        value: 'tuesday1126202411Am2PmCt',
      },
    ],
  },
  {
    label: 'Wednesday, 11/27/2024',
    options: [
      {
        label: '9 am - 12 pm CT',
        displayLabel: 'Wednesday, 11/27/2024 9 am - 12 pm CT',
        value: 'wednesday112720249Am12PmCt',
      },
      {
        label: '1 pm - 4 pm CT',
        displayLabel: 'Wednesday, 11/27/2024 1 pm - 4 pm CT',
        value: 'wednesday112720241Pm4PmCt',
      },
    ],
  },
  {
    label: 'Thursday, 11/28/2024',
    options: [
      {
        label: '10 am - 1 pm CT',
        displayLabel: 'Thursday, 11/28/2024 10 am - 1 pm CT',
        value: 'thursday1128202410Am1PmCt',
      },
      {
        label: '2 pm - 5 pm CT',
        displayLabel: 'Thursday, 11/28/2024 2 pm - 5 pm CT',
        value: 'thursday112820242Pm5PmCt',
      },
    ],
  },
  {
    label: 'Friday, 11/29/2024',
    options: [
      {
        label: '11 am - 2 pm CT',
        displayLabel: 'Friday, 11/29/2024 11 am - 2 pm CT',
        value: 'friday1129202411Am2PmCt',
      },
      {
        label: '3 pm - 6 pm CT',
        displayLabel: 'Friday, 11/29/2024 3 pm - 6 pm CT',
        value: 'friday112920243Pm6PmCt',
      },
    ],
  },
  {
    label: 'Saturday, 11/30/2024',
    options: [
      {
        label: '12 pm - 3 pm CT',
        displayLabel: 'Saturday, 11/30/2024 12 pm - 3 pm CT',
        value: 'saturday1130202412Pm3PmCt',
      },
      {
        label: '4 pm - 7 pm CT',
        displayLabel: 'Saturday, 11/30/2024 4 pm - 7 pm CT',
        value: 'saturday113020244Pm7PmCt',
      },
    ],
  },
  {
    label: 'Sunday, 12/01/2024',
    options: [
      {
        label: '1 pm - 4 pm CT',
        displayLabel: 'Sunday, 12/01/2024 1 pm - 4 pm CT',
        value: 'sunday120120241Pm4PmCt',
      },
      {
        label: '5 pm - 8 pm CT',
        displayLabel: 'Sunday, 12/01/2024 5 pm - 8 pm CT',
        value: 'sunday120120245Pm8PmCt',
      },
    ],
  },
  {
    label: 'Monday, 12/02/2024',
    options: [
      {
        label: '2 pm - 5 pm CT',
        displayLabel: 'Monday, 12/02/2024 2 pm - 5 pm CT',
        value: 'monday120220242Pm5PmCt',
      },
      {
        label: '6 pm - 9 pm CT',
        displayLabel: 'Monday, 12/02/2024 6 pm - 9 pm CT',
        value: 'monday120220246Pm9PmCt',
      },
    ],
  },
  {
    label: 'Tuesday, 12/03/2024',
    options: [
      {
        label: '3 pm - 6 pm CT',
        displayLabel: 'Tuesday, 12/03/2024 3 pm - 6 pm CT',
        value: 'tuesday120320243Pm6PmCt',
      },
      {
        label: '7 pm - 10 pm CT',
        displayLabel: 'Tuesday, 12/03/2024 7 pm - 10 pm CT',
        value: 'tuesday120320247Pm10PmCt',
      },
    ],
  },
  {
    label: 'Wednesday, 12/04/2024',
    options: [
      {
        label: '4 pm - 7 pm CT',
        displayLabel: 'Wednesday, 12/04/2024 4 pm - 7 pm CT',
        value: 'wednesday120420244Pm7PmCt',
      },
      {
        label: '8 pm - 11 pm CT',
        displayLabel: 'Wednesday, 12/04/2024 8 pm - 11 pm CT',
        value: 'wednesday120420248Pm11PmCt',
      },
    ],
  },
  {
    label: 'Thursday, 12/05/2024',
    options: [
      {
        label: '5 pm - 8 pm CT',
        displayLabel: 'Thursday, 12/05/2024 5 pm - 8 pm CT',
        value: 'thursday120520245Pm8PmCt',
      },
      {
        label: '9 pm - 12 am CT',
        displayLabel: 'Thursday, 12/05/2024 9 pm - 12 am CT',
        value: 'thursday120520249Pm12AmCt',
      },
    ],
  },
];

export const MOCK_VEHICLE_MAKE_OPTIONS = [
  {
    label: 'Acura',
    value: 'Acura',
  },
  {
    label: 'Alfa Romeo',
    value: 'Alfa Romeo',
  },
  {
    label: 'Aston Martin',
    value: 'Aston Martin',
  },
  {
    label: 'Audi',
    value: 'Audi',
  },
  {
    label: 'Bentley',
    value: 'Bentley',
  },
  {
    label: 'BMW',
    value: 'BMW',
  },
  {
    label: 'Buick',
    value: 'Buick',
  },
  {
    label: 'Cadillac',
    value: 'Cadillac',
  },
  {
    label: 'Chevrolet',
    value: 'Chevrolet',
  },
  {
    label: 'Chrysler',
    value: 'Chrysler',
  },
  {
    label: 'Dodge',
    value: 'Dodge',
  },
  {
    label: 'Ferrari',
    value: 'Ferrari',
  },
  {
    label: 'Fiat',
    value: 'Fiat',
  },
  {
    label: 'Ford',
    value: 'Ford',
  },
  {
    label: 'GMC',
    value: 'GMC',
  },
  {
    label: 'Honda',
    value: 'Honda',
  },
  {
    label: 'Hyundai',
    value: 'Hyundai',
  },
  {
    label: 'Infiniti',
    value: 'Infiniti',
  },
  {
    label: 'Isuzu',
    value: 'Isuzu',
  },
  {
    label: 'Jaguar',
    value: 'Jaguar',
  },
  {
    label: 'Jeep',
    value: 'Jeep',
  },
  {
    label: 'Kia',
    value: 'Kia',
  },
  {
    label: 'Lamborghini',
    value: 'Lamborghini',
  },
  {
    label: 'Land Rover',
    value: 'Land Rover',
  },
  {
    label: 'Lexus',
    value: 'Lexus',
  },
  {
    label: 'Lincoln',
    value: 'Lincoln',
  },
  {
    label: 'Lotus',
    value: 'Lotus',
  },
  {
    label: 'Maserati',
    value: 'Maserati',
  },
  {
    label: 'Mazda',
    value: 'Mazda',
  },
  {
    label: 'McLaren Automotive',
    value: 'McLaren Automotive',
  },
  {
    label: 'Mercedes-Benz',
    value: 'Mercedes-Benz',
  },
  {
    label: 'Mini',
    value: 'Mini',
  },
  {
    label: 'Mitsubishi',
    value: 'Mitsubishi',
  },
  {
    label: 'Nissan',
    value: 'Nissan',
  },
  {
    label: 'Porsche',
    value: 'Porsche',
  },
  {
    label: 'Ram',
    value: 'Ram',
  },
  {
    label: 'Rolls-Royce',
    value: 'Rolls-Royce',
  },
  {
    label: 'Smart',
    value: 'Smart',
  },
  {
    label: 'Subaru',
    value: 'Subaru',
  },
  {
    label: 'Tesla',
    value: 'Tesla',
  },
  {
    label: 'Toyota',
    value: 'Toyota',
  },
  {
    label: 'Volkswagen',
    value: 'Volkswagen',
  },
  {
    label: 'Volvo',
    value: 'Volvo',
  },
];

export const MOCK_VEHICLE_MODEL_OPTIONS = [
  {
    label: 'Wrangler',
    value: 'Wrangler',
  },
  {
    label: 'Grand Cherokee',
    value: 'Grand Cherokee',
  },
  {
    label: 'Cherokee',
    value: 'Cherokee',
  },
  {
    label: 'Compass',
    value: 'Compass',
  },
  {
    label: 'Renegade',
    value: 'Renegade',
  },
];

export const MOCK_BOX_OPTIONS = [
  {
    id: 'this',
    label: 'I would choose this one',
  },
  {
    id: 'or-this',
    label: 'Or this one',
  },
  {
    id: 'maybe-this',
    label: 'Maybe this one though',
  },
];
