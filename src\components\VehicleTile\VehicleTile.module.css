@value typography: "../../styles/typography.module.css";
@value caption, h6Strong, h5Strong, h3Strong, h4Strong, bodyBase from typography;
@value variables: "../../styles/variables.module.css";
@value neutralDarkest, halfSpace, largeWidth, twoSpace, fourSpace, threeSpace, captionLineHeight, captionFontSize, h4FontSize, h4LineHeight, h6FontSize, h6LineHeight, white, black, black10, black70, primary from variables;

.tag {
  min-height: 0;
}

.tile {
  & .metaData {
    font-size: captionFontSize;
    line-height: captionLineHeight;
    color: black70;
    word-break: break-word;
  }

  @media (max-width: largeWidth) {
    border: solid 1px black10;
    padding: threeSpace fourSpace;
    border-radius: 8px;
    background: white;
  }
}

.statusTag {
  margin-bottom: twoSpace;
}
.year {
  font-size: h6FontSize;
  line-height: h6LineHeight;
  color: black;
}

.makeModel {
  font-size: h4FontSize;
  line-height: h4LineHeight;
  color: black;
}

.detailsWithVIN {
  column-count: 2;

  @media (max-width: largeWidth) {
    display: block;
  }
}

.detailsWithoutVIN {
  column-count: 1;

  @media (max-width: largeWidth) {
    display: block;
  }
}
.primaryStyle {
  composes: h5Strong;
}

.displayStyle {
  composes: h3Strong;
}

.link {
  font-size: inherit;
  line-height: inherit;
}

.salesforceLink {
  color: primary;
  composes: caption;
}

.offerIdBtnWrapper {
  padding: twoSpace 0 halfSpace 0;
}

.statusTag {
  min-height: 0;
  & :first-child {
    margin-left: 0;
  }
}

.statusLabel {
  color: neutralDarkest;
  font-weight: 600;
  text-transform: uppercase;
}
