// import type { ReactElement, ReactEventHandler, ReactNode } from 'react';
// import React, { useCallback, useRef } from 'react';
// import {
//   ChevronDownIcon,
//   MinusIcon,
//   PlusIcon,
// } from '@peddleon/ped-ux-react-icons';
// import classNames from '@utils/classNames';

// import variableStyles from '../../styles/variables.module.css';
// import { Scrims } from '..';
// import { AccordionTheme } from './Accordion';
// import styles from './Accordion.module.css';

// export interface AccordionItem {
//   label: string;
//   left?: ReactNode;
//   content: ReactNode;
//   isDisabled?: boolean;
//   isActive?: boolean;
//   right?: ReactNode;
// }

// interface AccordionListItemProps {
//   item: AccordionItem;
//   theme: AccordionTheme;
//   isOpen: boolean;
//   onClick: ReactEventHandler;
//   labelClassName?: string;
//   itemClassName?: string;
//   isBorderless?: boolean;
//   buttonClassName?: string;
//   contentClassName?: string;
//   hasPlusMinusIcon?: boolean;
//   right?: ReactNode;
// }

// const AccordionListItem = ({
//   item,
//   theme = 'primaryMain',
//   isOpen = false,
//   onClick,
//   labelClassName = null,
//   itemClassName = null,
//   isBorderless = false,
//   buttonClassName = null,
//   contentClassName = null,
//   hasPlusMinusIcon = true,
//   right = null,
// }: AccordionListItemProps) => {
//   const { label, content, left, isDisabled, isActive, right: itemRight } = item;
//   const listRef = useRef(null);

//   let icon: ReactElement;
//   const hasDisplayVariantV1 = theme.includes('displayV1');
//   if (hasDisplayVariantV1) {
//     const Component = isOpen ? MinusIcon : PlusIcon;
//     icon = (
//       <Component color={variableStyles.black} height="1.25em" width="1.25em" />
//     );
//   } else if (theme.includes('display') && isOpen) {
//     icon = (
//       <div className={styles.customIconWrapper}>
//         <div className={styles.customIcon}>
//           <div className={styles.horizontalLine} />
//         </div>
//       </div>
//     );
//   } else if (theme.includes('display')) {
//     icon = (
//       <div className={styles.customIconWrapper}>
//         <div className={styles.customIcon}>
//           <div className={styles.verticalLine} />
//           <div className={styles.horizontalLine} />
//         </div>
//       </div>
//     );
//   } else if (theme.includes('primary')) {
//     icon = <ChevronDownIcon height="1.25em" width="1.25em" />;
//   } else if (theme.includes('secondary')) {
//     icon = <ChevronDownIcon height="1.25em" width="1.25em" />;
//   }

//   if (hasPlusMinusIcon && theme.includes('primary')) {
//     icon = !isOpen ? (
//       <PlusIcon height="1.25em" width="1.25em" />
//     ) : (
//       <MinusIcon height="1.25em" width="1.25em" />
//     );
//   }

//   const handleKeyPress = useCallback(
//     (e: React.KeyboardEvent) => {
//       if (e.key === 'Enter' || e.key === ' ') {
//         onClick(e);
//       }
//     },
//     [onClick],
//   );

//   /* eslint-disable jsx-a11y/no-noninteractive-element-to-interactive-role */
//   return (
//     <li
//       className={classNames(
//         styles.listItem,
//         styles[theme],
//         isOpen && styles.isOpen,
//         isDisabled && styles.isDisabled,
//         isActive && styles.isActive,
//         isBorderless && styles.isBorderless,
//         itemClassName,
//       )}
//       tabIndex={0}
//       role="button"
//     >
//       <div
//         className={classNames(styles.button, buttonClassName)}
//         onClick={onClick}
//         role="button"
//         tabIndex={0}
//         onKeyPress={handleKeyPress}
//       >
//         <h3 className={styles.listItemLabel}>
//           {left}
//           <span className={labelClassName}>{label}</span>
//         </h3>
//         <div className={styles.iconWrapper}>
//           {right}
//           {itemRight}
//           <div className={classNames(styles.icon, isOpen && styles.isOpen)}>
//             {icon}
//           </div>
//         </div>
//       </div>
//       <div className={styles.contentWrapperRoot}>
//         <div
//           ref={listRef}
//           className={classNames(hasDisplayVariantV1 && styles.scrollableBody)}
//         >
//           <div className={classNames(styles.contentWrapper, contentClassName)}>
//             {isOpen && content}
//           </div>
//         </div>
//         <Scrims
//           bottomClassName={styles.bottomClassName}
//           overflowRef={listRef}
//         />
//       </div>
//     </li>
//   );
// };

// export default AccordionListItem;
