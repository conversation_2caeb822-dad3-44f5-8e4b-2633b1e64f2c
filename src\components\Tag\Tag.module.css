@value variables: "../../styles/variables.module.css";
@value typography: "../../styles/typography.module.css";
@value oneSpace from variables;
@value black5, black35 from variables;
@value white5, white35 from variables;
@value ease-out-expo from variables;
@value overline from typography;

.tag {
  position: relative;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px;
  min-height: 36px;

  &.withHorizontalPadding {
    padding-left: oneSpace;
    padding-right: oneSpace;
  }

  &.interactive {
    cursor: pointer;
    transition:
      background-color 0.5s ease-out-expo,
      border-color 0.5s ease-out-expo,
      color 0.5s ease-out-expo;
  }

  &:not(.interactive) {
    pointer-events: none;

    &.transparentTheme {
      color: inherit;
    }
  }

  &:disabled,
  &.disabled {
    pointer-events: none;
    background-color: black5;
    color: black35;
    border-color: black5;
  }

  &.disabledInverted:disabled {
    background-color: white5;
    color: white35;
    border-color: white5;
  }

  &:focus {
    outline: none;
  }
}

.wrapper {
  line-height: 0;
  transition: opacity 0.5s ease-out-expo;
}

.leftWrapper {
  composes: wrapper;
  margin-right: oneSpace;
}

.rightWrapper {
  composes: wrapper;
  margin-left: oneSpace;
}

.fetchingIconWrapper {
  composes: wrapper;
  position: absolute;
  top: 50%;
  left: 50%;
  opacity: 0;
  transform: translate(-50%, -50%);
}

.label {
  composes: overline;
  font-weight: 700;
  margin-top: 1px;
  text-transform: inherit;
}

/* variants */
.fullWidth {
  width: 100%;
}

.fetching {
  pointer-events: none;

  & .leftWrapper,
  & .rightWrapper,
  & .label {
    opacity: 0;
  }

  & .fetchingIconWrapper {
    opacity: 1;
  }
}
