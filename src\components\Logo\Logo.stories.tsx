import Logo, { LogoMark } from '@components/Logo';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

export default {
  title: 'Components/Utility',
};

export const LogoStory = ({ color }: { color: string }) => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
    }}
  >
    <ComponentGrid countColumns={2}>
      <ComponentTile label="Full Logo">
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Logo color={color} />
        </div>
      </ComponentTile>
      <ComponentTile label="Full Logo / Fluid">
        <div
          style={{
            width: '20vw',
            margin: '0px auto',
            height: '100%',
          }}
        >
          <Logo color={color} isFullWidth />
        </div>
      </ComponentTile>
      <ComponentTile label="Logo Mark">
        <LogoMark color={color} width={200} />
      </ComponentTile>
      <ComponentTile label="Logo Mark Inverted">
        <LogoMark color={color} width={125} isInverted />
      </ComponentTile>
    </ComponentGrid>
  </div>
);

LogoStory.args = {
  color: variableStyles.black,
};
