/* eslint-disable no-console */
import { useEffect, useState } from 'react';
import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta } from '@storybook/react';
import variableStyles from '@styles/variables.module.css';

import DialogModal from './DialogModal';
import { DialogModalStoryProps } from './DialogModal.types';

/**
 *
 * <br/>

## Overview

The DialogModal component is built using the Compound design pattern. 
To construct the entire modal from an integration perspective, developers need to combine hierarchies as shown in the Storybook.

## Usage

Import the component into your React application:

```jsx
import { DialogModal } from '@peddleon/ped-ux-react-library';```
 

 */
const meta: Meta<typeof DialogModal> = {
  component: DialogModal,
  title: 'Components/DialogModal',
  tags: ['autodocs'],
  parameters: {
    docs: {
      toc: true,
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
};

export const DialogModalStory = ({
  isActive,
  maxHeightMobile,
  maxWidth,
  isAnimated,
  isMobileCentered,
  title,
  heading,
  subHeading,
  body,
  acceptLabel,
  isFullWidthButton,
}: DialogModalStoryProps) => {
  const [isModalActive, setIsModalActive] = useState(isActive);

  useEffect(() => {
    setIsModalActive(isActive);
  }, [isActive]);

  useEffect(() => {
    if (isModalActive) {
      document.body.style.overflow = 'auto';
    }
  }, [isModalActive]);

  return (
    <div
      style={{
        padding: variableStyles.oneSpace,
        height: '600px',
      }}
    >
      {!isModalActive && (
        <button
          type="button"
          style={{ cursor: 'pointer' }}
          onClick={() => setIsModalActive(true)}
        >
          {' '}
          Show Dialog{' '}
        </button>
      )}
      <DialogModal
        maxWidth={maxWidth}
        isActive={isModalActive}
        isMobileCentered={isMobileCentered}
        maxHeightMobile={maxHeightMobile}
        isAnimated={isAnimated}
        onModalOutsideClick={() => {
          console.log('On Modal Outside Clicked!!');
          setIsModalActive(false);
        }}
      >
        <DialogModal.Modal
          onClose={() => {
            console.log('On Modal Close Icon Clicked!!');
            setIsModalActive(false);
          }}
          hasDialogWrapperStyles
        >
          <DialogModal.Header>
            <DialogModal.Title>{title}</DialogModal.Title>
            <DialogModal.Heading>{heading}</DialogModal.Heading>
            <DialogModal.SubHeading>{subHeading}</DialogModal.SubHeading>
          </DialogModal.Header>
          <DialogModal.Body>
            <p>{body}</p>
          </DialogModal.Body>
          <DialogModal.Footer>
            <DialogModal.Button
              label={acceptLabel}
              onClick={() => console.log('On Accept')}
              theme="warning"
              isFullWidthButton={isFullWidthButton}
            />
          </DialogModal.Footer>
        </DialogModal.Modal>
      </DialogModal>
    </div>
  );
};

DialogModalStory.args = {
  heading: 'ready to accept?',
  title: 'almost there',
  subHeading: '2008 Honda Civic Sedan LX',
  body: 'We can save your offer of $1,902 for 3 days, allowing you to come back and accept later. Feeling generous? You can also donate your car to a nonprofit of your choice.',
  acceptLabel: 'Save Offer for $1,902',
  declineLabel: 'Let’s donate my car',
  isFullWidthButton: true,
};

DialogModalStory.argTypes = {
  heading: {
    type: 'string',
    description:
      '[ Storybook Prop ] : To set a heading within the modal, use <DialogModal.Header />',
  },
  title: {
    type: 'string',
    description:
      '[ Storybook Prop ] : To set a title within the modal, use <DialogModal.Title />',
  },
  subHeading: {
    type: 'string',
    description:
      '[ Storybook Prop ] : To set a sub heading within the modal, use <DialogModal.SubHeading />',
  },
  body: {
    type: 'string',
    description:
      '[ Storybook Prop ] : To set a body within the modal, use <DialogModal.Body />',
  },
  acceptLabel: {
    type: 'string',
    description:
      '[ Storybook Prop ] : To set a label for an accept button, use <DialogModal.Button />',
  },
  declineLabel: {
    type: 'string',
    description:
      '[ Storybook Prop ] : To set a label for a reject/decline button, use <DialogModal.Button />',
  },
  isFullWidthButton: {
    type: 'boolean',
    description:
      '[ Storybook Prop ] : To set a button that occupies all available space, set this prop to true',
  },
};

export default meta;
