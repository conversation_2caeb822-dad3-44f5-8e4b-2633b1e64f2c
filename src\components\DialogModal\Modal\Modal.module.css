@value variables: "../../../styles/variables.module.css";
@value halfSpace, twoSpace, eightSpace, neutralDark, tenSpace, smallWidth, white, oneSpace, threeSpace, fiveSpace, shadow, aboveZIndex from variables;

.modal {
  border-radius: twoSpace;
  box-shadow: shadow;
  background-color: white;
  pointer-events: auto;
  position: relative;

  &.hasMaxHeight {
    max-height: 95vh;
    overflow: auto;
  }

  &.isBorderless {
    border: none;
    border-radius: 0;
  }
  &.isShadowless {
    box-shadow: none;
  }
  &.hasPadding {
    padding-top: fiveSpace;
    padding-right: threeSpace;
    padding-bottom: threeSpace;
    padding-left: threeSpace;
  }
}

.isScrollable {
  @media (min-width: smallWidth) {
    overflow: auto;
    max-height: 98vh;
  }

  &::-webkit-scrollbar {
    width: halfSpace;
  }

  &::-webkit-scrollbar-thumb {
    background-color: neutralDark;
  }
}

.closeIconButtonWrapper {
  position: absolute;
  top: oneSpace;
  right: oneSpace;
  z-index: aboveZIndex;
  @media (max-width: smallWidth) {
    top: halfSpace;
    right: halfSpace;
  }
}

.backIconButtonWrapper {
  position: absolute;
  top: oneSpace;
  left: oneSpace;
}

.dialogWrapper {
  --mobileDialogScrollHeadingHeight: 56px;
  padding-left: tenSpace;
  padding-right: tenSpace;
  padding-top: fiveSpace;
  padding-bottom: eightSpace;

  @media (max-width: smallWidth) {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 0;
    padding-top: var(--mobileDialogScrollHeadingHeight);
    max-height: var(--baseDialogModalMaxHeightMobile);
    display: flex;
    flex-direction: column;
  }
}
