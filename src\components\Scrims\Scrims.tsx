import React, { useCallback, useState } from 'react';
import useAnimationFrame from '@hooks/useAnimationFrame';
import classNames from '@utils/classNames';
import isElementOverflowing from '@utils/isElementOverflowing';

import styles from './Scrims.module.css';
import { ScrimsPropTypes } from './Scrims.types';

const SCROLL_TRIGGER = 30;
/**
 * The Scrims component adds gradient overlays to indicate overflow in a scrollable container.
 * It dynamically detects overflow in the specified container and applies gradient overlays accordingly.
 *
 * @param {Object} props - The props for the Scrims component.
 * @param {React.RefObject} props.overflowRef - Ref object for the scrollable container to monitor overflow.
 * @param {boolean} [props.isInverted=false] - Optional. Specifies whether the gradient overlays should be inverted (dark mode).
 * @param {boolean} [props.isHorizontal=false] - Optional. Specifies whether the scrollable container is horizontally scrollable.
 * @returns {JSX.Element} - The rendered Scrims component.
 */
const Scrims = ({
  overflowRef,
  isInverted = false,
  isHorizontal = false,
  topClassName = null,
  bottomClassName = null,
  leftClassName = null,
  rightClassName = null,
}: ScrimsPropTypes) => {
  const [state, setState] = useState({
    hasTopOverflow: false,
    hasBottomOverflow: false,
    hasLeftOverflow: false,
    hasRightOverflow: false,
  });

  useAnimationFrame(
    useCallback(() => {
      if (overflowRef.current) {
        const {
          scrollTop,
          scrollHeight,
          offsetHeight,
          scrollLeft,
          scrollWidth,
          offsetWidth,
        } = overflowRef.current;

        const scrollTopMax = scrollHeight - offsetHeight;
        const scrollLeftMax = scrollWidth - offsetWidth;
        const isOverflowing = isElementOverflowing(overflowRef.current);

        const computedHorizontalScrollTrigger = Math.min(
          scrollWidth - offsetWidth,
          SCROLL_TRIGGER,
        );
        const computedVerticalScrollTrigger = Math.min(
          scrollHeight - offsetHeight,
          SCROLL_TRIGGER,
        );

        if (!isOverflowing) {
          setState({
            hasTopOverflow: false,
            hasBottomOverflow: false,
            hasLeftOverflow: false,
            hasRightOverflow: false,
          });
        } else if (
          isHorizontal &&
          scrollLeft > scrollLeftMax - computedHorizontalScrollTrigger
        ) {
          setState({
            hasTopOverflow: false,
            hasBottomOverflow: false,
            hasLeftOverflow: true,
            hasRightOverflow: false,
          });
        } else if (
          isHorizontal &&
          scrollLeft < computedHorizontalScrollTrigger
        ) {
          setState({
            hasTopOverflow: false,
            hasBottomOverflow: false,
            hasLeftOverflow: false,
            hasRightOverflow: true,
          });
        } else if (!isHorizontal && scrollTop < computedVerticalScrollTrigger) {
          setState({
            hasTopOverflow: false,
            hasBottomOverflow: true,
            hasLeftOverflow: false,
            hasRightOverflow: false,
          });
        } else if (
          !isHorizontal &&
          scrollTop > scrollTopMax - computedVerticalScrollTrigger
        ) {
          setState({
            hasTopOverflow: true,
            hasBottomOverflow: false,
            hasLeftOverflow: false,
            hasRightOverflow: false,
          });
        } else {
          setState({
            hasTopOverflow: !isHorizontal && offsetHeight < scrollHeight,
            hasBottomOverflow: !isHorizontal && offsetHeight < scrollHeight,
            hasLeftOverflow: !!isHorizontal,
            hasRightOverflow: !!isHorizontal,
          });
        }
      }
    }, [isHorizontal, overflowRef]),
  );

  return (
    <>
      <div
        className={classNames(
          styles.topGradient,
          state.hasTopOverflow && styles.visible,
          isInverted && styles.isInverted,
          topClassName,
        )}
      />
      <div
        className={classNames(
          styles.bottomGradient,
          state.hasBottomOverflow && styles.visible,
          isInverted && styles.isInverted,
          bottomClassName,
        )}
      />
      <div
        className={classNames(
          styles.leftGradient,
          state.hasLeftOverflow && styles.visible,
          isInverted && styles.isInverted,
          leftClassName,
        )}
      />
      <div
        className={classNames(
          styles.rightGradient,
          state.hasRightOverflow && styles.visible,
          isInverted && styles.isInverted,
          rightClassName,
        )}
      />
    </>
  );
};

export default Scrims;
