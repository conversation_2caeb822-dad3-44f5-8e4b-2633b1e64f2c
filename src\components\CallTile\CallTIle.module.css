@value variables: "../../styles/variables.module.css";
@value bodySmallFontSize, oneSpace, twoSpace, mediumWidth, largeWidth from variables;
@value typography: "../../styles/typography.module.css";
@value caption from typography;

.supportFooter {
  padding: 0 twoSpace;

  @media (max-width: mediumWidth) {
    padding: 0;
  }
}

.supportContainer {
  display: inline-flex;
  align-items: center;
  justify-content: center;

  @media (max-width: largeWidth) {
    padding: oneSpace twoSpace;
  }
}

.phoneIcon {
  height: 24px;
  margin-right: twoSpace;
}

.supportLabel {
  composes: caption;
}

.supportNumber {
  font-size: bodySmallFontSize;
  font-weight: 700;
}
