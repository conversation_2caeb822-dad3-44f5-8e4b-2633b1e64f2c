import variableStyles from '@styles/variables.module.css';

import ContainerComponent from './Container';

export default {
  title: 'Layouts',
};

export const Container = ({
  backgroundColor,
  mobileBackgroundColor,
  isFull,
  isWide,
}: {
  backgroundColor?: string;
  mobileBackgroundColor?: string;
  isFull?: boolean;
  isWide?: boolean;
}) => (
  <ContainerComponent
    isFull={isFull}
    isWide={isWide}
    backgroundColor={backgroundColor}
    mobileBackgroundColor={mobileBackgroundColor}
  >
    <div
      style={{
        paddingTop: '200px',
        paddingBottom: '200px',
      }}
    />
  </ContainerComponent>
);

Container.args = {
  isFull: false,
  isWide: false,
  backgroundColor: variableStyles.primary,
  mobileBackgroundColor: variableStyles.primary,
};
