import { useRef } from 'react';
import useIsomorphicLayoutEffect from '@hooks/useIsomorphicLayoutEffect';

function useAnimationFrame(callback: () => void) {
  const callbackRef = useRef(callback);
  useIsomorphicLayoutEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const frameRef = useRef<number>();

  const loop = () => {
    frameRef.current = requestAnimationFrame(loop);
    const cb = callbackRef.current;
    cb();
  };

  useIsomorphicLayoutEffect(() => {
    frameRef.current = requestAnimationFrame(loop);
    return () => cancelAnimationFrame(frameRef.current);
  }, []);
}

export default useAnimationFrame;
