import EnvironmentFrameComponent, {
  EnvironmentFrameProps,
} from './EnvironmentFrame';

export default {
  title: 'Layouts',
};

export const EnvironmentFrame = ({
  label,
  isCollapsed,
  isOffset,
}: EnvironmentFrameProps) => (
  <EnvironmentFrameComponent
    label={label}
    isCollapsed={isCollapsed}
    isOffset={isOffset}
  />
);

EnvironmentFrame.args = {
  label: 'Environment',
  isCollapsed: false,
  isOffset: false,
};
