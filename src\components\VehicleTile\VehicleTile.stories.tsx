import { Meta } from '@storybook/react';

import VehicleTile from './VehicleTile';
import { VehicleTilePropTypes } from './VehicleTile.types';

/**
 * The VehicleTile component displays information about a vehicle in a styled tile format.
 * It includes details such as the vehicle's year, make, model, trim, VIN, status, and location.
 *
 * ## Overview
 *
 * The VehicleTile component is used to present essential information about a vehicle, making it
 * suitable for use in various contexts within your application.
 * It provides a clean and organized layout for displaying vehicle details.
 *
 * ## Usage
 *
 * To use the VehicleTile component in your React application, import it from the appropriate directory and include it in your JSX.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { VehicleTile } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the VehicleTile component within your application, passing the necessary props:
 *
 * ```jsx
 * <VehicleTile
 *   vehicle={{
 *     year: { label: '2007', id: '2007' },
 *     make: { label: 'Chevrolet', id: 'chevrolet' },
 *     model: { label: 'Malibu', id: 'malibu' },
 *     trim: { label: 'LS w/1FL', id: 'lSw/1FL' },
 *     VIN: 'JH4TB2H26CC000000',
 *   }}
 *   status={{
 *     id: 0,
 *     label: 'Assigned',
 *     value: 'assigned',
 *     theme: 'warning',
 *   }}
 *   location={{
 *     city: 'Madison',
 *     stateCode: 'WI',
 *   }}
 * />
 * ```
 *
 * This will render the VehicleTile component with the specified vehicle information and status.
 */
const meta: Meta<typeof VehicleTile> = {
  title: 'Components/Tiles/Vehicle Tile',
  tags: ['autodocs'],
  component: VehicleTile,
};

export const VehicleTileStroy = ({ ...restProps }: VehicleTilePropTypes) => (
  <div style={{ minWidth: '320px', maxWidth: '380px', padding: '1rem' }}>
    <VehicleTile {...restProps} />
  </div>
);

VehicleTileStroy.args = {
  vehicle: {
    year: { label: '2007', id: '2007' },
    make: { label: 'Chevrolet', id: 'chevrolet' },
    model: { label: 'Malibu', id: 'malibu' },
    trim: { label: 'LS w/1FL', id: 'lSw/1FL' },
    VIN: 'JH4TB2H26CC000000',
  },
  status: {
    id: 0,
    label: 'Assigned',
    value: 'assigned',
    theme: 'warning',
  },
  location: {
    city: 'Madison',
    stateCode: 'WI',
  },
};

export default meta;
