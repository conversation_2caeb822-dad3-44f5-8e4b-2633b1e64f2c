import { Description, Primary, Subtitle, Title } from '@storybook/blocks';
import { Meta } from '@storybook/react';
import useForm from '@hooks/useForm';
import variableStyles from '@styles/variables.module.css';

import Form from './Form';
import <PERSON>Field from './FormField';

const getMockOptions = prefixId => [
  {
    id: `${prefixId}_this`,
    label: 'I would choose this one',
  },
  {
    id: `${prefixId}_or-this`,
    label: 'Or this one',
  },
  {
    id: `${prefixId}_maybe-this`,
    label: 'Maybe this one though',
  },
];

const ERROR_MESSAGES = {
  name: {
    required: 'value is missing', // required
    rangeOverflow: 'max value should not be greater than 20', // maxValue
    stepMismatch: '', // step mismatch
    tooLong: '', // maxLength
    tooShort: '', // minLength
    rangeUnderflow: '', // minValue
    typeMismatch: '', // typeError
  },
  email: {
    required: 'value is missing',
  },
};

export const FormComponent = () => {
  const formState = useForm({
    fields: {
      name: '',
      email: '',
      select: '',
    },
    errorMessages: ERROR_MESSAGES,
  });

  const { register, errors, state } = formState;

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
        margin: variableStyles.twoSpace,
        border: `1px solid ${variableStyles.black20Opaque}`,
        borderRadius: '15px',
      }}
    >
      <Form
        register={register}
        formState={state}
        errors={errors}
        {...formState}
      >
        <div
          style={{
            margin: variableStyles.threeSpace,
          }}
        >
          <FormField label="email" name="email" required />
        </div>
        <div
          style={{
            margin: variableStyles.threeSpace,
          }}
        >
          <FormField
            caption="Example Caption"
            items={[
              { value: 'option1', label: 'Option 1' },
              { value: 'option2', label: 'Option 2' },
              { value: 'option3', label: 'Option 3' },
            ]}
            name="select"
            type="selectSearch"
            label="Label"
            required
          />
        </div>

        <div
          style={{
            margin: variableStyles.threeSpace,
          }}
        >
          <FormField
            options={getMockOptions('story')}
            type="radioGroup"
            id="html"
            label="test"
            alignment="left"
            direction="row"
            isInButtonShape
            name="radiogroup"
          />
        </div>
      </Form>
    </div>
  );
};
/**
 * ### Managing Form State with useForm Hook
 *
 * The `useForm` hook serves as a comprehensive tool for handling form-related functionalities such as state management, validation, and submission.
 *
 * ```jsx
 * import { useForm } from '@peddleon/ped-ux-react-library';
 *
 * const MyFormComponent = () => {
 *   const formState = useForm({
 *     fields: {
 *       name: '',
 *       email: '',
 *       select: '',
 *     },
 *     errorMessages: ERROR_MESSAGES,
 *   });
 * ```
 *
 * To integrate the form field component into your form, it must be wrapped within the `Form` component.
 *
 * ```jsx
 * import { useForm, Form } from '@peddleon/ped-ux-react-library';
 *
 * <Form {...formState}>
 *   <FormField label="Email" name="email" required />
 * </Form>
 * ```
 *
 * pass the error object with mentioned below keys and your custom error messages
 *
 * By default, the following error messages are available for form validation:
 *
 * - `required`: Indicates that a field is required. Example: "value is missing"
 * - `rangeOverflow`: Denotes that the entered value exceeds the maximum allowed value. Example: "max value should not be greater than described"
 * - `stepMismatch`: Specifies a step mismatch error.
 * - `tooLong`: Indicates that the input exceeds the maximum length.
 * - `tooShort`: Denotes that the input is too short.
 * - `rangeUnderflow`: Specifies that the value is below the minimum allowed value.
 * - `typeMismatch`: Denotes an invalid input type.
 */

export default {
  /**
   * The title of the component in Storybook.
   */
  title: 'Components/Form',
  /**
   * The component to be documented.
   */
  component: Form,
  /**
   * Tags to categorize the component for documentation purposes.
   */
  tags: ['autodocs'],
  /**
   * Arguments types for the component's props to generate controls in Storybook.
   */
  argTypes: {
    // You may add more controls for other props as needed
  },
  /**
   * Documentation parameters for the component.
   */
  parameters: {
    docs: {
      /**
       * Table of contents (TOC) visibility in the documentation page.
       */
      toc: true,
      /**
       * Custom page layout for the documentation.
       */
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
        </>
      ),
    },
  },
  render: FormComponent,
} as Meta;
