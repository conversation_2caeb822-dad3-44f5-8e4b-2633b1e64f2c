import { useEffect } from 'react';

function useKeyDown(key: string, callback: () => void, targetRef = null) {
  useEffect(() => {
    const handler = event => {
      // add aditional argument targetType here to check whether keydown event is same as the event target
      // and we need to make sure that anywhere else without targetType will still works as expected current behavior
      // We pass targetType value is only for Tabs component now, since arrowLeft, arrowRight event may also happens at input field as well
      if (
        (!targetRef && event.key === key) ||
        (targetRef && targetRef.includes(event.target) && event.key === key)
      ) {
        event.preventDefault();
        callback();
      }
    };

    window.addEventListener('keydown', handler);
    return () => {
      window.removeEventListener('keydown', handler);
    };
  }, [callback, key, targetRef]);
}

export default useKeyDown;
