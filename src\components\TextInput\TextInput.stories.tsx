import { ChangeEvent, useEffect, useState } from 'react';
import { Meta } from '@storybook/react';
import Field<PERSON>abel from '@components/FieldLabel';
import TextInput from '@components/TextInput';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import typographyStyles from '@styles/typography.module.css';
import variableStyles from '@styles/variables.module.css';

import { TextInputProps } from './TextInput.types';

/**
 * The TextInput component provides an input interface for users to enter text, with various customization options available.
 *
 * ## Overview
 *
 * The TextInput component allows developers to create input fields for text entry in their React applications. It supports different types of inputs, such as text, email, password, and more, and provides flexibility in styling and behavior.
 *
 * ## Usage
 *
 * To use the TextInput component in your React application, import it from the appropriate directory and render it with the desired props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { TextInput } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the TextInput component in your JSX:
 *
 * ```jsx
 * <TextInput
 *   label="Label"
 *   maxLength={50}
 *   onChange={() => {}}
 *   placeholder="Placeholder"
 *   type="text"
 *   value=""
 * />
 * ```
 *
 * This will render a text input field with a label, placeholder text, and controlled value.
 *
 */

const meta: Meta<typeof TextInput> = {
  title: 'Components/TextInput',
  tags: ['autodocs'],
  component: TextInput,
};

export const TextInputStory = (args: TextInputProps) => {
  const [state, setState] = useState(null);
  const { value: argValue } = args;
  useEffect(() => setState(argValue), [argValue]);
  return (
    <TextInput
      {...args}
      value={state}
      onChange={(
        event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
      ) => {
        const { value } = event.target;
        setState(value);
      }}
    />
  );
};
TextInputStory.args = {
  id: 'example-id',
  label: 'Label',
  name: 'example-name',
  caption: 'Example Caption',
  value: '',
  placeholder: 'Placeholder',
  left: null,
  right: null,
  type: 'text',
  minLength: null,
  maxLength: null,
  hasSucceeded: false,
  isLoading: false,
  isDisabled: false,
  isBorderless: false,
  isSquare: false,
  isFocused: false,
  shouldHideErrorMessage: false,
  onChange: () => {},
  onFocus: () => {},
  onBlur: () => {},
  onInvalid: () => {},
  isAutoFocused: false,
  className: '',
  inputMode: undefined,
  pattern: '',
  required: false,
  onKeyDown: () => {},
  onClick: () => {},
  onPaste: () => {},
  onCut: () => {},
  autoComplete: '',
  hideRightIcon: false,
  readOnly: false,
  isNewVariant: false,
};
TextInputStory.argTypes = {
  id: {
    control: 'text',
    description: 'A unique identifier for the input element.',
  },
  label: {
    control: 'text',
    description: 'The label displayed alongside the input element.',
  },
  name: {
    control: 'text',
    description: 'The name attribute of the input element.',
  },
  error: {
    control: 'text',
    description: 'An error message to display when the input value is invalid.',
  },
  caption: {
    control: 'text',
    description:
      'A supplementary caption or additional information related to the input.',
  },
  value: {
    control: 'text',
    description: 'The current value of the input element.',
  },
  placeholder: {
    control: 'text',
    description:
      'The placeholder text displayed when the input field is empty.',
  },
  left: {
    control: 'none',
    description: 'An element to display on the left side of the input.',
  },
  right: {
    control: 'none',
    description: 'An element to display on the right side of the input.',
  },
  type: {
    control: {
      type: 'select',
      options: ['text', 'email', 'password', 'number', 'search', 'tel', 'url'],
    },
    description:
      'The type of input element to display (e.g., text, email, password).',
  },
  minLength: {
    control: 'number',
    description: 'The minimum length of the input value.',
  },
  maxLength: {
    control: 'number',
    description: 'The maximum length of the input value.',
  },
  isNewVariant: {
    control: 'boolean',
    description: 'Indicates whether the new variant wrapper should be used.',
  },
  hasSucceeded: {
    control: 'boolean',
    description:
      'Indicates whether the input has succeeded (e.g., successfully submitted).',
  },
  isLoading: {
    control: 'boolean',
    description:
      'Indicates whether data is being fetched or processed for the input.',
  },
  isDisabled: {
    control: 'boolean',
    description: 'Indicates whether the input element is disabled.',
  },
  isBorderless: {
    control: 'boolean',
    description: 'Indicates whether the input element has a borderless style.',
  },
  isSquare: {
    control: 'boolean',
    description: 'Indicates whether the input element has a square style.',
  },
  isFocused: {
    control: 'boolean',
    description: 'Indicates whether the input element is currently focused.',
  },
  shouldHideErrorMessage: {
    control: 'boolean',
    description: 'Indicates whether the error message should be hidden.',
  },
  onChange: {
    action: 'changed',
    description: 'Callback function invoked when the input value changes.',
  },
  onFocus: {
    action: 'focused',
    description:
      'Callback function invoked when the input element receives focus.',
  },
  onBlur: {
    action: 'blurred',
    description:
      'Callback function invoked when the input element loses focus.',
  },
  onInvalid: {
    action: 'invalid',
    description:
      'Callback function invoked when the input value is deemed invalid.',
  },
  isAutoFocused: {
    control: 'boolean',
    description:
      'Indicates whether the input element should be automatically focused.',
  },
  className: {
    control: 'text',
    description: 'Additional CSS class names to apply to the input element.',
  },
  inputMode: {
    control: 'text',
    description:
      'The input mode (e.g., text, email, search) for mobile devices.',
  },
  pattern: {
    control: 'text',
    description: 'A regular expression pattern for validating the input value.',
  },
  required: {
    control: 'boolean',
    description: 'Indicates whether the input is required to be filled.',
  },
  resize: {
    control: {
      type: 'select',
      options: ['none', 'both', 'horizontal', 'vertical'],
    },
    description: 'Specifies whether and how the input field can be resized.',
  },
  onKeyDown: {
    action: 'key down',
    description:
      'Callback function invoked when a key is pressed while the input element is focused.',
  },
  onClick: {
    action: 'clicked',
    description: 'Callback function invoked when the input element is clicked.',
  },
  onPaste: {
    action: 'pasted',
    description:
      'Callback function invoked when content is pasted into the input element.',
  },
  onCut: {
    action: 'cut',
    description:
      'Callback function invoked when content is cut from the input element.',
  },
  autoComplete: {
    control: 'text',
    description:
      'Specifies whether the input field should have autocomplete enabled.',
  },
  hideRightIcon: {
    control: 'boolean',
    description: 'Indicates whether the right icon should be hidden.',
  },
  readOnly: {
    Control: 'boolean',
    description: 'Read only mode',
  },
};

export const TextInputSheet = ({
  label,
  fieldLabel,
  placeholder,
  error,
  caption,
  rightOverline,
}: {
  label: string;
  fieldLabel: string;
  placeholder: string;
  error: string;
  caption: string;
  rightOverline: string;
}) => {
  const [state, setState] = useState({
    standard: '',
    withLeft: '',
    withRight: '',
  });

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <ComponentGrid countColumns={2}>
        <ComponentTile label="Standard">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.standard}
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                standard: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Is focused">
          <TextInput
            isFocused
            label={label}
            placeholder={placeholder}
            value={state.standard}
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                standard: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Caption">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            caption={caption}
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Caption">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            caption={caption}
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Error">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            error={error}
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Error / Caption">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            error={error}
            caption={caption}
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Error">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            error={error}
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Error / No Message">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.standard}
            error={error}
            shouldHideErrorMessage
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                standard: event.target.value,
              })
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Error / No Message">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            error={error}
            shouldHideErrorMessage
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Right overline / Error / No Message">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            error={error}
            shouldHideErrorMessage
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Success">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            hasSucceeded
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Success">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            hasSucceeded
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Loading">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            isLoading
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Loading">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            isLoading
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Disabled">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            isDisabled
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Disabled">
          <TextInput
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            isDisabled
            onChange={(event: ChangeEvent<HTMLInputElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Field label">
          <div style={{ textAlign: 'left' }}>
            <FieldLabel label={fieldLabel} />
            <TextInput
              label={label}
              placeholder={placeholder}
              value={state.withLeft}
              onChange={(event: ChangeEvent<HTMLInputElement>) =>
                setState({
                  ...state,
                  withLeft: event.target.value,
                })
              }
            />
          </div>
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Field label">
          <div style={{ textAlign: 'left' }}>
            <FieldLabel label={fieldLabel} />
            <TextInput
              label={label}
              placeholder={placeholder}
              value={state.withRight}
              onChange={(event: ChangeEvent<HTMLInputElement>) =>
                setState({
                  ...state,
                  withRight: event.target.value,
                })
              }
              right={
                <span className={typographyStyles.overline}>
                  {rightOverline}
                </span>
              }
            />
          </div>
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
};

TextInputSheet.args = {
  label: 'Label',
  fieldLabel: 'Field label',
  placeholder: 'Placeholder',
  error: 'Input error message',
  caption: 'Input caption message',
  rightOverline: 'UNIT',
};

export default meta;
