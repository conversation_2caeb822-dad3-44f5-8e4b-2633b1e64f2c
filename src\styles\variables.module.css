/* COLORS */
/* blacks */
/* please default to the non-opaque versions as much as possible */
@value blackTrue: #000000;
@value black: #212121;
@value black70: rgba(33, 33, 33, 0.70);
@value black70Opaque: #636363;
@value black50: rgba(33, 33, 33, 0.50);
@value black50Opaque: #8F8F8F;
@value black35: rgba(33, 33, 33, 0.35);
@value black35Opaque: #B1B1B1;
@value black20: rgba(33, 33, 33, 0.20);
@value black20Opaque: #CCCCCC;
@value black10: rgba(33, 33, 33, 0.10);
@value black10Opaque: #E8E8E8;
@value black5: rgba(33, 33, 33, 0.05);
@value black5Opaque: #F3F3F3;
@value black2: rgba(33, 33, 33, 0.02);
@value black2Opaque: #FAFAFA;

/* whites */
/* please default to the non-opaque versions as much as possible */
@value white: #FFFFFF;
@value white70: rgba(255, 255, 255, 0.70);
@value white70Opaque: #B3B3B3;
@value white50: rgba(255, 255, 255, 0.60);
@value white50Opaque: #999999;
@value white35: rgba(255, 255, 255, 0.35);
@value white35Opaque: #595959;
@value white20: rgba(255, 255, 255, 0.20);
@value white20Opaque: #333333;
@value white10: rgba(255, 255, 255, 0.10);
@value white10Opaque: #1A1A1A;
@value white5: rgba(255, 255, 255, 0.05);
@value white5Opaque: #0D0D0D;
@value white2: rgba(255, 255, 255, 0.02);
@value white2Opaque: #050505;

/* primary */
@value primary: #1872ED;
@value primaryFade: #F0F6FF;
@value primaryDark: #0763E0;
@value primary5: rgba(24, 114, 237, 0.05);
@value primary10: rgba(24, 114, 237, 0.10);
@value primary20: rgba(24, 114, 237, 0.20);

@value danger: #E6252B;
@value dangerFade: #FEF1F1;
@value dangerDark: #D12227;
@value danger5: rgba(230, 37, 43, 0.05);
@value danger10: rgba(230, 37, 43, 0.10);
@value danger20: rgba(230, 37, 43, 0.20);

@value warning: #FFD94F;
@value warningFade: #FFFBEB;
@value warningDark: #F5C617;
@value warning5: rgba(255, 217, 79, 0.05);
@value warning10: rgba(255, 217, 79, 0.10);
@value warning20: rgba(255, 217, 79, 0.20);

/* neutrals */
@value neutral: #303030;
@value neutralDarkest: #646464;
@value neutralDarker: #909090;
@value neutralDark: #B1B1B1;
@value neutralLight: #E9E9E9;
@value neutralLighter: #F4F4F4;
@value neutralLightest: #F7F7F7;
@value neutralLightTable: #FBFBFB;

/* interface */
@value success: #00AB61;
@value successDark: #009a57;
@value success5: rgba(0, 171, 97, 0.05);
@value success10: rgba(0, 171, 97, 0.10);
@value success20: rgba(0, 171, 97, 0.20);
/* END COLORS */

/* MEDIA BREAKPOINTS */
/* import and then use like so: */
/* @media (max-width: smallWidth) {} */
@value xSmallWidth: 320px;
@value smallWidth: 480px;
@value mediumWidth: 768px;
@value largeWidth: 1080px;
@value xLargeWidth: 1440px;

@value smallHeight: 480px;
@value mediumHeight: 768px;
@value largeHeight: 1080px;
/* END MEDIA BREAKPOINTS */

/* FLUID SCALES */
@value fluidFloorUnitless: 320;
@value fluidCeilingUnitless: 1920;
@value fluidFloor: calc(fluidFloorUnitless * 1px);
@value fluidCeiling: calc(fluidCeilingUnitless * 1px);
/* END FLUID SCALES */

/* FLUID LINE-HEIGHTS */
@value displayFluidLineHeight: 1;
@value h1FluidLineHeight: 1;
@value h2FluidLineHeight: 1;
@value h3FluidLineHeight: 1.2;
@value h4FluidLineHeight: 1.2;
@value h5FluidLineHeight: 1.2;
@value h6FluidLineHeight: 1.5;
@value captionFluidLineHeight: 1.5;
/* FLUID LINE-HEIGHTS */

/* LINE-HEIGHTS */
@value displayLineHeight: 1;
@value displayMobileLineHeight: 1.14;
@value displayHeaderLineHeight: 0.78;
@value h1LineHeight: 1.17;
@value h1MobileLineHeight: 1.2;
@value h2LineHeight: 1.2;
@value h2MobileLineHeight: 1.25;
@value h3LineHeight: 1.25;
@value h3MobileLineHeight: 1.33;
@value h4LineHeight: 1.33;
@value h4MobileLineHeight: 1.2;
@value h5LineHeight: 1.2;
@value h5MobileLineHeight: 1.25;
@value h6LineHeight: 1.25;
@value h6MobileLineHeight: 1.428;
@value captionLineHeight: 1.67;
/* END LINE-HEIGHTS */

/* FLUID SPACING MINS/MAXES */
@value oneSpaceMin: 4px;
@value oneSpaceMax: 8px;
@value twoSpaceMin: 8px;
@value twoSpaceMax: 16px;
@value threeSpaceMin: 16px;
@value threeSpaceMax: 24px;
@value fourSpaceMin: 24px;
@value fourSpaceMax: 32px;
@value fiveSpaceMin: 32px;
@value fiveSpaceMax: 40px;
@value sixSpaceMin: 40px;
@value sixSpaceMax: 48px;
@value eightSpaceMin: 48px;
@value eightSpaceMax: 64px;
@value tenSpaceMin: 64px;
@value tenSpaceMax: 80px;
@value fifteenSpaceMin: 80px;
@value fifteenSpaceMax: 120px;
@value twentyFiveSpaceMin: 120px;
@value twentyFiveSpaceMax: 200px;
/* END FLUID SPACING MINS/MAXES */

/* SPACING */
@value halfSpace: 4px;
@value oneSpace: 8px;
@value twoSpace: 16px;
@value threeSpace: 24px;
@value fourSpace: 32px;
@value fiveSpace: 40px;
@value sixSpace: 48px;
@value eightSpace: 64px;
@value tenSpace: 80px;
@value fifteenSpace: 120px;
@value twentyFiveSpace: 200px;
/* END SPACING */

/* FLUID SPACING */
@value halfSpaceFluid: halfSpace;
@value oneSpaceFluid: calc(halfSpace + (8 - 4) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value twoSpaceFluid: calc(oneSpace + (16 - 8) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value threeSpaceFluid: calc(twoSpace + (24 - 16) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value fourSpaceFluid: calc(threeSpace + (32 - 24) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value fiveSpaceFluid: calc(fourSpace + (40 - 32) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value sixSpaceFluid: calc(fiveSpace + (48 - 40) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value eightSpaceFluid: calc(sixSpace + (64 - 48) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value tenSpaceFluid: calc(eightSpace + (80 - 64) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value fifteenSpaceFluid: calc(tenSpace + (120 - 80) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value twentyFiveSpaceFluid: calc(fifteenSpace + (200 - 120) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
/* END FLUID SPACING */

/* FONT SIZES */
@value displayFontSize: 120px;
@value displayMobileFontSize: 56px;
@value h1FontSize: 48px;
@value h1MobileFontSize: 40px;
@value h2FontSize: 40px;
@value h2MobileFontSize: 32px;
@value h3FontSize: 32px;
@value h3MobileFontSize: 24px;
@value h4FontSize: 24px;
@value h4MobileFontSize: 20px;
@value h5FontSize: 20px;
@value h5MobileFontSize: 16px;
@value h6FontSize: 16px;
@value h6MobileFontSize: 14px;
@value bodyLargeFontSize: 20px;
@value bodyBaseFontSize: 16px;
@value bodySmallFontSize: 14px;
@value buttonLabelXLargeFontSize: 20px;
@value buttonLabelLargeFontSize: 16px;
@value buttonLabelSmallFontSize: 14px;
@value captionFontSize: 12px;
@value overlineFontSize: 12px;
/* END FONT SIZES */

/* FLUID FONT SIZES MINS/MAXES */
@value displayFluidFontSizeMin: 56px;
@value displayFluidFontSizeMax: 160px;
@value h1FluidFontSizeMin: 40px;
@value h1FluidFontSizeMax: 124px;
@value h2FluidFontSizeMin: 32px;
@value h2FluidFontSizeMax: 96px;
@value h3FluidFontSizeMin: 28px;
@value h3FluidFontSizeMax: 64px;
@value h4FluidFontSizeMin: 20px;
@value h4FluidFontSizeMax: 32px;
@value h5FluidFontSizeMin: 16px;
@value h5FluidFontSizeMax: 20px;
@value h6FluidFontSizeMin: 14px;
@value h6FluidFontSizeMax: 16px;
@value bodyLargeFluidFontSizeMin: 16px;
@value bodyLargeFluidFontSizeMax: 20px;
@value bodyBaseFluidFontSizeMin: 14px;
@value bodyBaseFluidFontSizeMax: 16px;
@value bodySmallFluidFontSizeMin: 13px;
@value bodySmallFluidFontSizeMax: 14px;
@value buttonLabelXLargeFluidFontSizeMin: 16px;
@value buttonLabelXLargeFluidFontSizeMax: 20px;
@value buttonLabelLargeFluidFontSizeMin: 14px;
@value buttonLabelLargeFluidFontSizeMax: 16px;
@value buttonLabelSmallFluidFontSizeMin: 12px;
@value buttonLabelSmallFluidFontSizeMax: 14px;
@value captionFluidFontSizeMin: 12px;
@value captionFluidFontSizeMax: 16px;
@value overlineFluidFontSizeMin: 12px;
@value overlineFluidFontSizeMax: 14px;
/* END FLUID FONT SIZES MINS/MAXES */

/* FLUID FONT SIZES */
@value displayFluidFontSize: calc(56px + (160 - 56) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value h1FluidFontSize: calc(40px + (124 - 40) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value h2FluidFontSize: calc(32px + (96 - 32) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value h3FluidFontSize: calc(28px + (64 - 28) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value h4FluidFontSize: calc(20px + (32 - 20) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value h5FluidFontSize: calc(16px + (20 - 16) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value h6FluidFontSize: calc(14px + (16 - 14) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value bodyLargeFluidFontSize: calc(16px + (20 - 16) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value bodyBaseFluidFontSize: calc(14px + (16 - 14) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value bodySmallFluidFontSize: calc(13px + (14 - 13) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value buttonLabelXLargeFluidFontSize: calc(16px + (20 - 16) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value buttonLabelLargeFluidFontSize: calc(14px + (16 - 14) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value buttonLabelSmallFluidFontSize: calc(12px + (14 - 12) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value captionFluidFontSize: calc(12px + (16 - 12) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
@value overlineFluidFontSize: calc(12px + (14 - 12) * ((100vw - fluidFloor) / (fluidCeilingUnitless - fluidFloorUnitless)));
/* END FLUID FONT SIZES */

/* MAX WIDTHS */
@value bodyMaxWidth: 50em;
@value containerMaxWidth: 1000px;
@value pageMaxWidth: 1440px;
/* END MAX WIDTHS */

/* Z-INDICES */
@value belowZIndex: -1;
@value aboveZIndex: 1;
@value buoyantZIndex: 2;

@value tableHeaderZIndex: 4;
@value pinnedColumnZIndex: 5;

/* make sure this is higher than the table and columns */
@value topZIndex: 7;
/* make sure this is higher than top z index */
@value higherModalXZIndex: 8;
/* END Z-INDICES */

/* BEZIER CURVES */
@value ease-out-expo: cubic-bezier(0.19, 1.0, 0.22, 1.0);
@value swingTo: cubic-bezier(0.2, 1.47, 0.27, 1.05);
@value swingFrom: cubic-bezier(.36,0,.66,-0.56);
/* END BEZIER CURVES */

/* SHADOWS */
@value shadow: 4px 4px 0px rgba(33, 33, 33, 0.05);
/* SHADOWS */
