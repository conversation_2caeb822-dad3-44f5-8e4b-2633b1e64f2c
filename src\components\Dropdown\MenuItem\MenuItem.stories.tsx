import React from 'react';
import { Meta } from '@storybook/react';
import { MenuItem as SelectInputMenuItem } from '@components/Dropdown';

/**
 * <br/>
 * ## Overview
 * Menu Item component is used to render a Items which can be used within the menu.
 * ## Usage
 * Import the component into your React application:
 *
 * ```jsx
 * import { MenuItem } from '@peddleon/ped-ux-react-library';
 * ```
 */
export default {
  title: 'Components/Menu Items',
  component: SelectInputMenuItem,
  argTypes: {
    isHighlighted: { control: 'boolean' },
    left: { control: 'disabled' },
    secondaryLabel: {
      control: {
        type: 'text',
      },
    },
  },
  tags: ['autodocs'],
} as Meta;

/**
 * Template function to render SelectInputMenuItem component
 * @param {Object} args - Arguments for the component
 * @returns {JSX.Element} - SelectInputMenuItem component
 */
const Template = args => <SelectInputMenuItem {...args} />;

/**
 * Default Menu Item
 */
export const Default = Template.bind({});
Default.args = {
  children: 'Default Item',
  isHighlighted: false,
  secondaryLabel: '',
};

/**
 * Menu Item with Left Content
 */
export const WithLeftContent = Template.bind({});
WithLeftContent.args = {
  children: 'Item with Left Content',
  left: <div>Left Content</div>,
};

/**
 * Menu Item with Secondary Label
 */
export const WithSecondaryLabel = Template.bind({});
WithSecondaryLabel.args = {
  children: 'Item with Secondary Label',
  secondaryLabel: 'Secondary Label',
};

/**
 * Highlighted Menu Item
 */
export const Highlighted = Template.bind({});
Highlighted.args = {
  children: 'Highlighted Item',
  isHighlighted: true,
};
