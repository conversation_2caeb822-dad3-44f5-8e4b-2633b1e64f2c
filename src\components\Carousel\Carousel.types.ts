import { ReactNode } from 'react';

/**
 * Defines the props for the Carousel component.
 *
 * @template T - The type of items in the carousel.
 */
export interface CarouselPropTypes<T> {
  /**
   * An array of items to be rendered in the carousel.
   */
  items: T[];

  /**
   * A function that renders each item in the carousel.
   *
   * @param {T} item - The item to be rendered.
   * @returns {ReactNode} - The rendered React node.
   */
  renderSlide: (item: T, index: number) => ReactNode;

  /**
   * The number of slides visible at once in the carousel.
   */
  visibleSlides?: number;

  /**
   * The index of the current slide.
   */
  currentSlide?: number;

  /**
   * The number of slides to move when navigating.
   */
  step?: number;

  /**
   * Specifies whether the carousel should adjust slide height to fit the content.
   */
  isIntrinsicHeight?: boolean;

  /**
   * Specifies whether the carousel should loop infinitely.
   */
  infinite?: boolean;

  /**
   * Specifies whether the carousel should automatically scroll.
   */
  autoScroll?: boolean;

  /**
   * The direction in which the carousel should auto-scroll.
   */
  playDirection?: 'forward' | 'backward';

  /**
   * The interval in milliseconds between auto-scroll transitions.
   */
  interval?: number;

  /**
   * Specifies whether the carousel should display navigation dots.
   */
  hasDots?: boolean;
  /**
   * callback on active index change
   */
  onActiveIndexChange?: (index: number) => void;
}
