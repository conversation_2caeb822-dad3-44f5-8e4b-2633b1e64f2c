import React, { CSSProperties, FC } from 'react';
import Button from '@components/Button';
import { ButtonTheme } from '@components/Button/Button.types';
import classNames from '@utils/classNames';

import styles from './TableText.module.css';

interface Icon {
  id: string;
  icon: React.ReactNode;
}

interface TableTextIconProps {
  isInverted?: boolean;
  isActive?: boolean;
  customStyle?: CSSProperties;
  className?: string;
  icons?: Icon[];
  handleIconClick: (id: string) => void;
  iconButtonTheme?: ButtonTheme;
}

const TableTextIcon: FC<TableTextIconProps> = ({
  isInverted,
  isActive,
  customStyle,
  className,
  icons,
  handleIconClick,
  iconButtonTheme,
}) => (
  <div
    className={classNames(
      styles.text,
      styles.icon,
      isActive && styles.active,
      isInverted && styles.isInverted,
      className,
    )}
    style={customStyle}
  >
    {icons &&
      icons.map(item => (
        <Button
          key={item.id}
          onClick={() => handleIconClick(item.id)}
          theme={iconButtonTheme}
          size="small"
        >
          {item.icon}
        </Button>
      ))}
  </div>
);

export default TableTextIcon;
