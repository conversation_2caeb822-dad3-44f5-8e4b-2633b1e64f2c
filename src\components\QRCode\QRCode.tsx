import React, { forwardRef, useCallback, useEffect, useRef } from 'react';
import QRCodeStyling, { FileExtension } from 'qr-code-styling';
import variableStyles from '@styles/variables.module.css';

const qrCode = new QRCodeStyling({
  width: 300,
  height: 300,
  image: 'https://peddle-public-dev.imgix.net/favicon/peddle-icon.svg',
  margin: 10,
  type: 'svg',
  imageOptions: {
    crossOrigin: 'anonymous',
  },
  dotsOptions: {
    color: variableStyles.black,
  },
  cornersSquareOptions: {
    type: 'extra-rounded',
  },
  cornersDotOptions: {
    type: 'dot',
  },
  backgroundOptions: {
    color: 'transparent',
  },
});

const QRCode = forwardRef<
  HTMLDivElement,
  { url: string; fileExt?: FileExtension; size?: string }
>(({ url, fileExt, size = '300' }, ref) => {
  const divRef = useRef<HTMLDivElement>(null);

  const onDownloadClick = useCallback(async () => {
    if (fileExt === 'svg') {
      // Generate SVG Blob
      const svgBlob = await qrCode.getRawData('svg');
      const svgText = await (svgBlob as Blob).text();
      // Modify the SVG content to use xlink:href
      const modifiedSvg = svgText.replace(
        /<image href=/g,
        '<image xlink:href=',
      );
      // Create a new Blob and trigger download
      const modifiedBlob = new Blob([modifiedSvg], { type: 'image/svg+xml' });
      const link = document.createElement('a');
      link.href = URL.createObjectURL(modifiedBlob);
      link.download = `peddle-qr_${url}.svg`;
      link.click();
      URL.revokeObjectURL(link.href);
    } else {
      // Handle other file extensions
      qrCode.download({
        extension: fileExt,
        name: `peddle-qr_${url}`,
      });
    }
  }, [fileExt, url]);

  useEffect(() => {
    let currentRef = null;
    if (ref && 'current' in ref && ref.current) {
      currentRef = ref.current;
      currentRef.addEventListener('click', onDownloadClick);
    }
    return () => {
      if (currentRef) {
        currentRef.removeEventListener('click', onDownloadClick);
      }
    };
  }, [fileExt, onDownloadClick, ref]);

  useEffect(() => {
    qrCode.append(divRef.current);
  }, []);

  useEffect(() => {
    qrCode.update({
      data: url,
    });
  }, [url]);

  useEffect(() => {
    qrCode.update({
      width: parseInt(size, 10),
      height: parseInt(size, 10),
    });
  }, [size]);

  return (
    <div>
      <div ref={divRef} />
    </div>
  );
});

export default QRCode;
