import { FC, ReactNode, useMemo } from 'react';
import Divider from '@components/Divider';
import Typography from '@components/Typography';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import variableStyles from '@styles/variables.module.css';

import styles from './TableFooter.module.css';

interface TableFooterRowProps {
  left?: ReactNode;
  right?: ReactNode;
  needToAddDevider?: boolean;
}

const TableFooterRow: FC<TableFooterRowProps> = ({
  left,
  right,
  needToAddDevider = false,
}) => {
  const isMobile = useMediaQueryState({
    query: `(max-width: ${variableStyles.smallWidth})`,
  });
  const mobileWrapperClasses = useMemo(
    () => classNames(styles.tableFooterWrapperMobile, styles.footerRoot),
    [],
  );
  return (
    <>
      <div
        className={classNames(
          styles.tableFooterWrapper,
          isMobile && mobileWrapperClasses,
        )}
      >
        <Typography
          variant={isMobile ? 'h5Strong' : 'h6Strong'}
          className={styles.footerText}
        >
          {left}
        </Typography>

        <Typography
          variant={isMobile ? 'h5Strong' : 'h6Strong'}
          className={styles.footerText}
        >
          {right}
        </Typography>
      </div>
      {needToAddDevider && (
        <Divider theme="lightInverted" className={styles.footerDevider} />
      )}
    </>
  );
};

export default TableFooterRow;
