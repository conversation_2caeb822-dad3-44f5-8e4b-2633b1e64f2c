import { ReactNode, SyntheticEvent } from 'react';

export type AlertTheme =
  | 'primary'
  | 'primaryInverted'
  | 'primaryFaded'
  | 'danger'
  | 'dangerInverted'
  | 'dangerFaded'
  | 'success'
  | 'successInverted'
  | 'successFaded'
  | 'warning'
  | 'warningInverted'
  | 'warningFaded';

export interface AlertProps {
  /**
   * The main content body of the alert.
   */
  body: string;
  /**
   * The theme of the alert. Choose from the predefined set of themes.
   */
  theme: AlertTheme;
  /**
   * An optional ReactNode to be displayed on the left side of the alert body.
   */
  left?: ReactNode;
  /**
   * Specifies whether the alert can be cancelled by the user.
   */
  isCancellable?: boolean;
  /**
   * Callback function triggered when the cancel button is clicked.
   */
  onCancelButtonClick?: (e: SyntheticEvent<Element, Event>) => void;
  /**
   * Specifies whether the alert should have animated effects.
   */
  isAnimated?: boolean;
}
