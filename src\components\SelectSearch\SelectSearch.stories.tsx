/* eslint-disable no-console */
import { useState } from 'react';
import { Meta } from '@storybook/react';
import Field<PERSON>abel from '@components/FieldLabel';
import SelectSearch from '@components/SelectSearch';
import {
  MOCK_GROUP_OPTIONS,
  MOCK_VEHICLE_MAKE_OPTIONS,
  MOCK_VEHICLE_YEAR_OPTIONS,
} from '@constants/stories';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import typographyStyles from '@styles/typography.module.css';
import variableStyles from '@styles/variables.module.css';

import { SelectSearchPropType } from './SelectSearch.types';
/**
 * The SelectSearch component provides a dropdown input interface for users to select options from a list.
 * It allows for the selection of single items from a dropdown menu, with customizable styling and behavior.
 *
 * ## Overview
 *
 * The SelectSearch component allows developers to create dropdown input fields for selecting options in their React applications.
 * It supports various features such as placeholder text, search functionality, and controlled state management.
 *
 * ## Usage
 *
 * To use the SelectSearch component in your React application, import it from the appropriate directory and render it with the desired props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { SelectSearch } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the SelectSearch component in your JSX:
 *
 * ```jsx
 * <SelectSearch
 *   caption="Example Caption"
 *   maxLength={50}
 *   onMenuClose={() => {}}
 *   items={[
 *     { value: 'option1', label: 'Option 1' },
 *     { value: 'option2', label: 'Option 2' },
 *     { value: 'option3', label: 'Option 3' },
 *   ]}
 *   onChange={(selectedItem) => {
 *     console.log('Selected Item:', selectedItem);
 *   }}
 *   isMenuAlignedRight={false}
 *   placeholder="Placeholder"
 *   isActive={false}
 *   value={{ value: 'option1', label: 'Option 1' }}
 *   noMatchFoundText="No matches"
 *   label="Label"
 * />
 * ```
 *
 * This will render a dropdown input field with a label, placeholder text, and controlled value.
 *
 */
const meta: Meta<typeof SelectSearch> = {
  title: 'Components/SelectSearch',
  tags: ['autodocs'],
  component: SelectSearch,
};

export const SelectSearchStory = (args: SelectSearchPropType) => {
  const [state, setState] = useState(null);
  return (
    <div style={{ minHeight: '400px' }}>
      <SelectSearch
        {...args}
        value={state}
        onChange={value => setState(value)}
      />
    </div>
  );
};
SelectSearchStory.args = {
  caption: 'Example Caption',
  maxLength: 50,
  onMenuClose: () => {},
  items: MOCK_VEHICLE_MAKE_OPTIONS,
  onChange: selectedItem => {
    console.log('Selected Item:', selectedItem);
  },
  isMenuAlignedRight: false,
  placeholder: 'Placeholder',
  isActiveControlled: false,
  isActive: false,
  onToggleButtonClick: () => {
    console.log('Toggle button clicked');
  },
  onShouldClose: () => {
    console.log('Menu should close');
  },
  value: null,
  inputMode: 'text',
  noMatchFoundText: 'No matches',
  label: 'Label',
  onInvalid: () => {
    console.log('on invalid');
  },
  variant: 'normalFonts',
};
SelectSearchStory.argTypes = {
  caption: {
    control: 'text',
    description: 'A supplementary text displayed at the bottom of the input.',
  },
  maxLength: {
    control: 'number',
    description: 'The maximum number of characters allowed in the input.',
  },
  onMenuClose: {
    action: 'onMenuClose',
    description: 'Callback function invoked when the menu is closed.',
  },
  items: {
    control: 'object',
    description: 'An array of items to be displayed in the dropdown menu.',
  },
  onChange: {
    action: 'onChange',
    description:
      'Callback function invoked when an item is selected from the dropdown menu.',
  },
  isMenuAlignedRight: {
    control: 'boolean',
    description: 'Specifies whether the dropdown menu is aligned to the right.',
  },
  placeholder: {
    control: 'text',
    description: 'Placeholder text displayed when no option is selected.',
  },
  isActiveControlled: {
    control: 'boolean',
    description:
      'Indicates whether the active state of the dropdown is controlled externally.',
  },
  isActive: {
    control: 'boolean',
    description: 'Specifies whether the dropdown menu is active/open.',
  },
  onToggleButtonClick: {
    action: 'onToggleButtonClick',
    description:
      'Callback function invoked when the toggle button is clicked while the input is in focus.',
  },
  onShouldClose: {
    action: 'onShouldClose',
    description: 'Callback function invoked when the menu should be closed.',
  },
  value: {
    control: 'object',
    description: 'The currently selected value from the dropdown menu.',
  },
  inputMode: {
    control: {
      type: 'select',
      options: [
        'text',
        'email',
        'search',
        'tel',
        'url',
        'none',
        'numeric',
        'decimal',
      ],
    },
    description: 'The input mode for mobile devices.',
  },
  noMatchFoundText: {
    control: 'text',
    description:
      'Text displayed when no matching options are found in the dropdown.',
  },
  id: {
    control: 'text',
    description: 'A unique identifier for the input element.',
  },
  label: {
    control: 'text',
    description: 'The label displayed alongside the input element.',
  },
  error: {
    control: 'text',
    description: 'An error message to display when the input value is invalid.',
  },
  left: {
    control: 'none',
    description: 'An element to display on the left side of the input.',
  },
  right: {
    control: 'none',
    description: 'An element to display on the right side of the input.',
  },
  hasSucceeded: {
    control: 'boolean',
    description:
      'Indicates whether the input has succeeded (e.g., successfully submitted).',
  },
  isLoading: {
    control: 'boolean',
    description:
      'Indicates whether data is being fetched or processed for the input.',
  },
  isDisabled: {
    control: 'boolean',
    description: 'Indicates whether the input element is disabled.',
  },
  isBorderless: {
    control: 'boolean',
    description: 'Indicates whether the input element has a borderless style.',
  },
  onInvalid: {
    action: 'invalid',
    description:
      'Callback function invoked when the input value is deemed invalid.',
  },
  variant: {
    control: {
      type: 'select',
      options: ['normalFonts', 'helveticaFonts'],
    },
    description: 'The variant of the input element.',
  },
};

export const SelectSearchGroupOptionStory = (args: SelectSearchPropType) => {
  const [state, setState] = useState(null);
  return (
    <div style={{ minHeight: '400px', margin: '10px' }}>
      <SelectSearch
        {...args}
        value={state}
        onChange={value => {
          setState(value);
        }}
        isFixedMenu
      />
    </div>
  );
};
SelectSearchGroupOptionStory.args = {
  maxLength: 50,
  onMenuClose: () => {},
  items: MOCK_GROUP_OPTIONS,
  onChange: selectedItem => {
    console.log('Selected Item:', selectedItem);
  },
  isMenuAlignedRight: false,
  placeholder: 'Placeholder',
  isActiveControlled: false,
  isActive: false,
  onToggleButtonClick: () => {
    console.log('Toggle button clicked');
  },
  onShouldClose: () => {
    console.log('Menu should close');
  },
  value: null,
  inputMode: 'text',
  noMatchFoundText: 'No matches',
  label: 'Select pickup window',
  onInvalid: () => {
    console.log('on invalid');
  },
  variant: 'normalFonts',
};
SelectSearchGroupOptionStory.argTypes = {
  caption: {
    control: 'text',
    description: 'A supplementary text displayed at the bottom of the input.',
  },
  maxLength: {
    control: 'number',
    description: 'The maximum number of characters allowed in the input.',
  },
  onMenuClose: {
    action: 'onMenuClose',
    description: 'Callback function invoked when the menu is closed.',
  },
  items: {
    control: 'object',
    description: 'An array of items to be displayed in the dropdown menu.',
  },
  onChange: {
    action: 'onChange',
    description:
      'Callback function invoked when an item is selected from the dropdown menu.',
  },
  isMenuAlignedRight: {
    control: 'boolean',
    description: 'Specifies whether the dropdown menu is aligned to the right.',
  },
  placeholder: {
    control: 'text',
    description: 'Placeholder text displayed when no option is selected.',
  },
  isActiveControlled: {
    control: 'boolean',
    description:
      'Indicates whether the active state of the dropdown is controlled externally.',
  },
  isActive: {
    control: 'boolean',
    description: 'Specifies whether the dropdown menu is active/open.',
  },
  onToggleButtonClick: {
    action: 'onToggleButtonClick',
    description:
      'Callback function invoked when the toggle button is clicked while the input is in focus.',
  },
  onShouldClose: {
    action: 'onShouldClose',
    description: 'Callback function invoked when the menu should be closed.',
  },
  value: {
    control: 'object',
    description: 'The currently selected value from the dropdown menu.',
  },
  inputMode: {
    control: {
      type: 'select',
      options: [
        'text',
        'email',
        'search',
        'tel',
        'url',
        'none',
        'numeric',
        'decimal',
      ],
    },
    description: 'The input mode for mobile devices.',
  },
  noMatchFoundText: {
    control: 'text',
    description:
      'Text displayed when no matching options are found in the dropdown.',
  },
  id: {
    control: 'text',
    description: 'A unique identifier for the input element.',
  },
  label: {
    control: 'text',
    description: 'The label displayed alongside the input element.',
  },
  error: {
    control: 'text',
    description: 'An error message to display when the input value is invalid.',
  },
  left: {
    control: 'none',
    description: 'An element to display on the left side of the input.',
  },
  right: {
    control: 'none',
    description: 'An element to display on the right side of the input.',
  },
  hasSucceeded: {
    control: 'boolean',
    description:
      'Indicates whether the input has succeeded (e.g., successfully submitted).',
  },
  isLoading: {
    control: 'boolean',
    description:
      'Indicates whether data is being fetched or processed for the input.',
  },
  isDisabled: {
    control: 'boolean',
    description: 'Indicates whether the input element is disabled.',
  },
  isBorderless: {
    control: 'boolean',
    description: 'Indicates whether the input element has a borderless style.',
  },
  onInvalid: {
    action: 'invalid',
    description:
      'Callback function invoked when the input value is deemed invalid.',
  },
  variant: {
    control: {
      type: 'select',
      options: ['normalFonts', 'helveticaFonts'],
    },
    description: 'The variant of the input element.',
  },
};

export const SelectSearchSheet = ({
  label,
  fieldLabel,
  error,
  caption,
  rightOverline,
  isMenuAlignedRight,
  placeholder,
}: {
  label: string;
  fieldLabel: string;
  error: string;
  caption: string;
  rightOverline: string;
  isMenuAlignedRight: boolean;
  placeholder: string;
}) => {
  const [state, setState] = useState({
    standard: null,
    withLeft: null,
    withRight: null,
    make: null,
    year: null,
    model: null,
  });

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <ComponentGrid countColumns={2}>
        <ComponentTile label="Standard | Group data">
          <SelectSearch
            label={label}
            value={state.withLeft}
            items={MOCK_GROUP_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            onChange={value => setState({ ...state, withLeft: value })}
            placeholder={placeholder}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline">
          <SelectSearch
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
            placeholder={placeholder}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Caption">
          <SelectSearch
            label={label}
            value={state.withLeft}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            caption={caption}
            onChange={value => setState({ ...state, withLeft: value })}
            placeholder={placeholder}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Caption">
          <SelectSearch
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            caption={caption}
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
            placeholder={placeholder}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Error">
          <SelectSearch
            label={label}
            value={state.withLeft}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            error={error}
            onChange={value => setState({ ...state, withLeft: value })}
            placeholder={placeholder}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Error / Caption">
          <SelectSearch
            label={label}
            value={state.withLeft}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            caption={caption}
            error={error}
            onChange={value => setState({ ...state, withLeft: value })}
            placeholder={placeholder}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Error / Caption / Fixed">
          <SelectSearch
            label={label}
            value={state.withLeft}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            caption={caption}
            error={error}
            onChange={value => setState({ ...state, withLeft: value })}
            placeholder={placeholder}
            isFixedMenu
          />
        </ComponentTile>

        <ComponentTile label="Standard / Right overline / Error">
          <SelectSearch
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            error={error}
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
            placeholder={placeholder}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Success">
          <SelectSearch
            label={label}
            value={state.withLeft}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            hasSucceeded
            onChange={value => setState({ ...state, withLeft: value })}
          />
        </ComponentTile>

        <ComponentTile label="Standard / Right overline / Success / Fixed">
          <SelectSearch
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            hasSucceeded
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
            placeholder={placeholder}
            isFixedMenu
          />
        </ComponentTile>
        <ComponentTile label="Standard / Loading">
          <SelectSearch
            label={label}
            value={state.withLeft}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            isLoading
            onChange={value => setState({ ...state, withLeft: value })}
            placeholder={placeholder}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Loading">
          <SelectSearch
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            isLoading
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
            placeholder={placeholder}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Disabled">
          <SelectSearch
            label={label}
            value={state.withLeft}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            isDisabled
            onChange={value => setState({ ...state, withLeft: value })}
            placeholder={placeholder}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Disabled">
          <SelectSearch
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            isDisabled
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
            placeholder={placeholder}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Field label">
          <div style={{ textAlign: 'left' }}>
            <FieldLabel label={fieldLabel} />
            <SelectSearch
              label={label}
              value={state.withLeft}
              items={MOCK_VEHICLE_MAKE_OPTIONS}
              isMenuAlignedRight={isMenuAlignedRight}
              onChange={value => setState({ ...state, withLeft: value })}
              placeholder={placeholder}
            />
          </div>
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Field label">
          <div style={{ textAlign: 'left' }}>
            <FieldLabel label={fieldLabel} />
            <SelectSearch
              label={label}
              value={state.withRight}
              items={MOCK_VEHICLE_MAKE_OPTIONS}
              isMenuAlignedRight={isMenuAlignedRight}
              onChange={value => setState({ ...state, withRight: value })}
              right={
                <span className={typographyStyles.overline}>
                  {rightOverline}
                </span>
              }
              placeholder={placeholder}
            />
          </div>
        </ComponentTile>
        <ComponentTile label="Triggered Selects 1 / Fixed">
          <div style={{ textAlign: 'left' }}>
            <SelectSearch
              label="Year"
              value={state.year}
              items={MOCK_VEHICLE_YEAR_OPTIONS}
              isMenuAlignedRight={isMenuAlignedRight}
              onChange={value => setState({ ...state, year: value })}
              placeholder={placeholder}
              isFixedMenu
            />
          </div>
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
};

SelectSearchSheet.args = {
  label: 'Label',
  fieldLabel: 'Field label',
  placeholder: 'Placeholder',
  error: 'Input error message',
  caption: 'Input caption message',
  rightOverline: 'UNIT',
  isMenuAlignedRight: false,
};

export default meta;
