import type { ReactEventHandler, ReactNode } from 'react';
import React, { forwardRef } from 'react';
import { TagTheme } from '@components/Button/Button.types';
import classNames from '@utils/classNames';
import themeStyles from '@styles/themes.module.css';

import { AnimatedLoader } from '..';
import styles from './Tag.module.css';

interface TagProps {
  label?: string;
  left?: ReactNode;
  right?: ReactNode;
  theme?: TagTheme;
  type?: 'submit' | 'reset' | 'button' | 'label';
  isFullWidth?: boolean;
  isLoading?: boolean;
  isDisabled?: boolean;
  withHorizontalPadding?: boolean;
  onClick?: ReactEventHandler;
  tabIndex?: number;
  className?: string;
  labelClassName?: string;
  onMouseDown?: ReactEventHandler;
  onMouseUp?: ReactEventHandler;
  onTouchStart?: ReactEventHandler;
  onTouchEnd?: ReactEventHandler;
}
const Tag = forwardRef<HTMLDivElement | HTMLButtonElement, TagProps>(
  (
    {
      label = '',
      left = null,
      right = null,
      theme = 'dark',
      type = 'button',
      isFullWidth = false,
      isLoading = false,
      isDisabled = false,
      withHorizontalPadding = true,
      onClick = () => {},
      tabIndex = 0,
      className: classNameProp,
      labelClassName = null,
      onMouseDown = () => {},
      onMouseUp = () => {},
      onTouchStart = () => {},
      onTouchEnd = () => {},
    },
    ref,
  ) => {
    const isLabel = type === 'label';
    const className = classNames(
      themeStyles[`${theme}Theme`],
      styles[`${theme}Theme`],
      theme.includes('Inverted') && styles.disabledInverted,
      isDisabled && styles.disabled,
      styles.tag,
      withHorizontalPadding && styles.withHorizontalPadding,
      isFullWidth && styles.fullWidth,
      isLoading && styles.fetching,
      !isLabel && styles.interactive,
      classNameProp,
    );
    const inner = (
      <>
        {left && (
          <span className={styles.leftWrapper} aria-hidden="true">
            {left}
          </span>
        )}
        <span className={classNames(styles.label, labelClassName)}>
          {label}
        </span>
        <span className={styles.fetchingIconWrapper}>
          <AnimatedLoader size="18px" />
        </span>
        {right && (
          <span className={styles.rightWrapper} aria-hidden="true">
            {right}
          </span>
        )}
      </>
    );
    return isLabel ? (
      <div
        ref={ref as React.ForwardedRef<HTMLDivElement>}
        className={className}
      >
        {inner}
      </div>
    ) : (
      <button
        ref={ref as React.ForwardedRef<HTMLButtonElement>}
        className={className}
        // eslint-disable-next-line react/button-has-type
        type={type}
        disabled={isDisabled}
        onClick={onClick}
        tabIndex={tabIndex}
        onMouseDown={onMouseDown}
        onMouseUp={onMouseUp}
        onTouchStart={onTouchStart}
        onTouchEnd={onTouchEnd}
      >
        {inner}
      </button>
    );
  },
);
export default Tag;
