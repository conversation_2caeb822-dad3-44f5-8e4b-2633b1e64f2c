import Typography from '@components/Typography';
import classNames from '@utils/classNames';

import variableStyles from '../../../styles/variables.module.css';
import styles from '../DatePicker.module.css';

const DayButton = ({
  buttonProps,
  day,
  modifiers,
  pickerType,
  selectedDate,
}) => (
  // eslint-disable-next-line react/button-has-type
  <button
    {...buttonProps}
    className={classNames(
      'rdp-day_button',
      styles.dayButton,
      modifiers.focused && styles.dayButtonFocused,
      modifiers.selected && styles.dayButtonSelected,
      (modifiers.range_start || modifiers.range_end) &&
        styles.dayButtonRangeStartEnd,
      !!(
        pickerType === 'range' &&
        selectedDate?.from < day &&
        selectedDate?.to > day
      ) && styles.dayButtonMiddleSelected,
      modifiers.today && styles.dayButtonToday,
    )}
    style={{
      '--rdp-accent-color': variableStyles.primary,
      '--rdp-range_end-date-background-color': variableStyles.primary,
      '--rdp-range_middle-background-color': variableStyles.primaryFade,
      '--rdp-range_start-date-background-color': variableStyles.primary,
    }}
  >
    <Typography tag="h6" strong={modifiers.today || modifiers.selected}>
      {buttonProps.children}
    </Typography>
  </button>
);

export default DayButton;
