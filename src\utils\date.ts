/* eslint-disable no-case-declarations */
export const formatDate = (
  timestamp: Date | string | number,
  timeZone: string = undefined,
  formatBy: string = '-',
) =>
  new Date(timestamp)
    .toLocaleDateString(undefined, {
      day: 'numeric',
      month: 'numeric',
      year: '2-digit',
      timeZone,
    })
    .replace(/\//g, formatBy);

export const formatDateTable = dateString => {
  try {
    const date = new Date(dateString);
    const month = date.getMonth() + 1; // Months are 0-based
    const day = date.getDate();
    const year = date.getFullYear();
    return `${month}/${day}/${year}`;
  } catch (error) {
    return 'Invalid Date';
  }
};

export const isToday = date => formatDate(date) === formatDate(Date.now());

export const formatMonth = (
  timestamp: Date | string | number,
  timeZone: string = undefined,
  month: 'short' | 'numeric' | 'long' | '2-digit' = 'short',
) =>
  new Date(timestamp).toLocaleDateString(undefined, {
    month,
    timeZone,
  });

export const formatWeekday = (
  timestamp: Date | string | number,
  timeZone: string = undefined,
  weekday: 'short' | 'long' | 'narrow' = 'short',
): string =>
  new Date(timestamp).toLocaleDateString(undefined, {
    weekday,
    timeZone,
  });

export const formatDay = (
  timestamp: Date | string | number,
  timeZone: string = undefined,
): string =>
  new Date(timestamp).toLocaleDateString(undefined, {
    day: '2-digit',
    timeZone,
  });

export const generateHref = ({ date }) =>
  `#${formatDate(date).replace(/\//g, '-')}`;

export const formatTime = (
  timestamp: Date | string | number,
  timeZone: string = undefined,
) =>
  new Date(timestamp)
    .toLocaleTimeString(undefined, {
      hour: 'numeric',
      minute: '2-digit',
      timeZone,
    })
    .replace(/ AM/g, 'am')
    .replace(/ PM/g, 'pm');

export const formatTimeRange = (
  fromTimestamp: Date | string | number,
  toTimestamp: Date | string | number,
  timeZone: string = undefined,
): string => {
  if (!fromTimestamp || !toTimestamp) return '';
  const fromTimeString = formatTime(fromTimestamp, timeZone);
  const toTimeString = formatTime(toTimestamp, timeZone);
  const from =
    fromTimeString === 'Invalid Date' || !fromTimeString
      ? fromTimestamp
      : fromTimeString;
  const to =
    toTimeString === 'Invalid Date' || !toTimeString
      ? toTimestamp
      : toTimeString;

  return `${from} - ${to}`;
};

export const getTimeZone = (timeZone: string = undefined): string =>
  new Date()
    .toLocaleDateString(undefined, {
      day: '2-digit',
      timeZoneName: 'short',
      timeZone,
    })
    .slice(4);

export const differenceInDays = (
  date1: Date | string | number,
  date2: Date | string | number,
) => {
  const fromDate = new Date(date1);
  const toDate = new Date(date2);
  let daysDiff = 0;

  if (fromDate < toDate) {
    daysDiff = Math.ceil(
      Math.abs(toDate.valueOf() - fromDate.valueOf()) / (1000 * 60 * 60 * 24),
    );
  }

  return daysDiff;
};

// This function is for creating an array with continuous date where start from startDate value we pass
export function generateDatesBetween(
  startDate: Date | string | number,
  endDate: Date | string | number,
): Array<Date | string | number> {
  const dates = [];
  const datesDiffer = differenceInDays(startDate, endDate);
  for (let i = 0; i <= datesDiffer; i += 1) {
    const startDateValue = new Date(startDate).valueOf();
    dates.push(new Date(startDateValue.valueOf() + 1000 * 3600 * (i * 24)));
  }
  return dates;
}

// eslint-disable-next-line
export const getDateRange = periodValue => {
  const today = new Date(); // Current date

  let from;
  let to;

  switch (periodValue) {
    // Year
    case 'thisYear':
      from = new Date(today);
      from.setMonth(0, 1); // Jan 1st of this year
      to = new Date(today);
      to.setMonth(11, 31); // Dec 31st of this year
      break;

    case 'lastYear':
      const lastYear = new Date(today);
      lastYear.setFullYear(today.getFullYear() - 1); // Last year
      from = new Date(lastYear);
      from.setMonth(0, 1); // Jan 1st of last year
      to = new Date(lastYear);
      to.setMonth(11, 31); // Dec 31st of last year
      break;

    // Month
    case 'lastMonth':
      const lastMonth = new Date(today);
      lastMonth.setMonth(lastMonth.getMonth() - 1); // Go to the previous month
      from = new Date(lastMonth);
      from.setDate(1); // First day of last month
      to = new Date(lastMonth);
      to.setMonth(lastMonth.getMonth() + 1, 0); // Last day of last month
      break;

    case 'thisMonth':
      from = new Date(today);
      from.setDate(1); // First day of this month
      to = new Date(today);
      to.setMonth(today.getMonth() + 1, 0); // Last day of this month
      break;

    // Last 3 months
    case 'last3Month':
      // Set `from` to the first day of the month, 3 months ago
      from = new Date(today);
      from.setMonth(today.getMonth() - 2); // Move 3 months back
      from.setDate(1); // First day of the month

      // Set `to` to the last day of the previous month (just before today)
      to = new Date(today);
      to.setMonth(today.getMonth() + 1, 0); // Last day of the current month
      break;

    // Last 6 months
    case 'last6Month':
      from = new Date(today);
      from.setMonth(today.getMonth() - 5); // Move 6 months back from today
      from.setDate(1); // Set `from` to the first day of the month

      // Set `to` to the last day of the previous month
      to = new Date(today);
      to.setMonth(today.getMonth() + 1, 0); // Set to the last day of the current month
      break;

    // Last 12 months
    case 'last12Month':
      from = new Date(today);
      from.setMonth(today.getMonth() - 11); // Move 12 months back from today
      from.setDate(1); // Set `from` to the first day of the month

      // Set `to` to the last day of the previous month
      to = new Date(today);
      to.setMonth(today.getMonth() + 1, 0); // Set to the last day of the current month
      break;

    // Week
    case 'thisWeek':
      const startOfWeek = new Date(today);
      startOfWeek.setDate(today.getDate() - today.getDay()); // Sunday (start of the week)
      from = new Date(startOfWeek);
      to = new Date(startOfWeek);
      to.setDate(startOfWeek.getDate() + 6); // Saturday (last day of the week)
      break;

    case 'lastWeek':
      const lastWeekStart = new Date(today);
      lastWeekStart.setDate(today.getDate() - (today.getDay() + 7)); // Last Sunday
      from = new Date(lastWeekStart);
      to = new Date(lastWeekStart);
      to.setDate(lastWeekStart.getDate() + 6); // Last Saturday
      break;

    // Day
    case 'yesterday':
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1); // Subtract 1 day for yesterday
      from = new Date(yesterday);
      to = new Date(yesterday); // Same day
      break;

    case 'today':
      from = new Date(today);
      to = new Date(today); // Same day
      break;

    case 'last7Days':
      from = new Date(today);
      from.setDate(today.getDate() - 6); // 7 days ago
      to = new Date(today); // Today
      break;

    case 'last30Days':
      from = new Date(today);
      from.setDate(today.getDate() - 29); // 30 days ago
      to = new Date(today); // Today
      break;

    default:
      throw new Error('Unknown period value');
  }

  return { from, to };
};
