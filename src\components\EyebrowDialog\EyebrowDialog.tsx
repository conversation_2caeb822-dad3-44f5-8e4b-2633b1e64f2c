import { FC } from 'react';
import DialogModal from '@components/DialogModal';
import { ModalAction } from '@constants/components';
import useMediaQueryState from '@hooks/useMediaQueryState';
import variablesStyles from '@styles/variables.module.css';

import { EyebrowDialogTypes } from './EyebrowDialog.types';

/**
 * The EyebrowDialog component renders a modal dialog with customizable content and actions.
 * It supports features such as setting modal configuration, content, actions, and styling.
 *
 * @param {EyebrowDialogTypes} props - The props for the EyebrowDialog component.
 * @param {EyebrowDialogConfig} props.modalConfig - Required. An object containing configuration settings for the modal.
 * @param {EyebrowDialogContent} [props.modalContent] - Optional. An object containing content for the modal.
 * @param {EyebrowDialogActions} [props.modalActions] - Optional. An object containing actions for the modal.
 * @param {EyebrowDialogClassNames} [props.modalClassNames] - Optional. An object containing class names for styling various parts of the modal.
 * @returns {JSX.Element} - The rendered EyebrowDialog component.
 */
const EyebrowDialog: FC<EyebrowDialogTypes> = ({
  modalConfig,
  modalContent,
  modalActions,
  modalClassNames,
}) => {
  const {
    maxWidth = 620,
    isActive,
    modalAcceptActionType = ModalAction.ACCEPT,
    modalDeclineActionType = ModalAction.DECLINE,
    hasFullWidthButtons,
    isAcceptLoading,
    isDeclineLoading,
    declineButtonTheme,
    acceptButtonTheme,
    modalName,
    acceptButtonDisabled,
    declineButtonDisabled,
    isAnimated,
    hasCloseButton,
    maxHeightMobile,
    hasDialogWrapperStyles = true,
    isMobileCentered,
    isHeadingStatic = false,
    hasNoPaddingAroundForBody,
    maxBodyHeight = 'none',
  } = modalConfig ?? {};

  const {
    illustrationContent,
    preHeadingContent,
    headingContent,
    postHeadingContent,
    bodyContent,
    acceptLabel,
    declineLabel,
    acceptButtonRight,
    declineButtonRight,
    acceptButtonLeft,
    declineButtonLeft,
    headerwrapperContent,
  } = modalContent ?? {};

  const {
    dialogWrapperClassName,
    headerWrapperClassName,
    headerWrapperContainerClassName,
    bodyWrapperClassName,
    bodyWrapperContainerScrollClassName,
  } = modalClassNames ?? {};

  const { onClose, onAccept, onDecline, trackModalAction } = modalActions ?? {};

  const isMobile = useMediaQueryState({
    query: `(max-width: ${variablesStyles.smallWidth})`,
  });
  return (
    <DialogModal
      tracker={trackModalAction}
      maxWidth={maxWidth}
      isActive={isActive}
      onModalOutsideClick={onClose}
      name={modalName}
      isAnimated={isAnimated}
      maxHeightMobile={maxHeightMobile}
      isMobileCentered={isMobileCentered}
      wrapperClassName={dialogWrapperClassName}
    >
      <DialogModal.Modal
        onClose={onClose}
        hasCloseButton={hasCloseButton}
        hasDialogWrapperStyles={hasDialogWrapperStyles}
      >
        <DialogModal.Header
          wrapperClassName={headerWrapperClassName}
          wrapperContainerClassName={headerWrapperContainerClassName}
          isHeadingStatic={isHeadingStatic}
          headingStaticContent={headingContent}
        >
          {!headerwrapperContent ? (
            <>
              {illustrationContent && (
                <DialogModal.Illustration>
                  {illustrationContent}
                </DialogModal.Illustration>
              )}

              {preHeadingContent && (
                <DialogModal.Title>{preHeadingContent}</DialogModal.Title>
              )}

              {headingContent && !isHeadingStatic && (
                <DialogModal.Heading>{headingContent}</DialogModal.Heading>
              )}

              {postHeadingContent && (
                <DialogModal.SubHeading>
                  {postHeadingContent}
                </DialogModal.SubHeading>
              )}
            </>
          ) : (
            headerwrapperContent
          )}
        </DialogModal.Header>

        {bodyContent && (
          <DialogModal.Body
            hasNoPaddingAround={hasNoPaddingAroundForBody}
            wrapperClassName={bodyWrapperClassName}
            wrapperContainerScrollClassName={
              bodyWrapperContainerScrollClassName
            }
            maxBodyHeight={maxBodyHeight}
          >
            {bodyContent}
          </DialogModal.Body>
        )}

        {(acceptLabel || declineLabel) && (
          <DialogModal.Footer>
            {acceptLabel && (
              <DialogModal.Button
                label={acceptLabel}
                buttonSize={isMobile ? 'small' : 'large'}
                buttonRight={acceptButtonRight}
                buttonLeft={acceptButtonLeft}
                onClick={onAccept}
                theme={acceptButtonTheme}
                modalButtonActionType={modalAcceptActionType}
                isLoading={isAcceptLoading}
                isFullWidthButton={hasFullWidthButtons}
                isDisabled={acceptButtonDisabled}
                modalName={modalName}
                tracker={trackModalAction}
              />
            )}

            {declineLabel && (
              <DialogModal.Button
                label={declineLabel}
                buttonSize={isMobile ? 'small' : 'large'}
                buttonRight={declineButtonRight}
                buttonLeft={declineButtonLeft}
                onClick={onDecline}
                theme={declineButtonTheme}
                modalButtonActionType={modalDeclineActionType}
                isFullWidthButton={hasFullWidthButtons}
                isLoading={isDeclineLoading}
                isDisabled={declineButtonDisabled}
                modalName={modalName}
                tracker={trackModalAction}
              />
            )}
          </DialogModal.Footer>
        )}
      </DialogModal.Modal>
    </DialogModal>
  );
};

export default EyebrowDialog;
