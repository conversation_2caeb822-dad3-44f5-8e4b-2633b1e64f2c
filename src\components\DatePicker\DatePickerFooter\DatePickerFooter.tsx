import React from 'react';
import Button from '@components/Button';

import styles from '../DatePicker.module.css';

interface DatePickerFooterProps {
  onTodayClick: () => void;
}

const DatePickerFooter: React.FC<DatePickerFooterProps> = ({
  onTodayClick,
}) => (
  <div className={styles.footerWrapper}>
    <Button
      theme="grey"
      size="xSmall"
      onClick={onTodayClick}
      className={styles.clearButton}
      label="Today"
    />
  </div>
);

export default DatePickerFooter;
