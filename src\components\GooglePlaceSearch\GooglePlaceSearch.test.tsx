import React from 'react';
import { fireEvent, render, screen } from '@testing-library/react';

import GooglePlaceSearch from './GooglePlaceSearch';

// Mock items to be used in the dropdown
const mockItems = [
  { value: '1', label: 'First Item' },
  { value: '2', label: 'Second Item' },
];

// Setup function to render the component
const setup = (props = {}) => {
  const utils = render(
    <GooglePlaceSearch
      id="streetID"
      label="Street Address"
      items={mockItems}
      inputValue=""
      value={null}
      onChange={jest.fn()}
      onInputValueChange={jest.fn()}
      required
      {...props}
    />,
  );

  const input = utils.getByRole('textbox');
  return {
    input,
    ...utils,
  };
};

describe('GooglePlaceSearch Component', () => {
  test('should render the component', () => {
    setup();
    expect(screen.getByText('Street Address')).toBeInTheDocument();
  });

  test('should apply correct CSS classes', () => {
    const { input } = setup();
    fireEvent.focus(input);
    expect(input.closest('.combobox')).toHaveClass('focused');
  });

  test('should handle required property', () => {
    const { input } = setup();
    expect(input).toBeRequired();
  });

  test('should trigger onChange event when an item is selected', () => {
    const onChangeMock = jest.fn();
    const { input } = setup({ onChange: onChangeMock });

    // Type in the input to open the dropdown
    fireEvent.change(input, { target: { value: 'First' } });

    // Ensure the dropdown items are rendered
    const dropdownItem = screen.getByText('First Item');
    expect(dropdownItem).toBeInTheDocument();

    // Select the first item
    fireEvent.click(dropdownItem);

    // Check if onChange is called with the correct item
    expect(onChangeMock).toHaveBeenCalledWith(mockItems[0]);
  });
});
