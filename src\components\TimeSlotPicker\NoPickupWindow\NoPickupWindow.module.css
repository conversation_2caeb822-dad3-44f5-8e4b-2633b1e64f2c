@value typography: "../../../styles/typography.module.css";
@value variables: "../../../styles/variables.module.css";
@value black, neutralLight, twoSpace, oneSpace, smallWidth from variables;
@value caption from typography;

.emptyState {
  color: black;
  border-radius: 8px;
  border: 1px solid neutralLight;
  width: 100%;
  padding: twoSpace;
  text-align: center;
  max-width: 635px;
  margin-top: oneSpace;
  margin-bottom: oneSpace;
  margin-left: oneSpace;

  @media (max-width: smallWidth) {
    margin-left: 0;
  }
}

.emptyStateParagraph {
  composes: caption;
  margin-right: auto;
  margin-left: auto;
}
