import React, { CSSProperties, useCallback } from 'react';
import { Column, flexRender } from '@tanstack/react-table';
import Shimmer from '@components/Shimmer';
import classNames from '@utils/classNames';
import getRandomNumber from '@utils/getRandomNumber';

import styles from './TableRow.module.css';

interface TextRowProps {
  /**
   * Callback function to be executed when the row is clicked.
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onClick?: (data: any) => void;

  /**
   * Indicates whether the row should be faded.
   */
  isRowFaded?: boolean;

  /**
   * The data object representing the row.
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  row: Record<string, any>;

  /**
   * Array of column configurations.
   */
  column: Array<{
    minWidth?: number;
    width?: number;
    shimmer?: {
      count?: number;
      width?: number | string;
      minWidth?: number;
      maxWidth?: number;
    };
  }>;

  /**
   * Indicates whether the row is a secondary row.
   */
  isSecondary?: boolean;

  /**
   * Function to customize the rendering of the row wrapper.
   */
  rowRender?: (element: React.ReactNode) => React.ReactNode;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getCommonPinningStyles = (column: Column<any>): CSSProperties => {
  const isPinned = column.getIsPinned();

  return {
    left: isPinned === 'left' ? `${column.getStart('left')}px` : undefined,
    right: isPinned === 'right' ? `${column.getAfter('right')}px` : undefined,
    opacity: 1,
    position: isPinned ? 'sticky' : 'relative',
    width: column.getSize(),
    zIndex: isPinned ? 1 : 0,
  };
};

const TextRow: React.FC<TextRowProps> = ({
  onClick,
  isRowFaded,
  row,
  column,
  isSecondary,
  rowRender,
}) => {
  const isRowLoading = row.original.isLoading;
  const renderShimmersFromCellIndex = useCallback(
    (cellIndex: string | number) => {
      const shimmerOptions = column?.[cellIndex]?.shimmer;

      let finalWidth: number | string = '75%';
      const finalHeight: number | string = '16px';
      const finalCount = shimmerOptions?.count || 1;

      if (shimmerOptions) {
        const { width, minWidth, maxWidth } = shimmerOptions;

        if (width) {
          finalWidth = width;
        } else if (minWidth && maxWidth) {
          finalWidth = getRandomNumber(minWidth, maxWidth);
        }
      }

      return Array.from(Array(finalCount)).map((_, index) => (
        <span data-testid="Shimmer">
          <Shimmer
            // eslint-disable-next-line react/no-array-index-key
            key={`shimmer-${index}`}
            width={finalWidth}
            height={finalHeight}
          />
        </span>
      ));
    },
    [column],
  );

  const rowProps = {
    className: classNames(
      styles.row,
      onClick && styles.rowClickable,
      isRowLoading && styles.isLoading,
      isRowFaded && styles.isFaded,
      isSecondary || (row.index % 2 !== 0 && styles.isSecondary),
    ),
    key: row.id,
  };

  const rnRow = () =>
    row.getVisibleCells().map((cell, index) => {
      if (isRowLoading) {
        return (
          <td
            className={classNames(
              styles.tableRow,
              row.getIsSelected() && styles.isSelected,
            )}
            key={cell.id}
            style={{ ...getCommonPinningStyles(cell.column) }}
          >
            <div
              className={styles.cell}
              style={{
                maxWidth: cell.column.columnDef.maxSize,
              }}
            >
              <div className={classNames(styles.cellContents, styles.shimmer)}>
                {renderShimmersFromCellIndex(index)}
              </div>
            </div>
          </td>
        );
      }

      return (
        <td
          className={classNames(
            styles.tableRow,
            row.getIsSelected() && styles.isSelected,
          )}
          key={cell.id}
          style={{ ...getCommonPinningStyles(cell.column) }}
        >
          <div
            className={styles.cell}
            style={{
              maxWidth: cell.column.columnDef.maxSize,
            }}
          >
            <div className={styles.cellContents}>
              {flexRender(cell.column.columnDef.cell, cell.getContext())}{' '}
            </div>{' '}
          </div>
        </td>
      );
    });

  const renderRowWrapper = rowRender
    ? rowRender(<span {...rowProps}>{rnRow()}</span>)
    : rnRow();

  const handleRowClick = useCallback(
    () => onClick(row.original),
    [onClick, row.original],
  );

  return (
    <tr
      onClick={handleRowClick}
      aria-label="table-row"
      key={row.id}
      className={classNames(
        rowProps.className,
        row.getIsSelected() && styles.isSelected,
      )}
    >
      {renderRowWrapper}
    </tr>
  );
};
export default TextRow;
