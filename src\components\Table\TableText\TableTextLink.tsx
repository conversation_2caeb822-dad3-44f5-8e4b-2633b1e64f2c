import React from 'react';
import { EMPTY_TABLE_VALUE } from '@constants/common';
import classNames from '@utils/classNames';

import styles from './TableText.module.css';

interface TableTextLinkProps {
  isInverted?: boolean;
  isActive?: boolean;
  customStyle?: React.CSSProperties;
  className?: string;
  label: string;
  onClick: (event: React.MouseEvent<HTMLAnchorElement>) => void;
  component: React.ElementType;
}

const TableTextLink: React.FC<TableTextLinkProps> = ({
  isInverted,
  isActive,
  customStyle,
  className,
  label = EMPTY_TABLE_VALUE,
  onClick,
  component,
  ...props
}) => {
  const LinkComponent = component || 'a';
  return (
    <div
      className={classNames(
        styles.text,
        styles.link,
        isActive && styles.active,
        isInverted && styles.isInverted,
        className,
      )}
      style={customStyle}
    >
      <LinkComponent onClick={onClick} {...props}>
        {label}
      </LinkComponent>
    </div>
  );
};

export default TableTextLink;
