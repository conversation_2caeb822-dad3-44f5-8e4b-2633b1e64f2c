@value variables: "../../styles/variables.module.css";
@value fourSpace, eightSpace, black50Opaque, fiveSpace, neutral, primary, tableHeaderZIndex, black, black5, black50, mediumWidth, black, white5, white10, white70, white, neutralLightest, ease-out-expo, aboveZIndex, oneSpace, halfSpace, twoSpace, neutralLightest, neutralLightTable, smallWidth from variables;
@value typography: "../../styles/typography.module.css";
@value overline, bodySmall, bodyBase, bodySmallStrong from typography;

.tableMain {
  position: relative;

  @media (max-width: smallWidth) {
    display: none;
  }
}

.table {
  width: 100%;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;

  @media (max-width: mediumWidth) {
    display: block;
    min-width: 100%;
  }
}

.isInverted {
  color: white70;
  & .rowClickable {
    padding-left: twoSpace;
    &:hover {
      background-color: white5;
      border-radius: 16px;
    }
  }

  & .headerGroup {
    background-color: inherit;
    padding-left: twoSpace;
    color: white70;
    &.headerSortable {
      &:focus {
        color: primary;
      }
    }
    &.headerSorted {
      color: white;
    }
  }
}

.tableMobileBody {
  overflow: auto;
  padding: oneSpace;
}

.tableMobileRoot {
  position: relative;
}

.centerContent {
  align-items: center;
  display: flex;
  justify-content: center;
  margin: twoSpace;
}

.centerRecord {
  text-align: center;
  /* making important to override default table styling */
  background-color: white !important;

  &.hasNoRecordConfig {
    padding: eightSpace 0;
  }
}

.tableRoot {
  overflow: auto;
  position: relative;
  margin: oneSpace;
  padding-top: 0;
  @media (max-width: smallWidth) {
    padding: 0;
    margin: oneSpace;
  }

  th {
    background-color: lightgray;
    font-weight: bold;
    position: relative;
    text-align: center;
  }

  tr:nth-child(even) td {
    background-color: neutralLightest;
  }

  tr:nth-child(odd) td {
    background-color: neutralLightTable;
  }

  tfoot tr:last-child td {
    border-bottom: 1px solid var(--footerColor);
    background-color: neutral;
  }

  thead th {
    background-color: white;
  }

  table {
    white-space: nowrap;
    margin: 0;
    border: none;
    border-collapse: separate;
    border-spacing: 0;
    table-layout: fixed;
  }

  table thead th {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  table thead tr {
    z-index: tableHeaderZIndex;
  }

  table thead th {
    z-index: tableHeaderZIndex;
  }

  table td {
    background: white;
  }

  table tbody th {
    font-weight: 100;
    font-style: italic;
    text-align: left;
    position: relative;
  }

  table tbody th {
    position: sticky;
    left: 0;
    background: white;
    z-index: 1;
  }

  tfoot tr {
    bottom: 0;
    position: sticky;
    z-index: 2;
  }

  /*  */

  .isInverted tr td:first-child {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }

  .isInverted tr td:last-child {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .isInverted table td {
    border: 0;
    background-color: black;
  }

  .isInverted .row {
    border-radius: 0;
  }

  .isInverted thead th {
    background-color: black;
  }
}

.topClassName {
  background: linear-gradient(180deg, white 0%, rgba(255, 255, 255, 0) 30.31%);
}

.bottomClassName {
  top: unset;
  bottom: var(--tablePagination);
  /* width: calc(100% - 14px); */

  @media (max-width: smallWidth) {
    top: unset;
    bottom: var(--tablePagination);
  }
}

.footerWrapper {
  margin: 0px oneSpace;
  background-color: neutral;
  border-radius: twoSpace;
  bottom: 0;
  position: sticky;
  z-index: 2;
}

.noRecordHeader {
  margin-top: fourSpace;
}

.noRecordBody {
  margin-top: twoSpace;
  color: black50Opaque;
  max-width: 45ch;
  margin-left: auto;
  margin-right: auto;
  white-space: pre-wrap;
  display: inline-block;
  width: 100%;
  word-wrap: break-word;
}
.indicatorRoot {
  display: flex;
  flex-flow: row;
  align-items: center;
}

.indicatorWrapper {
  line-height: 0;
  margin-right: oneSpace;
}

.clipboardIcon {
  margin-left: oneSpace;
  height: twoSpace;
  cursor: pointer;
}

.label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
