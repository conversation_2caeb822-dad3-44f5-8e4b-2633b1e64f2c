# Welcome to ped-ux-react-library

`@peddle/ped-ux-react-library` is a comprehensive React component library designed to empower developers with a rich set of UI components for building stunning web applications. Whether you're a seasoned developer or just getting started with React, `@peddle/ped-ux-react-library` provides a collection of reusable and customizable components to streamline your development process.

## UI/UX Design Guidelines

`@peddle/ped-ux-react-library` follows modern UI/UX design principles to ensure consistency, usability, and accessibility across all components. Here are some guidelines to consider when using `@peddle/ped-ux-react-library` components:

- **Consistency**: Maintain consistency in layout, typography, and color schemes throughout your application using `@peddle/ped-ux-react-library`'s predefined styles.
- **Usability**: Prioritize user experience by ensuring intuitive interactions and clear visual feedback within your application.
- **Accessibility**: Ensure that `@peddle/ped-ux-react-library` components are accessible to all users, including those with disabilities, by adhering to accessibility standards and guidelines.

## Version Control & Development Tools

`@peddle/ped-ux-react-library` leverages version control and development tools to streamline the development process and ensure collaboration among team members.

- **Version Control**: `@peddle/ped-ux-react-library`'s GitHub repository serves as the central hub for version control, allowing developers to track changes, manage issues, and collaborate on enhancements and bug fixes.
- **Development Tools**: `@peddle/ped-ux-react-library` integrates seamlessly with popular development tools such as npm, webpack, and Babel, enabling smooth integration into your existing development workflow.

## Getting Started

To start using `@peddle/ped-ux-react-library` in your project, follow these simple steps:

1. Install `@peddle/ped-ux-react-library` from npm:

   ```bash
   npm install @peddle/ped-ux-react-library
   ```

   <br />

   Also you'll have to add the following styles in order to get the styles, Please add it under the main file of your project

   ```
   import '@peddleon/apex/dist/styles/base.css';
   import '@peddleon/apex/dist/styles/typography.css';
   import '@peddleon/apex/dist/index.css';
   ```

2. To **run** the storybook within project

   ```bash
   npm run dev:storybook
   ```

## Storybook for the different peddle portals

- [Apex](https://peddle-apex.onrender.com)
- [Seller](https://peddle-marketing-storybook.onrender.com)
- [Marketing](https://peddle-marketing-storybook.onrender.com)
