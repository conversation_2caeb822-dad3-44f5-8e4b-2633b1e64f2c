import { Meta } from '@storybook/react';

import DateView from './DateView';
import { DateViewProps } from './DateView.types';

/**
 * The DateView displays a date in a specific format.
 *
 * ## Overview
 *
 * The DateView is used to display a date in a specified format. It provides a convenient way to present dates in a consistent manner across your application.
 *
 * ## Usage
 *
 * To use the DateView in your React application, import it from the appropriate directory and include it in your JSX.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { DateView } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the DateView within your JSX:
 *
 * ```jsx
 * <DateView date="04-10-2024" />
 * ```
 *
 * This will render the date "04-10-2024" using the default format.
 *
 * NOTE : Supported date format is MM-DD-YYYY
 *
 */

const meta: Meta<typeof DateView> = {
  title: 'Components/DateView',
  tags: ['autodocs'],
  component: DateView,
};

export const Container = ({ date }: DateViewProps) => <DateView date={date} />;

Container.args = {
  date: '04-11-2024',
};

export default meta;
