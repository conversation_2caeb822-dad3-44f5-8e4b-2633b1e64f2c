import { fireEvent, render } from '@testing-library/react';

import Radio from './Radio';

describe('Radio Component', () => {
  test('should render', () => {
    const { getByText } = render(
      <Radio
        id="test-radio"
        label="Test Radio"
        isChecked={false}
        onChange={() => {}}
      />,
    );
    expect(getByText('Test Radio')).toBeInTheDocument();
  });

  test('should render with default checked', () => {
    const { getByLabelText } = render(
      <Radio
        id="test-radio"
        label="Test Radio"
        isChecked
        onChange={() => {}}
      />,
    );
    expect(getByLabelText('Test Radio')).toBeChecked();
  });

  test('should call onChange event when clicked', () => {
    const handleChange = jest.fn();
    const { getByLabelText } = render(
      <Radio
        id="test-radio"
        label="Test Radio"
        isChecked={false}
        onChange={handleChange}
      />,
    );
    fireEvent.click(getByLabelText('Test Radio'));
    expect(handleChange).toHaveBeenCalledTimes(1);
  });

  test('should render with custom className', () => {
    const { getByText } = render(
      <Radio
        id="test-radio"
        label="Test Radio"
        className="custom-radio"
        isChecked={false}
        onChange={() => {}}
      />,
    );
    expect(getByText('Test Radio').parentElement).toHaveClass('custom-radio');
  });
});
