import { fireEvent, render } from '@testing-library/react';

import RadioGroup from './RadioGroup';

describe('RadioGroup Component', () => {
  const options = [
    { id: 'option1', label: 'Option 1', value: 'value1' },
    { id: 'option2', label: 'Option 2', value: 'value2' },
    { id: 'option3', label: 'Option 3', value: 'value3' },
  ];

  test('should render all options', () => {
    const { getByText } = render(
      <RadioGroup options={options} checked="value1" onChange={() => {}} />,
    );
    options.forEach(option => {
      expect(getByText(option.label)).toBeInTheDocument();
    });
  });

  test('should select the checked option by default', () => {
    const { getByLabelText } = render(
      <RadioGroup options={options} checked="value2" onChange={() => {}} />,
    );
    expect(getByLabelText('Option 2')).toBeChecked();
  });

  test('should call onChange event when an option is clicked', () => {
    const handleChange = jest.fn();
    const { getByLabelText } = render(
      <RadioGroup options={options} checked="value1" onChange={handleChange} />,
    );
    fireEvent.click(getByLabelText('Option 3'));
    expect(handleChange).toHaveBeenCalledWith({ checked: 'value3' });
  });

  test('should render with custom className', () => {
    const { getByLabelText } = render(
      <RadioGroup
        options={options}
        checked="value1"
        onChange={() => {}}
        className="custom-radio-group"
      />,
    );
    expect(
      getByLabelText('Option 1').parentElement.parentElement.parentElement,
    ).toHaveClass('custom-radio-group');
  });

  test('should render options in a row', () => {
    const { container } = render(
      <RadioGroup
        options={options}
        checked="value1"
        onChange={() => {}}
        direction="row"
      />,
    );
    const radioGroup = container.querySelector('ul');
    expect(radioGroup).toHaveClass('row');
  });

  test('should disable all options when isDisabled is true', () => {
    const { getByLabelText } = render(
      <RadioGroup
        options={options}
        checked="value1"
        onChange={() => {}}
        isDisabled
      />,
    );
    options.forEach(option => {
      expect(getByLabelText(option.label)).toBeDisabled();
    });
  });
});
