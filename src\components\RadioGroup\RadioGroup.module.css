@value variables: "../../styles/variables.module.css";
@value oneSpace, twoSpace, mediumWidth from variables;

.radioGroup {
  list-style: none;
}

.radioWrapper {
  display: flex;
  margin-bottom: twoSpace;
  justify-content: space-between;
}

.column {
  &.right {
    display: flex;
    justify-content: flex-end;
  }

  &.left {
    & .radioWrapper {
      justify-content: flex-start;
    }
  }

  & .radioButtonWrapper {
    margin-bottom: oneSpace;
  }
}

.row {
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-start;
  align-items: center;

  &.right {
    justify-content: flex-end;
    text-align: right;
    width: 100%;
  }

  & .radioWrapper {
    margin-right: twoSpace;
    margin-bottom: oneSpace;
  }
}

@media (max-width: mediumWidth) {
  .mobileBlock {
    display: block !important;
    margin-left: 0px !important;
    & .radioWrapper {
      margin-right: 0 !important;
    }
  }
}

.right {
  display: inline-flex;
  align-items: center;
  margin-left: oneSpace;
  padding-right: twoSpace;
  &.column {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
}
