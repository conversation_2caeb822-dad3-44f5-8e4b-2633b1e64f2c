@value variables: "../../styles/variables.module.css";
@value oneSpace, halfSpace ,twoSpace,fourSpace, shadow, primary, primaryFade, neutralDark, buttonLabelSmallFontSize from variables;
@value black, black10, ease-out-expo from variables;

.menu {
  position: absolute;
  top: oneSpace;
  left: 0;
  z-index: 1;
  background-color: white;
  border-radius: oneSpace;
  width: max-content;
  min-width: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-out-expo;
  width: 100%;

  &.visible {
    opacity: 1;
    border: 1px solid black10;
    box-shadow: shadow;
  }

  &.alignedRight {
    left: auto;
    right: 0;
  }

  &.menuPositionTop {
    bottom: 70px;
    top: unset;
  }
}

.container {
  position: relative;
  border-radius: oneSpace;
}

.list {
  max-height: 33vh;
  overflow-y: auto;
  text-align: left;
  list-style: none;

  &:-moz-focus-inner {
    border: 0;
  }

  &:focus {
    outline: none;
  }
}

.itemHighlighted {
  color: primary;
  background-color: primaryFade;
}

.singleItem {
  padding: oneSpace twoSpace;

  &:first-of-type {
    margin-top: twoSpace;
  }
  &:last-of-type {
    margin-bottom: twoSpace;
  }
}
.item {
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5;
  color: black;
  padding: oneSpace fourSpace;
  cursor: pointer;

  &.itemHighlighted {
    color: primary;
    background-color: primaryFade;
  }

  &.hasLeftItem {
    display: flex;
    align-items: center;
  }
  &.hasSecondaryLabel > span {
    color: neutralDark;
    display: block;
    font-size: buttonLabelSmallFontSize;
  }

  &.isGroupBy {
    &:first-of-type {
      margin-top: 0;
    }

    &:last-of-type {
      margin-bottom: 0;
    }
  }
}

.googleFooter {
  padding: oneSpace twoSpace;
  text-align: right;
  border-top: solid 1px black10;

  & img {
    height: 18px;
    width: auto;
  }
}

.groupHeader {
  font-weight: 600;
  padding: halfSpace oneSpace;
  pointer-events: none;
  color: black;
  padding-left: twoSpace;
}

.isGroupBy {
  padding: oneSpace twoSpace;
}
