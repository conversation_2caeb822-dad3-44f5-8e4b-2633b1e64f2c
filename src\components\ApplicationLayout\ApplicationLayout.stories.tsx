// import { UserIcon } from '@peddleon/ped-ux-react-icons';
import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta, StoryObj } from '@storybook/react';
import typographyStyles from '@styles/typography.module.css';
import variableStyles from '@styles/variables.module.css';

import ApplicationLayout from './ApplicationLayout';

/**
 *
 * This story demonstrates the usage and customization options of the ApplicationLayout component, which provides a versatile layout configuration for React applications. The ApplicationLayout component is designed to offer a cohesive structure to your application by allowing you to define various layout properties such as background color, container width, padding, presence of a footer, navigation bar configuration, and more.
 *
 * ## Overview
 *
 * The ApplicationLayout component acts as a wrapper around your application content, providing essential layout functionalities while ensuring a consistent user experience across different views.
 *
 * ## Usage
 *
 * To integrate the ApplicationLayout component into your React application, follow these steps:
 *
 * 1. Import the component:
 *
 * ```jsx
 * import { ApplicationLayout } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * 2. Use the component as a wrapper around your application content:
 *
 * ```jsx
 * <ApplicationLayout>
 *   {/* Your application content goes here *\/}
 * </ApplicationLayout>
 * ```
 */
const meta: Meta<typeof ApplicationLayout> = {
  component: ApplicationLayout,
  title: 'Layouts/ApplicationLayout',
  tags: ['autodocs'],
  parameters: {
    docs: {
      toc: true,
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
};

type Story = StoryObj<typeof ApplicationLayout>;

export const Basic: Story = {
  render: props => (
    <ApplicationLayout {...props}>
      <div
        style={{
          backgroundColor: 'white',
          borderTopRightRadius: '16px',
          borderTopLeftRadius: '16px',
        }}
      >
        <div
          style={{
            padding: `${variableStyles.fourSpace} ${variableStyles.tenSpace}`,
            height: '200vh',
          }}
        >
          <h1 className={typographyStyles.h1}>Main content</h1>
        </div>
      </div>
    </ApplicationLayout>
  ),
  args: {
    backgroundColor: variableStyles.black,
    mobileBackgroundColor: variableStyles.black,
    isFullContainer: false,
    isWideContainer: false,
    isFluid: true,
    hasBackgroundTransition: true,
    isStickOnTop: false,
    navigationProps: {
      onNavigationClick: () => {},
      logo: '',
      logoUrl: '/',
      leftNavItems: [
        {
          label: 'Dashboard',
          theme: 'transparentInverted',
        },
        {
          label: 'Leads',
          link: '/leads',
          theme: 'transparentInverted',
        },
        {
          label: 'Payments',
          theme: 'transparentInverted',
          listItems: [
            {
              label: 'Open invoices',
              link: 'open-invoices',
            },
            {
              label: 'Payment history',
              link: 'payment-history',
            },
          ],
        },
        {
          label: 'Analytics',
          theme: 'transparentInverted',
          listItems: [
            {
              label: 'Leads',
              link: 'open-invoices',
            },
            {
              label: 'Leads - Custom',
              link: 'open-invoices',
            },
            {
              label: 'Accepted offers',
              link: 'open-invoices',
            },
            {
              label: 'Accepted offers - Custom',
              link: 'open-invoices',
            },
            {
              label: 'Completed offers',
              link: 'open-invoices',
            },
            {
              label: 'Commission',
              link: 'open-invoices',
            },
            {
              label: 'Commission - Custom',
              link: 'open-invoices',
            },
            {
              label: 'Payments',
              link: 'open-invoices',
            },
          ],
        },
      ],
      // rightNavItems: [
      //   {
      //     icon: <UserIcon width={24} height={24} color="currentColor" />,
      //     label: 'Rohan Vachheta',
      //     link: '/users',
      //     theme: 'greyInverted',
      //   },
      //   {
      //     label: 'Get Offer',
      //     link: '/profile',
      //     theme: 'greyInverted',
      //   },
      // ],
      mobileNavButton: [],
      mobileMenuButtons: null,
    },
    footer: null,
  },
};

export default meta;
