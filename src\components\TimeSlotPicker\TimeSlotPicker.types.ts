import { ReactNode } from 'react';

export interface TimeSlotItem {
  /**
   * Unique identifier for the item.
   */
  id: string;
  /**
   * Start date and time of the item.
   */
  startAt: string;
  /**
   * End date and time of the item.
   */
  endAt: string;
  /**
   * Start time of the item.
   */
  timeStartAt: string;
  /**
   * End time of the item.
   */
  timeEndAt: string;
}

export interface TimeSlotPickerRowPropTypes {
  /**
   * Test ID for the row.
   */
  dataTestId?: string;
  /**
   * Date associated with the row.
   */
  date: string;
  /**
   * Time zone for the row.
   */
  timeZone?: string;
  /**
   * Currently selected value.
   */
  value?: string;
  /**
   * Array of items for the row.
   */
  items: TimeSlotItem[];
  /**
   * Function triggered on item selection.
   */
  onSelect?: (item: TimeSlotItem) => void;
  /**
   * Specifies if the row is disabled.
   */
  isDisabled?: boolean;
}

export interface TimeSlotPickerPropTypes {
  /**
   *  Array of rows for the time slot picker.
   */
  rowItems: TimeSlotPickerRowPropTypes[];
  /**
   * Time zone for the picker.
   */
  timeZone: string;
  /**
   * Currently selected value.
   */
  value: string;
  /**
   *  Function triggered on selection of a time slot item.
   */
  onSelect?: (item: TimeSlotItem) => void;
  /**
   *  Specifies if the picker is disabled.
   */
  isDisabled?: boolean;
  /**
   *  Specifies if the picker is empty.
   */
  isEmpty?: boolean;
  /**
   *  Specifies if the picker is in loading state.
   */
  isLoading?: boolean;
  /**
   * Specifies if the picker has a date carousel header.
   */
  hasDateCarouselHeader?: boolean;
  /**
   * To set the Component when none of the slots are available, use it as a ReactNode prop.
   */
  renderEmptyState?: ReactNode;
}
