// import React from 'react';
// import { GearsIllustration } from '@peddleon/ped-ux-react-icons';
// import {
//   ERROR_BOUNDARY_BODY,
//   ERROR_BOUNDARY_BUTTON_LABEL,
//   ERROR_BOUNDARY_HEADING,
// } from '@constants/common';

// import { Button, DetailedCTA, Typography } from '..';
// import styles from './ErrorBoundary.module.css';

// interface ErrorBoundaryProps {
//   children: React.ReactNode;
//   registerError?: (error: unknown) => void;
// }

// class ErrorBoundary extends React.Component<
//   ErrorBoundaryProps,
//   { hasError: boolean }
// > {
//   constructor(props: ErrorBoundaryProps) {
//     super(props);
//     this.state = { hasError: false };
//   }

//   componentDidCatch(error: Error) {
//     const { registerError } = this.props;
//     registerError?.(error);
//     this.setState({
//       hasError: true,
//     });
//   }

//   render() {
//     const { hasError } = this.state;
//     const { children } = this.props;
//     if (hasError) {
//       // You can render any custom fallback UI
//       return (
//         <div className={styles.container}>
//           <DetailedCTA
//             illustration={<GearsIllustration height="100%" width="100%" />}
//             footer={null}
//             heading={
//               <Typography
//                 tag="h3"
//                 strong
//                 className={styles.errorBoundaryHeader}
//               >
//                 {ERROR_BOUNDARY_HEADING}
//               </Typography>
//             }
//             body={
//               <Typography
//                 tag="p"
//                 variant="bodyBase"
//                 className={styles.errorBoundaryBody}
//               >
//                 {ERROR_BOUNDARY_BODY}
//               </Typography>
//             }
//             size="medium"
//             action={
//               <Button
//                 label={ERROR_BOUNDARY_BUTTON_LABEL}
//                 theme="warning"
//                 onClick={() => {
//                   // reload page
//                   window?.location.reload();
//                 }}
//               />
//             }
//             position="center"
//           />
//         </div>
//       );
//     }
//     return children;
//   }
// }

// export default ErrorBoundary;
