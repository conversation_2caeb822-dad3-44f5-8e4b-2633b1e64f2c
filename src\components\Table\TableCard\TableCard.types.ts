import React, { ReactNode } from 'react';
import { ItemType } from '@components/Dropdown/Dropdown';

export interface TableCardPropTypes {
  /**
   * To uniquely identify a dropdown while rendering it with other dropdowns.
   */
  id: string;
  /**
   * Content to be displayed on the right/middle side of the card header.
   * Defaults to `null`.
   */
  cardHeaderRight?: ReactNode;
  /**
   * Content to be displayed on the left side of the card header.
   * Defaults to `null`.
   */
  cardHeaderLeft?: ReactNode;

  /**
   * The main title to be displayed in the card body.
   */
  bodyTitle: string;

  /**
   * The subtitle to be displayed below the main title in the card body.
   */
  bodySubTitle: string;

  /**
   * The content to display / button in body right side
   */
  cardBodyRight?: ReactNode;
  /**
   * Content to be displayed on the left side of the card footer.
   */
  footerLeftContent: ReactNode;

  /**
   * Content to be displayed on the right side of the card footer.
   */
  footerRightContent: ReactNode;

  /**
   * List of items for the dropdown menu, where each item has a label,
   * and optionally an id and a link. Defaults to an empty array.
   */
  actionItems?: ItemType;

  /**
   * On menu item click event listner
   */
  onMenuItemClick?: (dropdownId: string | number, id: string | number) => void;
  /**
   * onClick on the card
   */
  onClick?: () => void;
  /**
   * extraStyles to override in the card
   */
  extraStyles?: {
    cardHeader?: string;
    cardHeaderRight?: string;
    cardBody?: string;
    cardContent?: string;
    cardBodyTitle?: string;
    cardBodySubTitle?: string;
    cardFooter?: string;
    cardFooterLeftContent?: string;
    cardFooterRightContent?: string;
  };
  /**
   * isLoading: To show loader in the card
   */
  isLoading?: boolean;
  /**
   * rowSelection: To show checkbox in the card
   */
  rowSelection?: { [key: number]: boolean };
  /**
   * index: Index of the card
   */
  index: number;
  /**
   * handleRowSelection: Callback function to handle row selection
   */
  handleRowSelection: (
    index: number,
    isChecked: boolean,
    event: React.ChangeEvent<HTMLInputElement>,
  ) => void;
  /**
   *  hasRowSelection: To show checkbox in the card
   */
  hasRowSelection?: boolean;
}
