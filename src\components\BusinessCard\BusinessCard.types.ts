export interface BusinessCardFormData {
    /**
     * The first name of the person on the business card
     */
    firstName: string;

    /**
     * The last name of the person on the business card
     */
    lastName: string;

    /**
     * The phone number of the person
     */
    phoneNumber: string;

    /**
     * The email address of the person
     */
    email: string;

    /**
     * The organization or company name
     */
    organization: string;

    /**
     * The job title or position of the person
     */
    title: string;
}

/**
 * Props interface for the BusinessCard component
 */
export interface BusinessCardProps {
    /**
     * Callback function called when PDF is successfully generated
     * @param formData The form data used to generate the card
     */
    onGenerate?: (formData: BusinessCardFormData) => void;

    /**
     * Callback function called when an error occurs during PDF generation
     * @param error The error that occurred
     */
    onError?: (error: Error) => void;

    /**
     * Initial data to populate the form fields
     */
    initialData?: Partial<BusinessCardFormData>;
}


export type BusinessCardFormErrors = Partial<
    Record<keyof BusinessCardFormData, string>
>;
