import { FC, useMemo, useRef } from 'react';
import Carousel from '@components/Carousel';
import DateView from '@components/DateView';
import { TIME_SLOT_PICKER_HEADER_DATA_TEST_ID } from '@constants/dataTestId';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import { formatDate, generateHref } from '@utils/date';
import variableStyles from '@styles/variables.module.css';

import styles from './TimeSlotPickerHeader.module.css';
import { TimeSlotPickerHeaderPropTypes } from './TimeSlotPickerHeader.types';

const getActiveDateIndex = (activeDate, items) => {
  const index = items.findIndex(
    item => formatDate(item.date) === formatDate(activeDate),
  );
  return index === -1 ? 0 : index;
};

const TimeSlotPickerHeader: FC<TimeSlotPickerHeaderPropTypes> = ({
  items,
  activeDate,
  onClick,
}) => {
  const columnsRef = useRef(new Array(items.length));
  const isMobile = useMediaQueryState({
    query: `(max-width: ${variableStyles.smallWidth})`,
  });

  const numItems = items.length;
  const visibleSlides = isMobile ? 5 : 7;
  const numPages =
    numItems === 0 ? 0 : Math.ceil((items.length - 1) / visibleSlides);
  const activeDateIndex = useMemo(
    () => (numItems === 0 ? 0 : getActiveDateIndex(activeDate, items)),
    [activeDate, items, numItems],
  );
  // maxPageIndex helps us keep the right-most item flush to the right side of the carousel
  const maxPageIndex = numPages < 2 ? 0 : numItems - 1 - (visibleSlides - 1);
  const activePageIndex =
    activeDateIndex >= maxPageIndex ? maxPageIndex : activeDateIndex;

  return (
    <div
      className={styles.header}
      data-testid={TIME_SLOT_PICKER_HEADER_DATA_TEST_ID}
    >
      <Carousel
        items={items}
        visibleSlides={visibleSlides}
        step={visibleSlides}
        currentSlide={activePageIndex}
        isIntrinsicHeight
        renderSlide={({ date }, index) => (
          // eslint-disable-next-line jsx-a11y/control-has-associated-label
          <a
            className={classNames(
              styles.column,
              activeDateIndex === index && styles.active,
            )}
            tabIndex={index + 1}
            href={generateHref({ date })}
            ref={$el => {
              columnsRef.current[index] = $el;
            }}
            onClick={event => onClick({ event, date, index })}
          >
            <DateView
              date={date}
              monthClassName={styles.month}
              weekdayClassName={styles.weekday}
            />
          </a>
        )}
      />
    </div>
  );
};

export default TimeSlotPickerHeader;
