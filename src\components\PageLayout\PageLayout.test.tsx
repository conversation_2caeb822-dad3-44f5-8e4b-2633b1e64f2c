import { render } from '@testing-library/react'; // Import extend-expect

import PageLayout from './PageLayout';

describe('PageLayout Component', () => {
  it('renders children and applies background color', () => {
    // Render the component with some props
    const { getByText, container } = render(
      <PageLayout backgroundColor="red">
        <div>Test Child</div>
      </PageLayout>,
    );

    // Assert that children are rendered
    expect(getByText('Test Child')).toBeInTheDocument();

    // Assert that background color is applied
    expect(container.firstChild).toHaveStyle('background-color: red');
  });

  it('applies overflow hidden style when isOverflowHidden prop is true', () => {
    // Render the component with isOverflowHidden prop set to true
    const { container } = render(
      <PageLayout isOverflowHidden>
        <div>Test Child</div>
      </PageLayout>,
    );

    // Assert that overflow hidden style is applied
    expect(container.firstChild).toHaveClass('isOverflowHidden');
  });

  it('applies background transition style when hasBackgroundTransition prop is true', () => {
    // Render the component with hasBackgroundTransition prop set to true
    const { container } = render(
      <PageLayout hasBackgroundTransition>
        <div>Test Child</div>
      </PageLayout>,
    );

    // Assert that background transition style is applied
    expect(container.firstChild).toHaveClass('backgroundTransition');
  });

  it('displays sidebar', () => {
    // Render the component with hasBackgroundTransition prop set to true
    const { getByText } = render(
      <PageLayout hasBackgroundTransition sidebar={<div>sidebar</div>}>
        <div>Test Child</div>
      </PageLayout>,
    );

    // Assert that children are rendered
    expect(getByText('Test Child')).toBeInTheDocument();

    // Assert that sidebar are rendered
    expect(getByText('sidebar')).toBeInTheDocument();
  });

  it('displays footer', () => {
    // Render the component with hasBackgroundTransition prop set to true
    const { getByText } = render(
      <PageLayout hasBackgroundTransition footer={<div>footer</div>}>
        <div>Test Child</div>
      </PageLayout>,
    );

    // Assert that children are rendered
    expect(getByText('Test Child')).toBeInTheDocument();

    // Assert that footer are rendered
    expect(getByText('footer')).toBeInTheDocument();
  });
});
