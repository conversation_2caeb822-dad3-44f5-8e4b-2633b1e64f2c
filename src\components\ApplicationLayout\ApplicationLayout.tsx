import React, { FC, forwardRef, useMemo, useRef, useState } from 'react';
import AdminFrame from '@components/AdminFrame';
import styles from '@components/ApplicationLayout/ApplicationLayout.module.css';
import Container from '@components/Container';
import EnvironmentFrame from '@components/EnvironmentFrame';
import ErrorBoundary from '@components/ErrorBoundary';
import FluidRoot from '@components/FluidRoot';
import { NavigationBarProps } from '@components/NavigationBar/NavigationBar';
import NavigationContainer from '@components/NavigationContainer';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import mergeRefs from '@utils/mergeRefs';
import variableStyles from '@styles/variables.module.css';

interface ApplicationLayoutProps {
  /**
   * Specifies the background color of the application layout.
   */
  backgroundColor?: string;

  /**
   * Specifies the background color of the application layout specifically for mobile devices.
   */
  mobileBackgroundColor?: string;

  /**
   * Indicates whether the container should be wide.
   */
  isWideContainer?: boolean;

  /**
   * Indicates whether the container should occupy the full width of the viewport.
   */
  isFullContainer?: boolean;

  /**
   * Indicates whether the layout should be fluid, meaning it adjusts its width based on the viewport size.
   */
  isFluid?: boolean;

  /**
   * Indicates whether the admin frame is shown.
   */
  isAdminFrameShown?: boolean;

  /**
   * Specifies the label for the admin frame if it's shown.
   */
  adminFrameLabel?: string;

  /**
   * Content to be displayed on the right side of the admin frame.
   */
  adminFrameRight?: React.ReactNode;

  /**
   * Content to be displayed on the left side of the admin frame.
   */
  adminFrameLeft?: React.ReactNode;

  /**
   * Specifies the environment (e.g., development, production) for the application.
   */
  environment?: string;

  /**
   * Indicates whether overflow should be hidden within the layout.
   */
  isOverflowHidden?: boolean;

  /**
   * Indicates whether background transition effects are enabled.
   */
  hasBackgroundTransition?: boolean;

  /**
   * The content to be rendered within the application layout.
   */
  children?: React.ReactNode;

  /**
   * Content to be rendered as the footer of the application layout.
   */
  footer?: React.ReactNode | null;

  /**
   * Indicates whether the layout should stick to the top of the viewport.
   */
  isStickOnTop?: boolean;
  /**
   * register/catch the global Error, generally attach with third party logger
   */
  registerError?: (error: unknown) => void;
  /**
   * Additional props for the NavigationBar component. It may include more detailed navigation properties specific to the NavigationBar component.
   */
  navigationProps?: NavigationBarProps;
}

// Define types for props
// Default values for navigation height
const DEFAULT_NAVIGATION_HEIGHT = '104px';
const DEFAULT_MOBILE_NAVIGATION_HEIGHT = '88px';
const DEFAULT_NAVIGATION_HEIGHT_WHEN_MENU_CLOSED = '96px';
const DEFAULT_NAVIGATION_HEIGHT_WHEN_HIDDEN = '48px';

const ApplicationLayout: FC<ApplicationLayoutProps> = forwardRef<
  HTMLDivElement,
  ApplicationLayoutProps
>(
  (
    {
      backgroundColor = variableStyles.black,
      mobileBackgroundColor = variableStyles.black,
      isWideContainer = false,
      isFullContainer = false,
      isFluid = false,
      isAdminFrameShown = false,
      adminFrameLabel = null,
      adminFrameRight,
      adminFrameLeft,
      environment,
      isOverflowHidden = false,
      hasBackgroundTransition = true,
      children,
      footer = null,
      isStickOnTop = false,
      navigationProps,
      registerError = () => {},
    },
    ref,
  ) => {
    // Reference to the layout element
    const layoutRef = useRef<HTMLDivElement>(null);
    const isLargeWidth = useMediaQueryState({ query: '(max-width: 1080px)' });
    const isMobile = useMediaQueryState({ query: '(max-width: 480px)' });
    const [isMobileChromeCollapsed, setIsMobileChromeCollapsed] =
      useState(false);

    // Merge refs
    const layoutRefs = useMemo(
      () => mergeRefs(layoutRef, ref),
      [layoutRef, ref],
    );

    const onNavigationHeightChange = ({
      height: navigationHeight,
    }: {
      height: number;
    }) => {
      const hasFrame = (isAdminFrameShown || environment) && isLargeWidth;
      if (layoutRef.current)
        layoutRef.current.style.setProperty(
          '--navigationHeight',
          `${navigationHeight + (hasFrame ? 48 : 0)}px`,
        );
      layoutRef.current.style.setProperty('--backgroundColor', backgroundColor);
      layoutRef.current.style.setProperty(
        '--mobileBackgroundColor',
        mobileBackgroundColor,
      );
    };

    const hasFrame = isAdminFrameShown || environment;

    return (
      <ErrorBoundary registerError={registerError}>
        <FluidRoot isFluid={isFluid}>
          <div
            ref={layoutRefs}
            className={classNames(
              styles.layout,
              hasBackgroundTransition && styles.backgroundTransition,
              isOverflowHidden && styles.isOverflowHidden,
            )}
            style={{
              '--backgroundColor': backgroundColor,
              '--mobileBackgroundColor': mobileBackgroundColor,
              '--navigationHeight': isMobile
                ? DEFAULT_MOBILE_NAVIGATION_HEIGHT
                : DEFAULT_NAVIGATION_HEIGHT,
              '--navigationHeightWhenMenuClosed':
                DEFAULT_NAVIGATION_HEIGHT_WHEN_MENU_CLOSED,
              '--navigationHeightWhenHidden':
                DEFAULT_NAVIGATION_HEIGHT_WHEN_HIDDEN,
            }}
          >
            <div className={classNames(styles.navigationWrapper)}>
              <NavigationContainer
                isStickOnTop={isStickOnTop}
                onNavigationHeightChange={onNavigationHeightChange}
                isAdminFrameShown={isAdminFrameShown}
                environment={environment}
                isNavigationHidden={false}
                navigationProps={navigationProps}
                isAdminPanelActive={!!environment || !!isAdminFrameShown}
                backgroundColor={backgroundColor}
                setIsMobileChromeCollapsed={setIsMobileChromeCollapsed}
              />
            </div>
            {isAdminFrameShown && (
              <AdminFrame
                label={adminFrameLabel}
                right={adminFrameRight}
                left={adminFrameLeft}
                isCollapsed={isMobileChromeCollapsed} // Set a default value here, as isMobileChromeCollapsed was not defined
              />
            )}
            {environment && !isAdminFrameShown && (
              <EnvironmentFrame
                label={environment}
                isCollapsed={isMobileChromeCollapsed} // Set a default value here, as isMobileChromeCollapsed was not defined
              />
            )}

            <Container isWide={isWideContainer} isFull={isFullContainer}>
              <div className={styles.childrenWrapper}>
                <div
                  className={classNames(
                    styles.applicationRoot,
                    isStickOnTop && styles.stickyApplicationRoot,
                    hasFrame && styles.applicationRootAdminFrame,
                  )}
                >
                  {children}
                </div>
              </div>
            </Container>

            {footer}
          </div>
        </FluidRoot>
      </ErrorBoundary>
    );
  },
);

export default ApplicationLayout;
