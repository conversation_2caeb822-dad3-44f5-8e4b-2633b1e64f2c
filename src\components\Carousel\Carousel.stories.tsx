/* eslint-disable no-console */
import { Meta } from '@storybook/react';

import Carousel from './Carousel';

/**
 * The Carousel component displays a code snippet with an optional icon.
 *
 * ## Overview
 *
 * The Carousel component renders a code snippet enclosed in a pre tag. It also supports displaying an optional icon.
 *
 * ## Usage
 *
 * To use the Carousel component:
 *
 * Step 1: Import the carousel CSS globally (root) as provided below.
 *
 * ```jsx
 * import 'pure-react-carousel/dist/react-carousel.es.css';
 * ```
 *
 * Step 2: Import the component into your React application:
 *
 * ```jsx
 * import { Carousel } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Step 3: Then, include the Carousel component in your JSX:
 *
 * ```jsx
 * <Carousel
 *   key="slide_Carousel"
 *   currentSlide={1}
 *   items={[
 *     {
 *       id: 1,
 *       title: 'Slide 1'
 *     },
 *     {
 *       id: 2,
 *       title: 'slide 2'
 *     },
 *     {
 *       id: 3,
 *       title: 'slide 3'
 *     },
 *     {
 *       id: 4,
 *       title: 'Slide 4'
 *     },
 *   ]}
 *   renderSlide: item => <h2>{item.title}</h2>
 *   step={1}
 *   visibleSlides={1}
 * />
 * ```
 *
 * This will render a code snippet enclosed in a pre tag with an optional copy button icon.
 */

const meta: Meta<typeof Carousel> = {
  component: Carousel,
  title: 'Utils/Carousel',
  tags: ['autodocs'],
};

export const CarouselStory = carouselProp => (
  <Carousel key="slide_Carousel" {...carouselProp} />
);

CarouselStory.args = {
  items: [
    {
      id: 1,
      title: 'Slide 1',
    },
    {
      id: 2,
      title: 'slide 2',
    },
    {
      id: 3,
      title: 'slide 3',
    },
    {
      id: 4,
      title: 'Slide 4',
    },
  ],
  renderSlide: item => <h2>{item.title}</h2>,
  visibleSlides: 1,
  currentSlide: 1,
  step: 1,
  isIntrinsicHeight: true,
  infinite: true,
  autoScroll: false,
  playDirection: 'forward',
  interval: 1000,
};
CarouselStory.argTypes = {
  playDirection: {
    control: 'select',
    options: ['forward', 'backward'],
  },
};

export default meta;
