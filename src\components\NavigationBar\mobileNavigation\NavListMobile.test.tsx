import React from 'react';
import { render } from '@testing-library/react';
import { NAV_ITEM_MOBILE } from '@constants/dataTestId';

import NavItemMobile from './NavItemMobile';

describe('NavItemMobile', () => {
  test('renders with data-active="true" when isActive is true', () => {
    const label = 'Home';
    const isActive = true;

    const { getByTestId } = render(
      <NavItemMobile label={label} isActive={isActive} />,
    );

    const navItem = getByTestId(NAV_ITEM_MOBILE);
    expect(navItem).toBeInTheDocument();
    expect(navItem).toHaveAttribute('data-active', 'true');
  });

  test('renders with data-active="false" when isActive is false', () => {
    const label = 'Home';
    const isActive = false;

    const { getByTestId } = render(
      <NavItemMobile label={label} isActive={isActive} />,
    );

    const navItem = getByTestId(NAV_ITEM_MOBILE);
    expect(navItem).toBeInTheDocument();
    expect(navItem).toHaveAttribute('data-active', 'false');
  });
});
