import React from 'react';
import { HeaderGroup } from '@tanstack/react-table';
import classNames from '@utils/classNames';

import { FooterConfig } from '../Table';
import styles from './TableFooter.module.css';
import TableFooterRow from './TableFooterRow';

interface TableFooterProps {
  isLoading?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  row: HeaderGroup<any>[];
  footerConfig: FooterConfig[];
}

const TableFooter: React.FC<TableFooterProps> = ({
  isLoading,
  row,
  footerConfig,
}) => {
  const rowProps = {
    className: classNames(isLoading && styles.isLoading, styles.footerRoot),
  };

  const totalColumns = row[0].headers.length;

  return (
    <tr aria-label="table-footer" className={rowProps.className}>
      <td colSpan={totalColumns}>
        {footerConfig.map((config, index) => (
          <TableFooterRow
            left={config.left}
            right={config.right}
            needToAddDevider={
              footerConfig.length > 1 && index < footerConfig.length - 1
            }
          />
        ))}
      </td>
    </tr>
  );
};

export default TableFooter;
