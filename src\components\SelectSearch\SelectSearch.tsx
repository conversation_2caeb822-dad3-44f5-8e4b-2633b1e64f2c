// import {
//   ChangeEvent,
//   forwardRef,
//   KeyboardEvent,
//   useCallback,
//   useEffect,
//   useImperativeHandle,
//   useMemo,
//   useRef,
//   useState,
// } from 'react';
// import { createPortal } from 'react-dom';
// import { compute } from 'compute-scroll-into-view';
// import {
//   AlertTriangleIcon,
//   CheckIcon,
//   ChevronDownIcon,
// } from '@peddleon/ped-ux-react-icons';
// import AnimatedLoaderIcon from '@components/AnimatedLoader';
// import EyebrowDialog from '@components/EyebrowDialog';
// import {
//   SelectInputMenu,
//   SelectInputMenuItem,
// } from '@components/SelectInputMenu';
// import TextInput from '@components/TextInput';
// import {
//   ARROW_DOWN_EVENT,
//   ARROW_DOWN_KEYCODE_EVENT,
//   ARROW_UP_EVENT,
//   ARROW_UP_KEYCODE_EVENT,
//   ENTER_ESCAPE_EVENT,
//   ENTER_KEY_EVENT,
//   ENTER_KEYCODE_EVENT,
//   ESCAPE_EVENT,
//   SPACE_KEY_EVENT,
//   SPACE_KEYCODE_EVENT,
//   TAB_KEY_EVENT,
//   TAB_KEYCODE_EVENT,
// } from '@constants/components';
// import useDebouncedCallback from '@hooks/useDebouncedCallback';
// import useMediaQueryState from '@hooks/useMediaQueryState';
// import classNames from '@utils/classNames';
// import findScrollableParent from '@utils/findScrollableParent';
// import variableStyles from '@styles/variables.module.css';

// import styles from './SelectSearch.module.css';
// import { SelectSearchPropType } from './SelectSearch.types';

// const HIGHLIGHT_TIMER = 100;
// function escapeRegExp(str) {
//   return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
// }
// function highlightMatchingCharacters(str1, str2, displayLabel = undefined) {
//   // Escape special characters in the first string to create a valid regular expression
//   if (!str1?.trim()) return str2;
//   const escapedStr1 = escapeRegExp(str1);
//   // Create a regular expression to match all occurrences of the first string in the second string
//   let result = str2;

//   if (
//     displayLabel &&
//     displayLabel === str1 &&
//     str1.toLowerCase().includes(str2.toLowerCase())
//   ) {
//     result = `<span className={${styles.optionListMatchedCharacters}} style="font-weight: bold;">${str2}</span>`;
//   } else {
//     try {
//       result = str2.replace(
//         new RegExp(escapedStr1, 'gi'),
//         match =>
//           `<span className={${styles.optionListMatchedCharacters}} style="font-weight: bold;">${match}</span>`,
//       );
//     } catch (error) {
//       // eslint-disable-next-line no-console
//       console.error('[Ped-ux-react-library]: regex error', error);
//     }
//   }

//   // ** We'll be only getting these records from backend so doesn't require to have sanitize
//   // eslint-disable-next-line react/no-danger
//   return <div dangerouslySetInnerHTML={{ __html: result }} />;
// }

// /**
//  * The SelectSearch component provides a searchable dropdown menu for selecting items.
//  * It supports features such as custom items, keyboard navigation, and dynamic filtering.
//  *
//  * @param {SelectSearchPropType} props - The props for the SelectSearch component.
//  * @param {string | null} [props.id=null] - Optional. The ID for the component.
//  * @param {string} [props.label=''] - Optional. The label text for the component.
//  * @param {string} [props.error=''] - Optional. The error message to display.
//  * @param {string} [props.caption=''] - Optional. Additional caption text to display.
//  * @param {React.ReactNode} [props.left=null] - Optional. The component to render on the left side of the input.
//  * @param {React.ReactNode} [props.right=null] - Optional. The component to render on the right side of the input.
//  * @param {Array<SelectItemType>} props.items - Required. An array of items to be displayed in the dropdown menu.
//  * @param {boolean} [props.hasSucceeded=false] - Optional. Specifies whether the component has succeeded.
//  * @param {boolean} [props.isLoading=false] - Optional. Specifies whether the component is in a loading state.
//  * @param {boolean} [props.isDisabled=false] - Optional. Specifies whether the component is disabled.
//  * @param {boolean} [props.isBorderless=false] - Optional. Specifies whether the component is borderless.
//  * @param {Function} [props.onInvalid=() => {}] - Optional. Callback function invoked when the component is invalid.
//  * @param {boolean} [props.required=false] - Optional. Specifies whether the component is required.
//  * @param {number} [props.maxLength] - Optional. The maximum number of characters allowed in the input.
//  * @param {SelectItemType} props.value - Required. The currently selected value from the dropdown menu.
//  * @param {Function} props.onChange - Required. Callback function invoked when an item is selected from the dropdown menu.
//  * @param {boolean} [props.isMenuAlignedRight=false] - Optional. Specifies whether the dropdown menu is aligned to the right.
//  * @param {string} [props.placeholder] - Optional. Placeholder text displayed when no option is selected.
//  * @param {boolean} [props.isActive=false] - Optional. Specifies whether the dropdown menu is active/open.
//  * @param {Function} [props.onToggleButtonClick=() => {}] - Optional. Callback function invoked when the toggle button is clicked while the input is in focus.
//  * @param {Function} [props.onShouldClose=() => {}] - Optional. Callback function invoked when the menu should be closed.
//  * @param {string} [props.inputMode='text'] - Optional. The input mode for mobile devices.
//  * @param {string} [props.noMatchFoundText='No matches'] - Optional. Text displayed when no matching options are found in the dropdown.
//  * @param {boolean} [props.isPoweredByGoogle=false] - Optional. Specifies whether the dropdown menu is powered by Google.
//  * @param {Function} [props.onInputValueChange=() => {}] - Optional. Callback function invoked when the input value changes.
//  * @param {boolean} [props.isFixedMenu=false] - Optional. Flag to indicate if the menu should be fixed so it can float over elements above or below.
//  * @param {string} [props.mobileModalPosition='center'] - Optional. Position of the modal on mobile devices.
//  * @param {boolean} [props.isNewVariant=false] - Optional. Flag to indicate if the new variant of the component should be used.
//  * @returns {JSX.Element} - The rendered SelectSearch component.
//  */
// const SelectSearch = forwardRef(
//   (
//     {
//       id = null,
//       label = '',
//       error = '',
//       caption = '',
//       left = null,
//       right = null,
//       items = [],
//       hasSucceeded = false,
//       isLoading = false,
//       isDisabled = false,
//       isBorderless = false,
//       onInvalid = () => {},
//       required = false,
//       maxLength,
//       value,
//       onChange,
//       onFocus = () => {},
//       onBlur = () => {},
//       onInputValueChange = () => {},
//       isMenuAlignedRight = false,
//       placeholder,
//       isActive = false,
//       onToggleButtonClick = () => {},
//       onShouldClose = () => {},
//       inputMode = 'text',
//       noMatchFoundText = 'No matches',
//       isPoweredByGoogle = false,
//       mobileModalPosition = 'center',
//       isFixedMenu = false,
//       className = '',
//       hideErrorMessage = false,
//       title = '',
//       variant = 'normalFonts',
//     }: SelectSearchPropType,
//     ref,
//   ) => {
//     const selectContainerRef = useRef<HTMLDivElement>(null);
//     const menuListRef = useRef<HTMLDivElement>(null);
//     const selectWrapperRef = useRef<HTMLDivElement>(null);
//     const [highlightedIndex, setHighlightedIndex] = useState(-1);
//     const [isMenuOpen, setIsMenuOpen] = useState(false);
//     const [listItems, setListItems] = useState([...items]);
//     const [inputValue, setInputValue] = useState('');
//     const [isFocusedInput, setIsFocusedInput] = useState(false);

//     const menuList = useRef();
//     const dropdownRef = useRef(null);
//     const inputRef = useRef<HTMLInputElement>(null);

//     const isSmallWidth = useMediaQueryState({
//       query: `(max-width: ${variableStyles.smallWidth})`,
//     });

//     const isGroupBy = useMemo(() => items.some(item => item.options), [items]);

//     const handleMenuOpen = () => {
//       inputRef.current?.focus({
//         preventScroll: true,
//       });
//     };

//     useImperativeHandle(ref, () => ({
//       openModal: () => {
//         setIsMenuOpen(true);
//         if (!isSmallWidth) handleMenuOpen();
//       },
//       closeModal: () => setIsMenuOpen(false),
//     }));

//     const { findIndex, findSubIndex, selectedIndex } = useMemo(() => {
//       const indexes = {
//         findIndex: -1,
//         findSubIndex: -1,
//         selectedIndex: -1,
//       };
//       let temp = -1;
//       if (!items) return indexes;

//       for (let i = 0; i < items.length; i += 1) {
//         const item = items[i];
//         indexes.findIndex =
//           item?.value === value?.value ? i : indexes.findIndex;
//         if (item.options && isGroupBy) {
//           indexes.findIndex = i;
//           for (let j = 0; j < item.options.length; j += 1) {
//             temp += 1;
//             const subItem = item.options[j];
//             indexes.findSubIndex =
//               subItem.value === value?.value ? j : indexes.findSubIndex;
//             if (subItem.value === value?.value) {
//               indexes.selectedIndex = temp;
//               return indexes;
//             }
//           }
//           if (indexes.findSubIndex < 0) indexes.findIndex = -1;
//         }
//       }
//       return indexes;
//     }, [isGroupBy, items, value]);

//     const totalItemsLength = useMemo(
//       () =>
//         listItems.reduce((acc, item) => {
//           if (item.options) {
//             return acc + item.options.length;
//           }
//           return acc + 1;
//         }, 0),
//       [listItems],
//     );

//     const handleMenuClose = () => {
//       onShouldClose();
//       setIsMenuOpen(false);
//     };

//     const hasError = error.length > 0;
//     const listItemsLength = listItems.length;

//     let statusRightIcon = null;
//     if (hasSucceeded) {
//       statusRightIcon = (
//         <span className={styles.successIconWrapper}>
//           <CheckIcon width={24} height={24} />
//         </span>
//       );
//     } else if (hasError) {
//       statusRightIcon = (
//         <span className={styles.errorIconWrapper}>
//           <AlertTriangleIcon width={24} height={24} />
//         </span>
//       );
//     } else if (isLoading) {
//       statusRightIcon = (
//         <span className={styles.fetchingIconWrapper}>
//           <AnimatedLoaderIcon />
//         </span>
//       );
//     }
//     /** rootNode: parent node of element, node: the element which requires to get focused */
//     const scrollIntoView = ({ rootNode, node }) => {
//       try {
//         const actions = compute(node, {
//           boundary: rootNode,
//           block: 'center',
//           inline: 'center',
//           scrollMode: 'if-needed',
//         });

//         // ** In Mobile view it does not get scroll automatically with compute scroll into view
//         if (isSmallWidth) {
//           node?.scrollIntoViewIfNeeded(true);
//         } else {
//           /* eslint-disable */
//           actions.forEach(({ el, top, left }) => {
//             el.scrollTop = top;
//             el.scrollLeft = left;
//           });
//         }
//       } catch (error) {}
//     };

//     const getHighlightedItemByDataId = useCallback((dataId, parentNode) => {
//       return parentNode?.querySelector(`li[data-id="${dataId}"]`);
//     }, []);

//     // to handle frequent state change
//     const handleSettingHighlight = useDebouncedCallback(index => {
//       setHighlightedIndex(index);
//     }, 10);

//     const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>): void => {
//       const isSpaceKey =
//         event.keyCode === SPACE_KEYCODE_EVENT || event.key === SPACE_KEY_EVENT;
//       const isEnter =
//         event.keyCode === ENTER_KEYCODE_EVENT || event.key === ENTER_KEY_EVENT;
//       const isTab =
//         event.keyCode === TAB_KEYCODE_EVENT || event.key === TAB_KEY_EVENT;
//       const isKeyDown =
//         event.keyCode === ARROW_DOWN_KEYCODE_EVENT ||
//         event.key === ARROW_DOWN_EVENT;
//       const isKeyUp =
//         event.keyCode === ARROW_UP_KEYCODE_EVENT ||
//         event.key === ARROW_UP_EVENT;

//       const isEscape =
//         event.keyCode === ENTER_ESCAPE_EVENT || event.key === ESCAPE_EVENT;

//       if (isEscape) {
//         setIsMenuOpen(false);
//         inputRef.current.blur();
//         return;
//       }
//       if (isKeyDown) {
//         let updatedIndex = 0;
//         setHighlightedIndex(prevIndex => {
//           if (!isGroupBy) {
//             updatedIndex =
//               prevIndex < items.length - 1 ? prevIndex + 1 : prevIndex;

//             if (prevIndex === listItemsLength - 1) {
//               updatedIndex = 0;
//             }
//             highlightScroll(updatedIndex, true);
//           } else {
//             updatedIndex =
//               prevIndex < 0 || prevIndex === totalItemsLength - 1
//                 ? 0
//                 : prevIndex + 1;
//             highlightScroll(updatedIndex, false);
//           }

//           return updatedIndex;
//         });
//       } else if (isKeyUp) {
//         let updatedIndex = 0;
//         // Handle up arrow key
//         setHighlightedIndex(prevIndex => {
//           if (!isGroupBy) {
//             updatedIndex = prevIndex > 0 ? prevIndex - 1 : prevIndex;
//             if (prevIndex === 0) {
//               updatedIndex = listItemsLength - 1;
//             }
//             highlightScroll(updatedIndex, true);
//           } else {
//             if (prevIndex > 0) {
//               updatedIndex = prevIndex - 1;
//             } else if (prevIndex === 0) {
//               updatedIndex = totalItemsLength - 1;
//             } else {
//               return prevIndex;
//             }
//             highlightScroll(updatedIndex, false);
//           }

//           return updatedIndex;
//         });
//       } else if (isEnter || isTab || isSpaceKey) {
//         const target = event.target as HTMLInputElement;

//         if (isSpaceKey && target.value) return;

//         if (
//           highlightedIndex === -1 ||
//           (!listItems[highlightedIndex] && !isGroupBy)
//         )
//           return;

//         let tempItem;

//         if (isGroupBy) {
//           let index = 0;
//           for (let i = 0; i < listItems.length; i++) {
//             if (tempItem) break;
//             if (listItems[i].options) {
//               for (let j = 0; j < listItems[i].options.length; j++) {
//                 if (highlightedIndex === index) {
//                   tempItem = listItems[i].options[j];
//                   break;
//                 }
//                 index += 1;
//               }
//             }
//           }
//         } else {
//           tempItem = listItems[highlightedIndex];
//         }

//         onChange(tempItem);
//         inputRef.current.value = isGroupBy
//           ? tempItem?.displayLabel || tempItem?.label
//           : tempItem?.label;
//         setInputValue(inputRef.current.value);
//         handleMenuClose();
//         // Handle enter key
//         if (highlightedIndex !== -1) {
//           setHighlightedIndex(-1); // Reset highlightedIndex
//         }
//         inputRef.current.blur();
//         if (isSpaceKey) event.preventDefault();
//       }
//     };

//     const highlightScroll = (updatedHighlightedIndex, native = false) => {
//       if (updatedHighlightedIndex >= 0) {
//         let node = dropdownRef?.current?.children[updatedHighlightedIndex];
//         if (isGroupBy && !native && dropdownRef.current) {
//           node = getHighlightedItemByDataId(
//             updatedHighlightedIndex,
//             dropdownRef?.current,
//           );
//         }
//         if (!native && dropdownRef.current && node) {
//           scrollIntoView({
//             rootNode: menuList,
//             node,
//           });
//         } else if (dropdownRef?.current) {
//           node?.scrollIntoView({
//             block: 'nearest',
//           });
//         }
//       }
//     };

//     const handleInputFocus = () => {
//       onFocus();
//       setIsMenuOpen(true);
//       setIsFocusedInput(true);
//       onToggleButtonClick();
//     };

//     const handleInvalid = data => {
//       if (value?.value && required) return;
//       onInvalid(data);
//     };

//     const handleInputBlur = () => {
//       onBlur();
//       const { value: inputValue } = inputRef.current;
//       if (!inputValue && value?.value && required) {
//         inputRef.current.value = value?.value;
//         setInputValue(inputRef.current.value);
//       }
//       // for mobile devices, blaring input immediately call, to avoid that
//       if (!isSmallWidth) handleMenuClose();
//     };

//     const handleMouseOver = index => {
//       handleSettingHighlight(index);
//     };

//     const handleInputChange = (
//       event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
//     ): void => {
//       const { value } = event.target;
//       onInputValueChange(value);
//       let updatedValues;
//       if (!isGroupBy) {
//         updatedValues = items?.filter(item =>
//           item?.label?.toLowerCase().includes(value?.trim()?.toLowerCase()),
//         );
//       } else {
//         updatedValues = items
//           ?.map(item => {
//             if (item.options) {
//               return {
//                 ...item,
//                 options: item.options.filter(subItem =>
//                   subItem?.label
//                     ?.toLowerCase()
//                     .includes(value?.trim()?.toLowerCase()),
//                 ),
//               };
//             }
//             return item;
//           })
//           .filter(item => item.options.length);
//         if (updatedValues.length < 1) {
//           updatedValues = items?.filter(item =>
//             item?.label?.toLowerCase().includes(value?.trim()?.toLowerCase()),
//           );
//         }
//       }
//       setListItems(updatedValues);
//       handleSettingHighlight(updatedValues.length && value.trim() ? 0 : -1);
//       if (value.trim()) highlightScroll(0, true);
//       setInputValue(value);
//     };

//     const isFocusedInputMenu =
//       isFocusedInput || value?.value || inputRef?.current?.value;

//     const handleChange = (highlightIndex, index, subIndex = undefined) => {
//       setHighlightedIndex(highlightIndex);
//       const item =
//         subIndex >= 0 ? listItems[index]?.options[subIndex] : listItems[index];
//       inputRef.current.value = isGroupBy
//         ? item?.displayLabel || item?.label
//         : item?.label;
//       setInputValue(item?.label);
//       onChange(item);
//       if (isMenuOpen) handleMenuClose();
//     };

//     useEffect(() => {
//       // To make menu open/close for mobile
//       if (isSmallWidth && !inputRef.current) {
//         if (isActive) {
//           setIsMenuOpen(true);
//         } else {
//           setIsMenuOpen(false);
//         }
//       }
//       if (!inputRef.current) return;
//       // To make menu open/close for desktop
//       if (isActive) {
//         inputRef.current?.focus({
//           preventScroll: true,
//         });
//       } else {
//         inputRef.current?.blur();
//       }
//     }, [isActive]);

//     useEffect(() => {
//       if (isMenuOpen) return;
//       if (value?.value && !isGroupBy && findIndex >= 0) {
//         const updatedInputValue = items[findIndex].label;
//         if (inputRef.current) inputRef.current.value = updatedInputValue;
//         setInputValue(updatedInputValue);
//       } else if (isGroupBy && findSubIndex >= 0) {
//         let item = items[findIndex]?.options[findSubIndex];
//         const updatedInputValue = item.displayLabel || item.label;
//         if (inputRef.current) inputRef.current.value = updatedInputValue;
//         setInputValue(updatedInputValue);
//       } else {
//         if (inputRef.current) inputRef.current.value = '';
//         setInputValue('');
//       }

//       setListItems(items);
//     }, [items, value, isMenuOpen]);

//     useEffect(() => {
//       let index = findIndex;

//       if (findSubIndex >= 0) {
//         index = selectedIndex;
//       }

//       setHighlightedIndex(index);

//       // ** direct scrolling will not work due to rendering of the menu list item to avoid that there is timeout
//       setTimeout(() => {
//         highlightScroll(index >= 0 ? index : 0);
//       }, HIGHLIGHT_TIMER);

//       // ** Mobile devices we've different Dialog modal, to make focus input within it
//       if (isMenuOpen && isSmallWidth) {
//         inputRef.current?.focus({
//           preventScroll: true,
//         });

//         setTimeout(() => {
//           inputRef.current?.focus({
//             preventScroll: true,
//           });
//         }, HIGHLIGHT_TIMER);
//       }
//     }, [isMenuOpen, findIndex, findSubIndex]);

//     useEffect(() => {
//       const handleDropdown = () => {
//         if (!selectWrapperRef.current || !menuListRef.current) return;

//         const {
//           top: wrapperTop,
//           left: wrapperLeft,
//           bottom: wrapperBottom,
//           width: selectWidth,
//         } = selectWrapperRef.current.getBoundingClientRect();
//         const { height: menuListHeight } =
//           menuListRef.current.getBoundingClientRect();

//         requestAnimationFrame(() => {
//           if (selectWidth > 0) {
//             menuListRef.current.style.maxWidth = `${selectWidth}px`;
//           }

//           menuListRef.current.style.left = `${wrapperLeft}px`;
//           let top = 0;
//           if (window.innerHeight < wrapperBottom + menuListHeight + 16) {
//             top = wrapperTop - menuListHeight - 8;
//           } else {
//             if (!selectContainerRef?.current?.closest('.dialogModalWrapper')) {
//               top += 8;
//             }
//             top += wrapperBottom;
//           }
//           menuListRef.current.style.top = `${top}px`;
//         });
//       };

//       if (isFixedMenu && !isSmallWidth) {
//         let scrollableAncestor;
//         if (selectWrapperRef.current) {
//           scrollableAncestor = findScrollableParent(selectWrapperRef.current);
//         }
//         if (isMenuOpen) {
//           handleDropdown();
//           if (scrollableAncestor) {
//             scrollableAncestor.addEventListener('scroll', handleDropdown);
//           }
//           window.addEventListener('scroll', handleDropdown);
//           window.addEventListener('resize', handleDropdown);
//         } else {
//           if (scrollableAncestor) {
//             scrollableAncestor.addEventListener('scroll', handleDropdown);
//           }
//           window.removeEventListener('scroll', handleDropdown);
//           window.removeEventListener('resize', handleDropdown);
//         }
//       }

//       return () => {
//         if (selectWrapperRef.current) {
//           const scrollableAncestor = findScrollableParent(
//             selectWrapperRef.current,
//           );
//           scrollableAncestor.removeEventListener('scroll', handleDropdown);
//         }
//         window.removeEventListener('scroll', handleDropdown);
//         window.removeEventListener('resize', handleDropdown);
//       };
//     }, [isFixedMenu, isMenuOpen, inputValue]);

//     const selectInput = (
//       <div
//         className={classNames(styles.inputWrapper, styles[`${variant}Wrapper`])}
//       >
//         <TextInput
//           isNewVariant={variant === 'helveticaFonts'}
//           isDisabled={isDisabled}
//           title={title}
//           left={left}
//           inputMode={inputMode}
//           error={error}
//           caption={caption}
//           label={label}
//           onKeyDown={handleKeyDown}
//           placeholder={placeholder}
//           onChange={handleInputChange}
//           value={inputValue}
//           hideRightIcon
//           ref={inputRef}
//           maxLength={maxLength}
//           hideErrorMessage={hideErrorMessage}
//           shouldHideErrorMessage
//           isBorderless={isSmallWidth}
//           required={required}
//           autoComplete={isPoweredByGoogle ? id : 'off'}
//           name={id}
//           onFocus={handleInputFocus}
//           onInvalid={handleInvalid}
//           onBlur={handleInputBlur}
//           right={
//             <>
//               {!isSmallWidth && right}
//               {!isSmallWidth && statusRightIcon}
//               {!isSmallWidth && (
//                 <div className={styles.inputRightNavigation}>
//                   {isMenuOpen && <div className={styles.overlayCloseBtn}></div>}

//                   <button
//                     tabIndex={-1}
//                     type="button"
//                     onClick={handleMenuOpen}
//                     className={styles.chevronIconWrapper}
//                   >
//                     <ChevronDownIcon width={24} height={24} />
//                   </button>
//                 </div>
//               )}
//             </>
//           }
//         />
//       </div>
//     );

//     let listIndex = -1;
//     const inputMenu = (
//       <SelectInputMenu
//         isOpen={isSmallWidth ? true : isMenuOpen}
//         getMenuProps={details => {
//           return null;
//         }}
//         listClassName={styles.menu}
//         className={classNames(
//           styles.menuSelectInput,
//           isSmallWidth && styles.inputMenuModal,
//           isFixedMenu && styles.fixedListWrapper,
//         )}
//         isAlignedRight={isMenuAlignedRight}
//         hasListRef
//         ref={menuListRef}
//       >
//         <div ref={dropdownRef}>
//           {listItems.length === 0 && (
//             <SelectInputMenuItem>
//               <div
//                 onMouseDown={event => {
//                   event.preventDefault();
//                 }}
//                 className={styles.notMatchFound}
//               >
//                 {noMatchFoundText}
//               </div>
//             </SelectInputMenuItem>
//           )}
//           {listItems.map((item, index) => {
//             if (isGroupBy && item.options && item.options?.length === 0) {
//               return null;
//             }
//             if (item.options?.length > 0) {
//               // Render a group
//               return (
//                 <li key={index} className={styles.group}>
//                   <div
//                     className={classNames(
//                       styles.groupHeader,
//                       item.options.some(
//                         i =>
//                           (i?.displayLabel || i?.label) === inputValue?.trim(),
//                       ) && styles.groupHeaderMatched,
//                     )}
//                   >
//                     {item.label}
//                   </div>
//                   <ul className={styles.groupList}>
//                     {item.options.map((subItem, subIndex) => {
//                       listIndex += 1;
//                       return (
//                         <SelectInputMenuItem
//                           key={`${index}-${subIndex}-${listIndex}`}
//                           isHighlighted={highlightedIndex === listIndex}
//                           isGroupBy={isGroupBy}
//                           // @ts-ignore
//                           onMouseOver={e => {
//                             const id = e.currentTarget.dataset.id;
//                             if (id) handleMouseOver(Number(id));
//                           }}
//                           onMouseDown={e => {
//                             const id = e.currentTarget.dataset.id;
//                             if (id) handleChange(Number(id), index, subIndex);
//                           }}
//                           data-id={listIndex}
//                           left={subItem.left}
//                         >
//                           {highlightMatchingCharacters(
//                             inputValue?.trim(),
//                             subItem.label,
//                             subItem.displayLabel,
//                           )}
//                         </SelectInputMenuItem>
//                       );
//                     })}
//                   </ul>
//                 </li>
//               );
//             } else {
//               listIndex += 1;
//               // Render a regular item
//               return (
//                 <SelectInputMenuItem
//                   key={index}
//                   isHighlighted={highlightedIndex === listIndex}
//                   // @ts-ignore
//                   onMouseOver={e => {
//                     const id = e.currentTarget.dataset.id;
//                     if (id) handleMouseOver(Number(id));
//                   }}
//                   onMouseDown={e => {
//                     const id = e.currentTarget.dataset.id;
//                     if (id) handleChange(Number(id), index);
//                   }}
//                   data-id={listIndex}
//                   left={item.left}
//                 >
//                   {highlightMatchingCharacters(inputValue?.trim(), item.label)}
//                 </SelectInputMenuItem>
//               );
//             }
//           })}
//         </div>
//       </SelectInputMenu>
//     );

//     return (
//       <div
//         className={classNames(
//           styles.comboboxWrapper,
//           value?.value && styles.filled,
//           isDisabled && styles.disabled,
//           hasError && styles.errored,
//           className,
//           styles[variant],
//         )}
//         ref={selectContainerRef}
//       >
//         <div className={styles.container} ref={selectWrapperRef}>
//           {isSmallWidth ? (
//             <button
//               type="button"
//               onClick={() => {
//                 setIsMenuOpen(true);
//               }}
//               className={styles.button}
//               data-id={`${id}-dropdown-button`}
//               tabIndex={isDisabled ? -1 : null}
//               title={isGroupBy ? value?.displayLabel : value?.label}
//             >
//               <div className={styles.buttonContainer}>
//                 {left && <span className={styles.leftWrapper}>{left}</span>}

//                 <div className={styles.centerWrapper}>
//                   {label.length > 0 && (
//                     // disabling because dropshift should be providing the necessary a11y props
//                     // eslint-disable-next-line jsx-a11y/label-has-associated-control
//                     <label className={styles.label}>{label}</label>
//                   )}

//                   <span className={styles.selected}>
//                     {isGroupBy ? value?.displayLabel : value?.label}
//                   </span>
//                 </div>

//                 <span className={styles.rightWrapper}>
//                   {right}
//                   {statusRightIcon}
//                   <span className={styles.chevronIconWrapper}>
//                     <ChevronDownIcon height={24} width={24} />
//                   </span>
//                 </span>
//               </div>
//             </button>
//           ) : (
//             <div
//               className={classNames(
//                 styles.combobox,
//                 isDisabled && styles.disabled,
//                 isBorderless && styles.borderless,
//                 isMenuOpen && styles.active,
//                 value?.label && styles.filled,
//                 hasError && styles.errored,
//                 ((value?.label && isMenuOpen) || isMenuOpen) &&
//                   isFocusedInputMenu &&
//                   styles.focused,
//               )}
//             >
//               {selectInput}
//             </div>
//           )}
//         </div>

//         {isSmallWidth && !isPoweredByGoogle ? (
//           <>
//             <EyebrowDialog
//               modalConfig={{
//                 isActive: isMenuOpen,
//                 isMobileCentered: true,
//                 hasDialogWrapperStyles: true,
//                 hasNoPaddingAroundForBody: true,
//               }}
//               modalActions={{
//                 onClose: handleMenuClose,
//               }}
//               modalContent={{
//                 bodyContent: inputMenu,
//                 headerwrapperContent: (
//                   <div
//                     className={classNames(
//                       isMenuOpen && styles.active,
//                       value?.label && styles.filled,
//                       (value?.label || isFocusedInput) && styles.focused,
//                     )}
//                   >
//                     {selectInput}
//                   </div>
//                 ),
//                 headingContent: 'Label',
//               }}
//               modalClassNames={{
//                 dialogWrapperClassName: classNames(
//                   styles.dialogModalWrapper,
//                   mobileModalPosition && styles[mobileModalPosition],
//                 ),
//                 headerWrapperClassName: styles.modalHeader,
//                 headerWrapperContainerClassName: styles.modalHeaderContainer,
//                 bodyWrapperClassName: styles.modalForCombo,
//                 bodyWrapperContainerScrollClassName: styles.modalContainer,
//               }}
//             />
//           </>
//         ) : (
//           <div className={styles.active}>
//             <div
//               className={styles.menuWrapper}
//               onMouseDown={event => {
//                 if (!listItems.length) {
//                   event.preventDefault();
//                   event.stopPropagation();
//                 }
//               }}
//               ref={menuList}
//             >
//               {isFixedMenu && !isSmallWidth
//                 ? createPortal(
//                     inputMenu,
//                     selectContainerRef?.current?.closest(
//                       '.dialogModalWrapper',
//                     ) ?? document.body,
//                   )
//                 : inputMenu}
//             </div>
//           </div>
//         )}
//       </div>
//     );
//   },
// );

// export default SelectSearch;
