import React, { createRef } from 'react';
import { render } from '@testing-library/react';

import Scrims from './Scrims';

describe('Scrims Component', () => {
  test('renders Scrims component without props', () => {
    const { container } = render(<Scrims />);
    // Verify that default elements are rendered
    expect(container.querySelector('.topGradient')).toBeInTheDocument();
    expect(container.querySelector('.bottomGradient')).toBeInTheDocument();
    expect(container.querySelector('.leftGradient')).toBeInTheDocument();
    expect(container.querySelector('.rightGradient')).toBeInTheDocument();
  });

  test('renders Scrims component with overflowRef and default props', () => {
    const ref = createRef<HTMLElement>();
    const { container } = render(<Scrims overflowRef={ref} />);
    // Verify that gradient overlays are not visible initially
    expect(container.querySelector('.topGradient.visible')).toBeNull();
    expect(container.querySelector('.bottomGradient.visible')).toBeNull();
    expect(container.querySelector('.leftGradient.visible')).toBeNull();
    expect(container.querySelector('.rightGradient.visible')).toBeNull();
  });

  test('renders Scrims component with inverted gradient overlays', () => {
    const ref = createRef<HTMLElement>();
    const { container } = render(<Scrims overflowRef={ref} isInverted />);
    // Verify that inverted gradient overlays are applied
    expect(
      container.querySelector('.topGradient.isInverted'),
    ).toBeInTheDocument();
    expect(
      container.querySelector('.bottomGradient.isInverted'),
    ).toBeInTheDocument();
    expect(
      container.querySelector('.leftGradient.isInverted'),
    ).toBeInTheDocument();
    expect(
      container.querySelector('.rightGradient.isInverted'),
    ).toBeInTheDocument();
  });
});
