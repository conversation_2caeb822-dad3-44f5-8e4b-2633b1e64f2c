@value typography: "../../../styles/typography.module.css";
@value caption, bodySmallStrong, bodySmall from typography;

@value variables: "../../../styles/variables.module.css";
@value neutralDarkest, black, neutralLighter, oneSpace, twoSpace from variables;
@value warning from variables;

.card {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: twoSpace;
  gap: oneSpace;
  box-shadow: rgba(100, 100, 111, 0.2) 0px 1px 7px 0px;
  margin-top: oneSpace;
  cursor: pointer;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: twoSpace twoSpace 0;
  padding: 0px 0px oneSpace 0px;
  gap: 0px;
  border-bottom: 1px solid rgba(33, 33, 33, 0.05);
  word-break: break-word;

  & .moreIcon {
    cursor: pointer;
  }
}

.cardContent {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-family: Aventa;
  text-align: left;
  gap: oneSpace;
  width: 70%;

  & .cardBodyTitle {
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
    word-break: break-word;
  }

  & .cardBodySubTitle {
    font-size: 12px;
    font-weight: 500;
    line-height: 20px;
    letter-spacing: 0.01em;
    color: neutralDarkest;
    word-break: break-word;
  }
}

.cardBodyFullContent {
  width: 100% !important;
}

.cardFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(33, 33, 33, 0.05);
  padding: oneSpace 0px 0px 0px;
  gap: 0px;
  font-family: Aventa;
  text-align: left;
  font-size: 12px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0.01em;

  & .cardFooterLeftContent {
    color: neutralDarkest;
    word-break: break-word;
  }

  & .cardFooterRightContent {
    color: black;
    word-break: break-word;
  }
}

.badgeClassName {
  font-family: Aventa;
  background-color: warning;
  padding: 2px twoSpace;
  border-radius: 20px;
  font-size: 12px;
  min-height: 25px;
  line-height: 13px;
}

.cardBody {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: oneSpace;
  width: 100%;
}

.checkbox {
  height: 16px;
}
