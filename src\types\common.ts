import React, {
  ChangeEvent,
  KeyboardEvent,
  MouseEvent,
  ReactElement,
  ReactNode,
} from 'react';
import { AlertProps } from '@components/Alert/Alert.types';

export interface SelectItemType {
  label: string;
  displayLabel?: string;
  value?: string;
  secondaryLabel?: string;
  right?: ReactNode; // MultiSelectInput expects this property
  left?: ReactNode; // SelectInput expects this property
  options?: Array<SelectItemType>;
  isFirst?: boolean;
}

export interface InputPropType {
  id?: string;
  label?: string;
  name?: string;
  error?: string;
  caption?: ReactNode;
  value?: string | number;
  left?: ReactNode;
  right?: ReactNode;
  hasSucceeded?: boolean;
  isLoading?: boolean;
  isDisabled?: boolean;
  isBorderless?: boolean;
  onChange: (event: ChangeEvent<HTMLInputElement>) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onKeyDown?: (event: KeyboardEvent<HTMLInputElement>) => void;
  onClick?: (event: MouseEvent<HTMLInputElement>) => void;
  onPaste?: (event: React.ClipboardEvent<HTMLInputElement>) => void;
  onCut?: (event: React.ClipboardEvent<HTMLInputElement>) => void;
  onCopy?: (event: React.ClipboardEvent<HTMLInputElement>) => void;
  onInvalid?: ({
    id,
    label,
    value,
    validity,
    name,
  }: {
    id?: string;
    label?: string;
    value?: string | number | SelectItemType;
    validity?: ValidityState;
    name?: string;
  }) => void;
  required?: boolean;
}

export interface TextareaPropType {
  id?: string;
  label?: string;
  name?: string;
  error?: string;
  caption?: ReactNode;
  value?: string | number;
  left?: ReactNode;
  right?: ReactNode;
  hasSucceeded?: boolean;
  isLoading?: boolean;
  isDisabled?: boolean;
  isBorderless?: boolean;
  onChange: (event: ChangeEvent<HTMLTextAreaElement>) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  onKeyDown?: (event: KeyboardEvent<HTMLTextAreaElement>) => void;
  onClick?: (event: MouseEvent<HTMLTextAreaElement>) => void;
  onPaste?: (event: React.ClipboardEvent<HTMLTextAreaElement>) => void;
  onCut?: (event: React.ClipboardEvent<HTMLTextAreaElement>) => void;
  onInvalid?: ({
    id,
    label,
    value,
    validity,
  }: {
    id?: string;
    label?: string;
    value?: string | number | SelectItemType;
    validity?: ValidityState;
  }) => void;
  required?: boolean;
}

export type ModalPositionType =
  | 'bottomRight'
  | 'bottomLeft'
  | 'bottomCenter'
  | 'topRight'
  | 'topLeft'
  | 'topCenter';

export interface ToastConfigType {
  theme?: AlertProps['theme'];
  body?: AlertProps['body'];
  isActive?: boolean;
  key?: string;
}

export interface ToastContextProviderType {
  children: ReactNode;
  position?: ModalPositionType;
  isCancellable?: boolean;
  timer?: number;
}

export interface ToastContextType {
  setToastConfig: (arg) => void;
  toastConfig: ToastConfigType;
  timer?: number;
  isCancellable?: boolean;
  position?: ModalPositionType;
}

interface ImageCarouselItem {
  id: string | number;
  image: ReactNode;
  label: string;
}

export interface ImageCarouselInnerProps {
  items: Array<ImageCarouselItem>;
  nextButton?: ReactElement;
  previousButton?: ReactElement;
  alignCenter?: boolean;
  onActiveIndexChange?: (index: number) => void;
}

export type SimpleTheme = 'dark' | 'light';
