import React from 'react';
import { fireEvent, render } from '@testing-library/react';

import CodeBlock from './CodeBlock';

import '@testing-library/jest-dom'; // For DOM assertions

describe('CodeBlock Component', () => {
  // Test Case 1: Render Test
  test('renders without crashing', () => {
    render(<CodeBlock code="Test code" />);
  });

  // Test Case 2: Icon Test
  test('renders icon if provided', () => {
    const { getByTestId } = render(
      <CodeBlock code="Test code" icon={<div data-testid="icon" />} />,
    );
    expect(getByTestId('icon')).toBeInTheDocument();
  });

  // Test Case 3: Click Event Test
  test('triggers onClick event when icon is clicked', () => {
    const onClickMock = jest.fn();
    const { getByTestId } = render(
      <CodeBlock
        code="Test code"
        icon={<div data-testid="icon" />}
        onClick={onClickMock}
      />,
    );
    fireEvent.click(getByTestId('icon'));
    expect(onClickMock).toHaveBeenCalledTimes(1);
  });

  // Test Case 4: No Icon Test
  test('renders correctly when no icon is provided', () => {
    const { queryByTestId } = render(<CodeBlock code="Test code" />);
    expect(queryByTestId('icon')).not.toBeInTheDocument();
  });

  // Test Case 5: Code Content Test
  test('displays code snippet correctly', () => {
    const code = 'const example = "Hello World";';
    const { getByText } = render(<CodeBlock code={code} />);
    expect(getByText(code)).toBeInTheDocument();
  });
});
