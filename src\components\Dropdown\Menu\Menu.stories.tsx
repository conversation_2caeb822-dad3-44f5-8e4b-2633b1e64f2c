import { Meta } from '@storybook/react';
import { Menu as SelectInputMenu, MenuItem } from '@components/Dropdown';

/**
 * <br/>
 * ## Overview
 * Menu component is used to render a menu which can be used within the dropdown.
 * ## Usage
 * Import the component into your React application:
 *
 * ```jsx
 * import { Menu } from '@peddleon/ped-ux-react-library';
 * ```
 */
export default {
  title: 'Components/Menu',
  component: SelectInputMenu,
  argTypes: {
    isOpen: { control: 'boolean' },
    isAlignedRight: { control: 'boolean' },
    className: { control: 'text' },
    listClassName: { control: 'text' },
    hasListRef: { control: 'boolean' },
    // You may add more controls for other props as needed
  },
  tags: ['autodocs'],
} as Meta;

const Template = args => <SelectInputMenu {...args} />;

/**
 * Default Menu Component
 */
export const Default = Template.bind({});
Default.args = {
  isOpen: true,
  getMenuProps: () => ({ ref: null }), // Replace with your getMenuProps implementation
  children: (
    <>
      <MenuItem>Menu Item 1</MenuItem>
      <MenuItem>Menu Item 2</MenuItem>
    </>
  ),
};

/**
 * Menu Component with Google Footer
 */
export const WithFooter = Template.bind({});
WithFooter.args = {
  isOpen: true,
  getMenuProps: () => ({ ref: null }), // Replace with your getMenuProps implementation
  children: (
    <>
      <MenuItem>Menu Item 1</MenuItem>
      <MenuItem>Menu Item 2</MenuItem>
    </>
  ),
  footer: <div>Footer </div>,
};
