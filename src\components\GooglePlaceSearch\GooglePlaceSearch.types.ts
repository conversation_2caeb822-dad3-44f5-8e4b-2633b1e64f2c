import type { UseComboboxProps } from 'downshift';
import { InputPropType, SelectItemType } from '@libtypes/common';

export interface GooglePlaceSearchPropTypes
  extends Omit<InputPropType, 'onChange'> {
  /**
   * caption?: string
   * An optional caption to display below the input field.
   */
  caption?: string;

  /**
   * items: Array<SelectItemType>
   * An array of items to be displayed as a menu list for autocomplete suggestions.
   */
  items: Array<SelectItemType>;

  /**
   * inputValue: string
   * The current input value of the search field.
   */
  inputValue: string;

  /**
   * value: SelectItemType['value']
   * The selected value from the menu list.
   */
  value: SelectItemType['value'];

  /**
   * onInputValueChange: UseComboboxProps<SelectItemType>['onInputValueChange']
   * A function to handle changes to the input value as the user types.
   */
  onInputValueChange: UseComboboxProps<SelectItemType>['onInputValueChange'];

  /**
   * onChange: (value: SelectItemType) => void
   * A function to handle the event when a value is selected from the menu list.
   */
  onChange: (value: SelectItemType) => void;

  /**
   * maxLength?: number
   * An optional maximum length for the input value.
   */
  maxLength?: number;

  /**
   * googlePlacesAPIKey?: string
   * An optional API key for accessing Google Places services.
   */
  googlePlacesAPIKey?: string;
  /**
   * name?: string
   * An optional name attribute for the input field.
   */
  name?: string;
}
