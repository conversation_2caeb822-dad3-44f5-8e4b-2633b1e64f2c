import { ReactNode } from 'react';

/**
 * Specifies the shape of an option item in the RadioGroup.
 */
export interface OptionItem {
  /**
   * The unique identifier for the option.
   */
  id: string;
  /**
   * The label displayed for the option.
   */
  label: string;
  /**
   * Optionally, a right element to be displayed next to the radio input.
   */
  right?: ReactNode;
  /**
   * Specifies whether the option is disabled.
   */
  isDisabled?: boolean;
  /**
   * Specifies the value associated with the option.
   */
  value?: string;
}

/**
 * The RadioGroupPropTypes interface defines the expected props for the RadioGroup component.
 */
export interface RadioGroupPropTypes {
  /**
   * The name attribute for the radio inputs in the group.
   */
  name?: string;
  /**
   * An array of options to be rendered as radio inputs.
   */
  options: OptionItem[];
  /**
   * The direction in which the radio inputs are laid out (column or row).
   */
  direction?: 'column' | 'row';
  /**
   * The horizontal alignment of the radio inputs (left or right).
   */
  alignment?: 'left' | 'right';
  /**
   * The identifier of the checked option.
   */
  checked: string;
  /**
   * Event handler for the onChange event of the radio inputs.
   */
  onChange: ({ checked }: { checked: string }) => void;
  /**
   * Specifies whether the entire group is disabled.
   */
  isDisabled?: boolean;
  /**
   * Specifies whether the layout should change on mobile devices to display options inline.
   */
  isMobileInline?: boolean;
  /**
   * Additional class name(s) for custom styling.
   */
  className?: string;
  /**
   * Specifies whether the radio inputs should have a button-like appearance.
   */
  isInButtonShape?: boolean;
}
