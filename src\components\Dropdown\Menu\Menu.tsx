import type { FC, ReactNode } from 'react';
import { forwardRef, useRef } from 'react';
import Scrims from '@components/Scrims';
import classNames from '@utils/classNames';

import styles from './Menu.module.css';

/**
 * Props for the SelectInputMenu component.
 */
interface SelectInputMenuProps {
  /** Determines whether the menu is open or closed. */
  isOpen: boolean;
  /** The content of the menu. */
  children: ReactNode;
  /** Additional CSS class for the component. */
  className?: string;
  /** Additional CSS class for the list element. */
  listClassName?: string;
  /** Footer content of the menu. */
  footer?: ReactNode;
  /** Callback function triggered when the menu loses focus. */
  onBlur?: () => void;
  /** Indicates whether the list reference is needed externally. */
  hasListRef?: boolean;
}

/**
 * Component for a select input menu.
 */
const SelectInputMenu: FC<SelectInputMenuProps> = forwardRef<
  HTMLDivElement,
  SelectInputMenuProps
>(
  (
    {
      isOpen,
      children,
      className = null,
      listClassName = null,
      footer = null,
      onBlur = () => {},
      hasListRef = false,
    }: SelectInputMenuProps,
    ref,
  ) => {
    const listRef = useRef<HTMLUListElement>();
    let extraListProps = {};

    if (hasListRef) {
      extraListProps = { ref: listRef };
    }

    return (
      <div
        ref={ref}
        className={classNames(styles.menu, isOpen && styles.visible, className)}
      >
        <div className={styles.container}>
          <ul
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            // {...(getMenuProps as any)({ ref: listRef, onBlur })}
            className={classNames(styles.list, listClassName)}
            onBlur={onBlur}
            {...extraListProps}
          >
            {isOpen && children}
          </ul>
        </div>
        {isOpen && <Scrims overflowRef={listRef} />}
        {footer}
      </div>
    );
  },
);

export default SelectInputMenu;
