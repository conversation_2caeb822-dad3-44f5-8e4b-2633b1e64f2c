import type { <PERSON>a, StoryObj } from '@storybook/react';
import BusinessCard from './BusinessCard';
import { INITIAL_FORM_DATA } from './constants';

const meta: Meta<typeof BusinessCard> = {
  title: 'Components/BusinessCard',
  component: BusinessCard,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `

A professional business card generator component that allows users to create and download business cards with QR codes. The component includes form validation, QR code generation, and PDF export functionality.

## Features
- Form validation for all required fields
- QR code generation for vCard integration
- PDF export with custom styling
- Responsive design
- Custom font support (Aventa)
- Single-page PDF output with front and back sides

## Usage

### Basic Usage
\`\`\`tsx
import { BusinessCard } from '@ped-ux/react-library';

const MyComponent = () => {
  return (
    <BusinessCard
      onGenerate={(formData) => console.log('Card generated:', formData)}
      onError={(error) => console.error('Error:', error)}
    />
  );
};
\`\`\`

### With Prefilled Data
\`\`\`tsx
import { BusinessCard } from '@ped-ux/react-library';

const MyComponent = () => {
  return (
    <BusinessCard
      initialData={{
        firstName: 'Lauren',
        lastName: 'McLeod',
        title: 'Account Manager',
        organization: 'Buyer Network',
        email: '<EMAIL>',
        phoneNumber: '************',
      }}
      onGenerate={(formData) => console.log('Card generated:', formData)}
      onError={(error) => console.error('Error:', error)}
    />
  );
};
\`\`\`

### With Custom Callbacks
\`\`\`tsx
import { BusinessCard } from '@ped-ux/react-library';

const MyComponent = () => {
  const handleGenerate = (formData) => {
    // Handle successful generation
    console.log('Business card generated:', formData);
    // You can also save the data to your backend here
  };

  const handleError = (error) => {
    // Handle errors
    console.error('Error generating business card:', error);
    // You can show a toast notification or alert here
  };

  return (
    <BusinessCard
      onGenerate={handleGenerate}
      onError={handleError}
    />
  );
};
\`\`\`

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| initialData | Partial<BusinessCardFormData> | No | Initial form data to prefill the business card |
| onGenerate | (formData: BusinessCardFormData) => void | No | Callback function called when the business card is successfully generated |
| onError | (error: Error) => void | No | Callback function called when there is an error generating the business card |

## Form Data Structure

\`\`\`typescript
interface BusinessCardFormData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  organization: string;
  title: string;
}
\`\`\`

## Notes
- The component requires the Aventa font family to be loaded
- The generated PDF will be a single page with front and back sides
- The QR code contains vCard information that can be scanned by mobile devices
`,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    initialData: {
      description: 'Initial form data to prefill the business card',
      control: 'object',
    },
    onGenerate: {
      action: 'generated',
      description:
        'Callback function called when the business card is generated',
    },
    onError: {
      action: 'error',
      description:
        'Callback function called when there is an error generating the business card',
    },
  },
};

export default meta;
type Story = StoryObj<typeof BusinessCard>;

export const Default: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          'Default business card form with all fields empty. Shows the basic usage of the component.',
      },
    },
  },
};

export const WithPrefilledData: Story = {
  args: {
    initialData: {
      ...INITIAL_FORM_DATA,
      firstName: 'Lauren',
      lastName: 'McLeod',
      title: 'Account Manager',
      organization: 'Buyer Network',
      email: '<EMAIL>',
      phoneNumber: '************',
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          'Business card form with prefilled data. Demonstrates how to initialize the form with existing data.',
      },
    },
  },
};

export const WithCallbacks: Story = {
  args: {
    onGenerate: formData => {
      console.log('Business card generated:', formData);
      alert('Business card generated successfully!');
    },
    onError: error => {
      console.error('Error generating business card:', error);
      alert('Error generating business card: ' + error.message);
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          'Business card form with custom callbacks. Shows how to handle successful generation and errors.',
      },
    },
  },
};

export const WithValidation: Story = {
  args: {
    initialData: {
      firstName: 'Lauren',
      lastName: 'McLeod',
      title: 'Account Manager',
      organization: 'Buyer Network',
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          'Business card form demonstrating validation. Shows how the component handles missing or invalid data.',
      },
    },
  },
};
