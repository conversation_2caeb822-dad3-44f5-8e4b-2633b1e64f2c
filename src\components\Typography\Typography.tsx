import { FC } from 'react';
import classNames from '@utils/classNames';
import typographyStyles from '@styles/typography.module.css';

import { TypographyProps, TypographyStyles } from './Typography.types';

// Define the mapping of variants to CSS classes
const variantStyles: TypographyStyles = {
  h1: typographyStyles.h1,
  h2: typographyStyles.h2,
  h3: typographyStyles.h3,
  h4: typographyStyles.h4,
  h5: typographyStyles.h5,
  h6: typographyStyles.h6,
  h1Var: typographyStyles.h1Var,
  h2Var: typographyStyles.h2Var,
  h3Var: typographyStyles.h3Var,
  h4Var: typographyStyles.h4Var,
  h5Var: typographyStyles.h5Var,
  h6Var: typographyStyles.h6Var,
  h1StrongVar: typographyStyles.h1StrongVar,
  h2StrongVar: typographyStyles.h2StrongVar,
  h3StrongVar: typographyStyles.h3StrongVar,
  h4StrongVar: typographyStyles.h4StrongVar,
  h5StrongVar: typographyStyles.h5StrongVar,
  h6StrongVar: typographyStyles.h6StrongVar,
  h1Strong: typographyStyles.h1Strong,
  h2Strong: typographyStyles.h2Strong,
  h3Strong: typographyStyles.h3Strong,
  h4Strong: typographyStyles.h4Strong,
  h5Strong: typographyStyles.h5Strong,
  h6Strong: typographyStyles.h6Strong,
  bodyBase: typographyStyles.bodyBase,
  bodySmall: typographyStyles.bodySmall,
  bodyLargeStrong: typographyStyles.bodyLargeStrong,
  bodyBaseStrong: typographyStyles.bodyBaseStrong,
  bodySmallStrong: typographyStyles.bodySmallStrong,
  bodyLarge: typographyStyles.bodyLarge,
  display: typographyStyles.display,
  displayStrong: typographyStyles.displayStrong,
  displayStrongVar: typographyStyles.displayStrongVar,
  displayVar: typographyStyles.displayVar,
  buttonLabelXLarge: typographyStyles.buttonLabelXLarge,
  buttonLabelLarge: typographyStyles.buttonLabelLarge,
  buttonLabelSmall: typographyStyles.buttonLabelSmall,
  caption: typographyStyles.caption,
  captionStrong: typographyStyles.captionStrong,
  overline: typographyStyles.overline,
  list: typographyStyles.list,
  orderedList: typographyStyles.orderedList,
  ulLarge: typographyStyles.ulLarge,
  ulBase: typographyStyles.ulBase,
  ulSmall: typographyStyles.ulSmall,
  olLarge: typographyStyles.olLarge,
  olBase: typographyStyles.olBase,
  olSmall: typographyStyles.olSmall,
};

const Typography: FC<TypographyProps> = ({
  tag = 'p',
  strong = false,
  isVar = false,
  variant = '',
  children,
  className: propClassName,
  ...rest
}) => {
  // Determine whether to use a heading or paragraph element
  const isHeading = tag.startsWith('h');

  // Get the appropriate tag based on the variant
  const Tag = isHeading ? tag : 'p';

  // Get the appropriate CSS class based on the variant
  let variantClass = variantStyles[tag] || variantStyles.bodyBase;

  if (strong) {
    variantClass = variantStyles[`${tag}Strong`];
  }

  if (isVar) {
    variantClass = variantStyles[`${strong ? `${tag}Strong` : tag}Var`];
  }

  if (variant) {
    variantClass = variantStyles[variant];
  }

  return (
    <Tag
      className={classNames(`typography ${variantClass}`, propClassName)}
      {...rest}
    >
      {children}
    </Tag>
  );
};

export default Typography;
