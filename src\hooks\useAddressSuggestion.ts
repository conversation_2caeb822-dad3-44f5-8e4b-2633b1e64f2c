/* eslint-disable @typescript-eslint/no-explicit-any, no-console, camelcase, @typescript-eslint/naming-convention */
import { useEffect, useMemo, useRef, useState } from 'react';

interface LatLng {
  lat: number | null;
  lng: number | null;
}

interface SuggestedPlace {
  label: string;
  secondaryLabel: string;
  value: string;
  placeId: string;
}

interface UseAddressSuggestionsParams {
  address?: { placeId?: string; label?: string; value?: string };
  street?: string;
  ZIPCode?: string;
}

const TYPES = ['address'];

let autocompleteService = null;
let geocoder = null;
let placesService = null;
let autocompleteSessionToken = null;

declare global {
  interface Window {
    google: {
      maps: {
        LatLng: any;
        Geocoder: any;
        places: {
          AutocompleteService: any;
          Geocoder: any;
          PlacesService: any;
          AutocompleteSessionToken: any;
          LatLng: any;
          placesServiceStatus: any;
          PlacesServiceStatus: any;
        };
      };
    };
  }
}

interface AddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

const getAutocompleteService = () => {
  if (
    !autocompleteService &&
    window?.google?.maps?.places?.AutocompleteService
  ) {
    autocompleteService = new window.google.maps.places.AutocompleteService();
  }
  return autocompleteService;
};

const getGeocoder = () => {
  if (!geocoder && window?.google?.maps?.Geocoder) {
    geocoder = new window.google.maps.Geocoder();
  }
  return geocoder;
};

const getPlacesService = () => {
  if (!placesService && window?.google?.maps?.places?.PlacesService) {
    placesService = new window.google.maps.places.PlacesService(
      document.createElement('div'),
    );
  }
  return placesService;
};

const fetchAutoComplete = async options => {
  try {
    return await getAutocompleteService()?.getPlacePredictions(options);
  } catch (e) {
    console.log('Autocomplete error: ', e);
    return null;
  }
};

const fetchGeocodeResults = async options => {
  try {
    const res = await getGeocoder()?.geocode(options);
    return res;
  } catch (e) {
    console.log('ERROR', e);
    return null;
  }
};

const fetchPlaceDetails = async (options): Promise<any> =>
  new Promise(resolve => {
    getPlacesService()?.getDetails(options, (data, serviceStatus) => {
      if (
        serviceStatus === window?.google?.maps?.places?.PlacesServiceStatus.OK
      ) {
        resolve(data);
      } else {
        console.log(`Place error: ${serviceStatus}`);
        resolve(null);
      }
    });
  });

const getAutocompleteSessionToken = ({
  shouldRefreshSession = true,
}: {
  shouldRefreshSession?: boolean;
}) => {
  if (
    typeof window !== 'undefined' &&
    window?.google?.maps?.places?.AutocompleteSessionToken &&
    (!autocompleteSessionToken || shouldRefreshSession)
  ) {
    autocompleteSessionToken =
      new window.google.maps.places.AutocompleteSessionToken();
  }
  return autocompleteSessionToken;
};

const useLatLngFromAddress = ({
  street = '',
  ZIP = '',
}: {
  street?: string;
  ZIP?: string;
}): LatLng => {
  const [result, setResult] = useState<LatLng>({ lat: null, lng: null });

  const address = useMemo(
    () => [street, ZIP?.length === 5 ? ZIP : null].filter(Boolean).join(' '),
    [ZIP, street],
  );

  useEffect(() => {
    const options = address ? { address } : null;
    fetchGeocodeResults(options).then(data => {
      const res = data?.results[0];
      if (res) {
        setResult({
          lat: res?.geometry?.location?.lat(),
          lng: res?.geometry?.location?.lng(),
        });
      }
    });
  }, [address]);

  return result;
};

export function useAutocompleteFromLatLng({
  input = null,
  lat = null,
  lng = null,
  sessionToken = null,
  types = null,
}) {
  const [predictions, setPredictions] = useState([]);

  useEffect(() => {
    const options =
      input && lat && lng
        ? {
            input,
            radius: 30000,
            location: new window.google.maps.LatLng({ lat, lng }),
            componentRestrictions: { country: 'us' },
            sessionToken,
            types,
          }
        : null;

    fetchAutoComplete(options).then(data => {
      if (data?.predictions) setPredictions(data?.predictions);
    });
  }, [input, lat, lng, sessionToken, types]);

  return predictions;
}

const useSuggestedPlacesFromLatLng = ({
  input,
  lat,
  lng,
  sessionToken,
  types = null,
}: {
  input: string | null;
  lat: number | null;
  lng: number | null;
  sessionToken: unknown;
  types?: string[] | null;
}): SuggestedPlace[] => {
  const places = useAutocompleteFromLatLng({
    input,
    lat,
    lng,
    sessionToken,
    types,
  });

  const mapPlaces = useMemo(
    () =>
      places && places.length > 0
        ? places.map(
            ({
              place_id,
              structured_formatting: { main_text, secondary_text },
            }) => ({
              label: main_text,
              secondaryLabel: `${secondary_text.split(',')[0]}, ${secondary_text.split(',')[1]}`,
              value: main_text,
              placeId: place_id,
            }),
          )
        : [],
    [places],
  );
  return mapPlaces;
};

const getAddressComponentFromType = ({
  type,
  addressComponents,
}: {
  type: string;
  addressComponents: AddressComponent[];
}): AddressComponent | null => {
  if (!addressComponents) return null;
  let retValue: AddressComponent | null = null;
  addressComponents.forEach(value => {
    if (value.types.includes(type)) {
      retValue = value;
    }
  });
  return retValue;
};

const useCityStateZIPCodeFromPlaceId = ({
  placeId,
  sessionToken,
}: {
  placeId: string | null;
  sessionToken: unknown;
}) => {
  const [data, setData] = useState({
    street: undefined,
    city: undefined,
    state: undefined,
    ZIPCode: undefined,
    isLoading: false,
  });

  useEffect(() => {
    const options = placeId
      ? {
          placeId,
          fields: ['address_components'],
          sessionToken,
        }
      : null;

    setData(prev => ({ ...prev, isLoading: true }));
    fetchPlaceDetails(options)
      .then((place: any) => {
        // <== Fix here
        if (place) {
          const streetNumber = getAddressComponentFromType({
            type: 'street_number',
            addressComponents: place?.address_components,
          })?.long_name;
          const route = getAddressComponentFromType({
            type: 'route',
            addressComponents: place?.address_components,
          })?.long_name;
          const street =
            streetNumber && route ? `${streetNumber} ${route}` : null;
          setData({
            street,
            city: getAddressComponentFromType({
              type: 'locality',
              addressComponents: place?.address_components,
            })?.long_name,
            state: getAddressComponentFromType({
              type: 'administrative_area_level_1',
              addressComponents: place?.address_components,
            })?.short_name,
            ZIPCode: getAddressComponentFromType({
              type: 'postal_code',
              addressComponents: place?.address_components,
            })?.long_name,
            isLoading: false,
          });
        }
      })
      .finally(() => {
        setData(prev => ({ ...prev, isLoading: false }));
      });
  }, [placeId, sessionToken]);

  return data;
};

const useAddressSuggestions = ({
  address,
  street,
  ZIPCode,
}: UseAddressSuggestionsParams) => {
  const addressAutocompleteSessionToken = useRef<unknown>(null);
  const previousAddress = useRef<{ placeId?: string } | null>(null);
  const placeIdToUse = address?.placeId;

  const { lat, lng } = useLatLngFromAddress({ street, ZIP: ZIPCode });

  const suggestedAddresses = useSuggestedPlacesFromLatLng({
    input: street || '',
    lat,
    lng,
    sessionToken: addressAutocompleteSessionToken.current,
    types: TYPES,
  });

  const {
    street: suggestedStreet,
    ZIPCode: suggestedZIPCode,
    isLoading: isLoadingSuggestions,
  } = useCityStateZIPCodeFromPlaceId({
    placeId: placeIdToUse,
    sessionToken: addressAutocompleteSessionToken.current,
  });

  useEffect(() => {
    const newToken = getAutocompleteSessionToken({
      shouldRefreshSession:
        address?.placeId !== previousAddress.current?.placeId,
    });

    if (newToken !== addressAutocompleteSessionToken.current) {
      addressAutocompleteSessionToken.current = newToken;
    }

    previousAddress.current = address;
  }, [address]);

  return {
    suggestedStreet,
    suggestedZIPCode,
    suggestedAddresses,
    isLoading: isLoadingSuggestions,
  };
};

export default useAddressSuggestions;
