import React from 'react';
import Tooltip from '@components/Tooltip';
import { EMPTY_TABLE_VALUE } from '@constants/common';
import classNames from '@utils/classNames';

import styles from './TableText.module.css';

interface TableTextTooltipProps {
  isInverted?: boolean;
  isActive?: boolean;
  customStyle?: React.CSSProperties;
  className?: string;
  children: React.ReactNode;
  tooltip: {
    text?: string;
    placement?: string;
  };
  id?: string;
}

const TableTextTooltip: React.FC<TableTextTooltipProps> = ({
  isInverted,
  isActive,
  customStyle,
  className,
  children,
  tooltip,
  id,
}) => (
  <Tooltip text={tooltip?.text}>
    <div
      className={classNames(
        styles.text,
        isActive && styles.active,
        isInverted && styles.isInverted,
        className,
      )}
      style={customStyle}
      id={id}
    >
      {children || EMPTY_TABLE_VALUE}
    </div>
  </Tooltip>
);

export default TableTextTooltip;
