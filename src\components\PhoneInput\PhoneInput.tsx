import React, { useCallback, useRef } from 'react';
import TextInput from '@components/TextInput';

import { PhoneInputPropTypes } from './PhoneInput.types';

const MAX_RAW_INPUT_VALUE_LENGTH = 10;

const PhoneInput = ({
  id = '',
  label = 'Phone number',
  caption = '',
  onChange,
  value,
  error,
  isDisabled = false,
  required = false,
  onInvalid = () => {},
  onFocus = () => {},
  onBlur = () => {},
  name = '',
  pattern = '[0-9]{3}-[0-9]{3}-[0-9]{4}',
}: PhoneInputPropTypes) => {
  // Formats the value into a phone number format (xxx-xxx-xxxx)
  const formatValue = useCallback((val: string): string => {
    if (!val) return '';
    return val
      .replace(/\D/g, '') // Remove non-digit characters
      .replace(
        /(\d{0,3})?(\d{0,3})?(\d{0,4})?/,
        (_, match1, match2, match3) =>
          [match1, match2, match3].filter(Boolean).join('-'), // Format into xxx-xxx-xxxx
      );
  }, []);

  const inputRef = useRef<HTMLInputElement>(null);

  // Extracts raw digits from a formatted phone number
  const getRawValueFromFormatted = useCallback(
    (formattedValue: string): string => formattedValue.replace(/\D/g, ''),
    [],
  );

  // Enforces the maximum input length
  const enforceMaxLength = useCallback(
    (val: string): string => val.slice(0, MAX_RAW_INPUT_VALUE_LENGTH),
    [],
  );

  // Handles clipboard events (cut, copy, paste) to manage formatting and cursor positioning
  const handleClipboardEvent = useCallback(
    (
      event: React.ClipboardEvent<HTMLInputElement>,
      val: string,
      onValueChange: (val_: string) => void,
      action: 'cut' | 'copy' | 'paste',
    ) => {
      const input = event.target as HTMLInputElement;
      const selectionStart = input.selectionStart ?? 0;
      const selectionEnd = input.selectionEnd ?? input?.value?.length;

      const formattedValue = formatValue(val);
      const rawValue = getRawValueFromFormatted(formattedValue);

      // Calculate adjusted selection range in raw value
      const adjustedStart = formattedValue
        .slice(0, selectionStart)
        .replace(/\D/g, '').length;
      const adjustedEnd = formattedValue
        .slice(0, input.selectionEnd)
        .replace(/\D/g, '').length;

      const selectedText = formattedValue.slice(selectionStart, selectionEnd);

      if (action === 'copy') {
        // Handle copying formatted text as raw value
        event.clipboardData.setData(
          'text/plain',
          getRawValueFromFormatted(selectedText),
        );
        event.preventDefault();
      }

      if (action === 'cut') {
        // Handle cutting text: remove from raw value and update state
        const selectedTextRaw = getRawValueFromFormatted(selectedText);
        event.clipboardData.setData('text/plain', selectedTextRaw);
        event.preventDefault();

        const newRawValue =
          rawValue.slice(0, adjustedStart) + rawValue.slice(adjustedEnd);
        onValueChange(formatValue(enforceMaxLength(newRawValue)));

        requestAnimationFrame(() => {
          input.setSelectionRange(selectionStart, selectionStart);
        });
      }

      if (action === 'paste') {
        // Handle pasting text: insert formatted text and update state
        const pastedData = event.clipboardData
          .getData('text/plain')
          .replace(/\D/g, '');
        const formattedPastedData = formatValue(pastedData);
        event.preventDefault();

        const newValue =
          formattedValue.slice(0, selectionStart) +
          formattedPastedData +
          formattedValue.slice(input.selectionEnd);

        const newRawValue = getRawValueFromFormatted(newValue);
        onValueChange(formatValue(enforceMaxLength(newRawValue)));

        requestAnimationFrame(() => {
          let newCursorPosition = selectionStart + pastedData.length;
          const extraShiftCount = Math.min(
            2,
            Math.floor(pastedData.length / 3),
          );
          newCursorPosition += extraShiftCount;
          input.setSelectionRange(newCursorPosition, newCursorPosition);
        });
      }
    },
    [enforceMaxLength, formatValue, getRawValueFromFormatted],
  );

  const handleCopy = useCallback(
    (event: React.ClipboardEvent<HTMLInputElement>) => {
      handleClipboardEvent(event, value, onChange, 'copy');
    },
    [handleClipboardEvent, onChange, value],
  );

  const handleCut = useCallback(
    (event: React.ClipboardEvent<HTMLInputElement>) => {
      handleClipboardEvent(event, value, onChange, 'cut');
    },
    [handleClipboardEvent, onChange, value],
  );

  const handlePaste = useCallback(
    (event: React.ClipboardEvent<HTMLInputElement>) => {
      handleClipboardEvent(event, value, onChange, 'paste');
    },
    [handleClipboardEvent, onChange, value],
  );

  const handleChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const input = event.target as HTMLInputElement;
      const rawValue = input.value.replace(/\D/g, '');
      event.preventDefault();
      const selectionStart = input.selectionStart ?? 0;
      const formattedValue = formatValue(enforceMaxLength(rawValue));

      const adjustedSelectionStart =
        selectionStart === 4 || selectionStart === 8
          ? selectionStart + 1
          : selectionStart;

      if (formattedValue.length < 1) {
        inputRef.current.value = '';
      }
      onChange(formattedValue);

      requestAnimationFrame(() => {
        input.setSelectionRange(adjustedSelectionStart, adjustedSelectionStart);
      });

      if (rawValue === '' && inputRef.current) {
        inputRef.current.checkValidity();
      }
    },
    [enforceMaxLength, formatValue, inputRef, onChange],
  );

  return (
    <TextInput
      id={id}
      label={label}
      type="tel"
      caption={caption}
      pattern={pattern}
      value={formatValue(value)}
      error={error}
      isDisabled={isDisabled}
      onChange={handleChange}
      required={required}
      onInvalid={onInvalid}
      onFocus={onFocus}
      onBlur={onBlur}
      onCopy={handleCopy}
      onPaste={handlePaste}
      onCut={handleCut}
      ref={inputRef}
      name={name}
      maxLength={MAX_RAW_INPUT_VALUE_LENGTH + 2}
    />
  );
};

export default PhoneInput;
