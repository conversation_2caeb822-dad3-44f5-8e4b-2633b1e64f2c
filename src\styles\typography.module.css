@value variables: "./variables.module.css";
@value black, oneSpace, twoSpace, threeSpace, smallWidth, xLargeWidth from variables;

.display {
  font-size: var(--displayFontSize);
  font-weight: normal;
  line-height: var(--displayLineHeight);
  letter-spacing: -0.04em;
}

.displayVar {
  composes: display;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wdth' 50,
    'opsz' 30;
}

.displayStrong {
  composes: display;
  font-weight: 800;
}

.displayStrongVar {
  composes: displayStrong;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wght' 900,
    'wdth' 50,
    'opsz' 30;
}

.displayMobile {
  font-size: var(--displayMobileFontSize);
  font-weight: normal;
  line-height: var(--displayMobileLineHeight);
  letter-spacing: -0.04em;
}

.displayMobileStrong {
  composes: displayMobile;
  font-weight: 800;
}

.h1 {
  font-size: var(--h1FontSize);
  font-weight: normal;
  line-height: var(--h1LineHeight);
  letter-spacing: -0.04em;
}

.h1Var {
  composes: h1;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wdth' 50,
    'opsz' 30;
}

.h1Strong {
  composes: h1;
  font-weight: 800;
}

.h1StrongVar {
  composes: h1Strong;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wght' 800,
    'wdth' 50,
    'opsz' 30;
}

.h2 {
  font-size: var(--h2FontSize);
  font-weight: normal;
  line-height: var(--h2LineHeight);
  letter-spacing: -0.04em;
}

.h2Var {
  composes: h2;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wdth' 50,
    'opsz' 30;
}

.h2Strong {
  composes: h2;
  font-weight: 800;
}

.h2StrongVar {
  composes: h2Strong;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wght' 800,
    'wdth' 50,
    'opsz' 30;
}

.h3 {
  font-size: var(--h3FontSize);
  font-weight: normal;
  line-height: var(--h3LineHeight);
  letter-spacing: -0.04em;

  @media (max-width: smallWidth) {
    letter-spacing: -0.03em;
  }
}

.h3Var {
  composes: h3;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wdth' 50,
    'opsz' 30;
}

.h3Strong {
  composes: h3;
  font-weight: 800;
}

.h3StrongVar {
  composes: h3Strong;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wght' 800,
    'wdth' 50,
    'opsz' 30;
}

.h4 {
  font-size: var(--h4FontSize);
  font-weight: normal;
  line-height: var(--h4LineHeight);
  letter-spacing: -0.03em;

  @media (max-width: smallWidth) {
    letter-spacing: -0.02em;
  }
}

.h4Var {
  composes: h4;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wdth' 50,
    'opsz' 30;
}

.h4Strong {
  composes: h4;
  font-weight: 800;
}

.h4StrongVar {
  composes: h4Strong;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wght' 800,
    'wdth' 50,
    'opsz' 30;
}

.h5 {
  font-size: var(--h5FontSize);
  font-weight: normal;
  line-height: var(--h5LineHeight);
  letter-spacing: -0.02em;

  @media (max-width: smallWidth) {
    letter-spacing: -0.01em;
  }
}

.h5Var {
  composes: h5;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wdth' 50,
    'opsz' 30;
}

.h5Strong {
  composes: h5;
  font-weight: 800;
}

.h5StrongVar {
  composes: h5Strong;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wght' 800,
    'wdth' 50,
    'opsz' 30;
}

.h6 {
  font-size: var(--h6FontSize);
  font-weight: normal;
  line-height: var(--h6LineHeight);
  letter-spacing: -0.01em;

  @media (max-width: smallWidth) {
    letter-spacing: 0em;
  }
}

.h6Var {
  composes: h6;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wdth' 50,
    'opsz' 30;
}

.h6Strong {
  composes: h6;
  font-weight: 800;
}

.h6StrongVar {
  composes: h6Strong;
  line-height: var(--displayHeaderLineHeight);
  letter-spacing: -0.01em;
  font-family: 'Helvetica Now Var', 'Aventa', sans-serif;
  font-variation-settings:
    'wght' 800,
    'wdth' 50,
    'opsz' 30;
}

.bodyLarge {
  font-size: var(--bodyLargeFontSize);
  font-weight: normal;
  line-height: 1.6;
  letter-spacing: 0.01em;
}
.bodyLargeStrong {
  composes: bodyLarge;
  font-weight: 600;
}

.bodyBase {
  font-size: var(--bodyBaseFontSize);
  font-weight: normal;
  line-height: 1.5;
  letter-spacing: 0.01em;
}

.bodyBaseStrong {
  composes: bodyBase;
  font-weight: 700;
}

.bodySmall {
  font-size: var(--bodySmallFontSize);
  font-weight: normal;
  line-height: 1.43;
  letter-spacing: 0.01em;
}

.bodySmallStrong {
  composes: bodySmall;
  font-weight: 700;
}

.buttonLabelXLarge {
  font-size: var(--buttonLabelXLargeFontSize);
  line-height: 1.5;
  font-weight: 700;
  letter-spacing: 0em;
}

.buttonLabelLarge {
  font-size: var(--buttonLabelLargeFontSize);
  line-height: 1.5;
  font-weight: 700;
  letter-spacing: 0em;
}

.buttonLabelSmall {
  font-size: var(--buttonLabelSmallFontSize);
  line-height: 1.43;
  font-weight: 700;
  letter-spacing: 0em;
}

.caption {
  font-size: var(--captionFontSize);
  line-height: var(--captionLineHeight);
  font-weight: normal;
  letter-spacing: 0.01em;
}

.captionStrong {
  composes: caption;
  font-weight: 700;
}

.overline {
  font-size: var(--overlineFontSize);
  line-height: 1.67;
  font-weight: 600;
  letter-spacing: 0.01em;
  text-transform: uppercase;
}

.list {
  list-style: none;
  counter-reset: list-counter;

  & li {
    margin-bottom: oneSpace;
    counter-increment: list-counter;
  }
}

.orderedList {
  composes: list;

  & li {
    &:before {
      content: counter(list-counter) '.';
    }
  }
}

.ulLarge {
  composes: bodyLarge;
  composes: list;

  & li {
    &:before {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: 3px solid;
      margin-right: threeSpace;
    }
  }
}

.ulBase {
  composes: bodyBase;
  composes: list;

  & li {
    &:before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      border: 3px solid;
      margin-right: twoSpace;
    }
  }
}

.ulSmall {
  composes: bodySmall;
  composes: list;

  & li {
    &:before {
      content: '';
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      border: 3px solid;
      margin-right: twoSpace;
    }
  }
}

.olLarge {
  composes: bodyLarge;
  composes: orderedList;

  & li {
    &:before {
      margin-right: threeSpace;
      font-weight: 800;
    }
  }
}

.olBase {
  composes: bodyBase;
  composes: orderedList;

  & li {
    &:before {
      margin-right: twoSpace;
      font-weight: 700;
    }
  }
}

.olSmall {
  composes: bodySmall;
  composes: orderedList;

  & li {
    &:before {
      margin-right: twoSpace;
      font-weight: 700;
    }
  }
}
