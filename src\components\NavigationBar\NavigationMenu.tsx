import { ReactNode, SyntheticEvent } from 'react';
import classNames from '@utils/classNames';

import NavListMobile from './mobileNavigation/NavListMobile';
import styles from './NavigationBar.module.css';

interface NavigationItem {
  label: string;
  isActive?: boolean;
  link?: string;
  listItems?: NavigationItem[];
}

interface NavListMobileProps {
  primaryLinks: NavigationItem[];
  mobileMenuButtons?: ReactNode;
  onClick?: (event: SyntheticEvent<Element, Event>) => void;
  renderMobileMenu: () => ReactNode;
  hasCustomMenuMobile?: boolean;
  isNavigationRight?: boolean;
}

const NavigationMenu = ({
  primaryLinks,
  mobileMenuButtons,
  onClick,
  hasCustomMenuMobile,
  renderMobileMenu,
  isNavigationRight,
}: NavListMobileProps) => (
  <div
    className={classNames(
      styles.menu,
      styles.isDesktop,
      isNavigationRight && styles.navigationRight,
    )}
  >
    <NavListMobile
      navigationMenuList={primaryLinks}
      mobileMenuButtons={mobileMenuButtons}
      onClick={onClick}
      hasCustomMenuMobile={hasCustomMenuMobile}
      renderMobileMenu={renderMobileMenu}
    />
  </div>
);

export default NavigationMenu;
