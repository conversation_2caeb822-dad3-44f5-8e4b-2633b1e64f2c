import { ReactEventHandler, ReactNode } from 'react';
import { ButtonSize, ButtonTheme } from '@components/Button/Button.types';
import { ModalTracker } from '@libtypes/modal';

export interface ModalButtonProps {
  isFullWidthButton?: boolean;
  buttonSize?: ButtonSize;
  onClick?: ReactEventHandler;
  buttonRight?: ReactNode;
  buttonLeft?: ReactNode;
  isDisabled?: boolean;
  theme: ButtonTheme;
  label: string;
  modalButtonActionType?: string;
  isLoading?: boolean;
  wrapperClassName?: string;
  tracker?: ModalTracker;
  modalName?: string;
}
