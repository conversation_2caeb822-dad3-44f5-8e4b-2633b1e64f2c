import { MutableRefObject, useEffect, useRef, useState } from 'react';
import throttle from '@utils/throttle';

function useWindowSize() {
  const [size, setSize] = useState({
    width: 0,
    height: 0,
  });
  const timeoutRef: MutableRefObject<number> = useRef();

  const handleResize = throttle(() => {
    window.clearTimeout(timeoutRef.current);

    timeoutRef.current = setTimeout(() => {
      setSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }, 166) as unknown as number;
  }, 100);

  useEffect(() => {
    window.addEventListener('resize', handleResize);

    setSize({
      width: window.innerWidth,
      height: window.innerHeight,
    });

    return () => {
      window.removeEventListener('resize', handleResize);
      window.clearTimeout(timeoutRef.current);
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  return size;
}

export default useWindowSize;
