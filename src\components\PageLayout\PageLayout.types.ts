import { ReactNode } from 'react';

export interface PageLayoutProps {
  /**
   * Children can be placed within the children placeholder in the JSX of the modal
   */
  children: ReactNode;
  /**
   * Background color of Page Layout
   */
  backgroundColor?: string;
  /**
   * Controls overflow visibility
   */
  isOverflowHidden?: boolean;
  /**
   * Enables background transition effect
   */
  hasBackgroundTransition?: boolean;
  /**
   * Enables sidebar
   */
  sidebar?: ReactNode;
  /**
   * Enables footer
   */
  footer?: ReactNode;
  /**
   * Enables left and right padding (16px)
   */
  hasLeftRightPadding?: boolean;
}
