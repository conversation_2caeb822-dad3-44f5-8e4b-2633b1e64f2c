/* eslint-disable import/prefer-default-export */
import variables from '@styles/variables.module.css';

export const colorThemeMapping = {
  primary: {
    light: variables.primary,
    dark: variables.primaryDark,
    border: variables.primary,
  },
  primaryInverted: {
    light: variables.white,
    dark: variables.primary5,
    border: variables.primary,
  },
  primaryFaded: {
    light: variables.primary5,
    dark: variables.primary10,
    border: variables.primary,
  },
  danger: {
    light: variables.danger,
    dark: variables.dangerDark,
    border: variables.danger,
  },
  dangerInverted: {
    light: variables.white,
    dark: variables.danger5,
    border: variables.danger,
  },
  dangerFaded: {
    light: variables.danger5,
    dark: variables.danger10,
    border: variables.danger,
  },
  success: {
    light: variables.success,
    dark: variables.successDark,
    border: variables.success,
  },
  successInverted: {
    light: variables.white,
    dark: variables.success5,
    border: variables.success,
  },
  successFaded: {
    light: variables.success5,
    dark: variables.success10,
    border: variables.success,
  },
  warning: {
    light: variables.warning,
    dark: variables.warningDark,
    border: variables.warning,
  },
  warningInverted: {
    light: variables.white,
    dark: variables.warning5,
    border: variables.warning,
  },
  warningFaded: {
    light: variables.warning5,
    dark: variables.warning10,
    border: variables.warning,
  },
};

export const TYPE_MISMATCH_MESSAGE = "Oops. That's not right.";

export const PORTRAIT_MODE_ONLY_MODAL_TITLE = 'say no to landscape mode';
export const PORTRAIT_MODE_ONLY_MODAL_BODY =
  'Do us a solid and give your mobile device a little flipper-dee-doo, k? We work waaaay better in portrait mode. Appreciate it.';

// ERROR BOUNDARY Component
export const ERROR_BOUNDARY_HEADING = "oops, that's our bad";
export const ERROR_BOUNDARY_BODY =
  "Something went wrong here. Apologies. Let's try again k?";
export const ERROR_BOUNDARY_BUTTON_LABEL = 'Try Again';
export const ERROR_BOUNDARY_BUTTON_THEME = 'warning';

// Table
export const EMPTY_TABLE_VALUE = ' - ';
