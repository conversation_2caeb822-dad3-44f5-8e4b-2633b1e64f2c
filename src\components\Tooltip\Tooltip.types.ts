import React from 'react';

/**
 * Props for the Tooltip component.
 */
export interface TooltipProps {
  /**
   * The text to be displayed inside the tooltip.
   */
  text: string;

  /**
   * The content over which the tooltip will be displayed.
   */
  children: React.ReactNode;

  /**
   * The preferred placement of the tooltip relative to the children.
   * Can be 'top', 'bottom', 'left', or 'right'.
   * Optional, defaults to 'top'.
   */
  placement?: 'top' | 'bottom' | 'left' | 'right';
}
