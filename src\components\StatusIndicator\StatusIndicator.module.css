@value variables: "../../styles/variables.module.css";
@value black, neutralDark, neutralDarker, danger, primary, success, warning from variables;

.statusIndicator {
  border-radius: 50%;
  border-width: 3px;
  border-style: solid;
  border-color: black;
  width: 12px;
  height: 12px;
  display: inline-block;

  &.isComplete {
    background-color: black;
    width: 8px;
    height: 8px;
  }

  &.darkColor {
    &.isComplete {
      background-color: black;
    }
  }

  &.neutralDarkColor {
    border-color: neutralDark;
    &.isComplete {
      background-color: neutralDark;
    }
  }

  &.neutralDarkerColor {
    border-color: neutralDarker;
    &.isComplete {
      background-color: neutralDarker;
    }
  }

  &.dangerColor {
    border-color: danger;
    &.isComplete {
      background-color: danger;
    }
  }

  &.primaryColor {
    border-color: primary;
    &.isComplete {
      background-color: primary;
    }
  }

  &.successColor {
    border-color: success;
    &.isComplete {
      background-color: success;
    }
  }

  &.warningColor {
    border-color: warning;
    &.isComplete {
      background-color: warning;
    }
  }
}
