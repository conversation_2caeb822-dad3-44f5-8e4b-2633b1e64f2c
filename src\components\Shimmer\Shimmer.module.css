@value variables: "../../styles/variables.module.css";
@value neutralLighter, neutralLightest from variables;

.shimmer {
  display: inline-block;
  background: neutralLighter;
  border-radius: 4px;
  position: relative;
  overflow: hidden;

  &.isMiddleAligned {
    vertical-align: middle;
  }

  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(
      to right,
      neutralLighter 0%,
      neutralLightest 20%,
      neutralLighter 40%,
      neutralLighter 100%
    );
    animation-duration: 1s;
    animation-fill-mode: forwards;
    animation-iteration-count: infinite;
    animation-name: shimmer;
    animation-timing-function: linear;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}
