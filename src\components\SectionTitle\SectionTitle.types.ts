import { ReactNode } from 'react';
import { TagsTypes } from '@components/Typography/Typography.types';

export type ActionDirection = 'right' | 'left';
export interface SectionTitleProps {
  /**
   * Heading of the section
   */
  headingText?: string;
  /**
   * Icon to display alongside the heading
   */
  icon?: ReactNode;
  /**
   * An array of Action Button objects representing action buttons within the section.
   */
  actions?: ReactNode[];
  /**
   * The alignment of action buttons within the section
   */
  actionDirection?: ActionDirection;
  /**
   * Tag can be set as native element for the heading for example tag p heading will be wrapped as a <p>{text}</p>
   */
  headingTag?: TagsTypes;
  /**
   * Props for the heading tag
   */
  headingProps?: Record<string, unknown>;
}
