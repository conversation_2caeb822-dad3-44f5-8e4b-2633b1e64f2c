import styles from './StatusIndicator.module.css';

export type StatusIndicatorTheme =
  | 'dark'
  | 'primary'
  | 'danger'
  | 'success'
  | 'warning'
  | 'neutralDark'
  | 'neutralDarker';

interface StatusIndicatorProps {
  isComplete?: boolean;
  theme: StatusIndicatorTheme;
}

const StatusIndicator = ({
  isComplete = false,
  theme = 'dark',
}: StatusIndicatorProps) => (
  <div
    className={[
      styles.statusIndicator,
      styles[`${theme}Color`],
      isComplete && styles.isComplete,
    ]
      .filter(Boolean)
      .join(' ')}
  />
);

export default StatusIndicator;
