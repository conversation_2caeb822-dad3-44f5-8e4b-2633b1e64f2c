import React from 'react';
import { render } from '@testing-library/react';
import { MenuItem as SelectInputMenuItem } from '@components/Dropdown';

describe('SelectInputMenuItem Component', () => {
  it('renders without crashing', () => {
    const { getByText } = render(
      <SelectInputMenuItem>Item</SelectInputMenuItem>,
    );
    expect(getByText('Item')).toBeInTheDocument();
  });

  it('renders with left content', () => {
    const { container } = render(
      <SelectInputMenuItem left={<div>Left Content</div>}>
        Item
      </SelectInputMenuItem>,
    );
    expect(container.querySelector('.item.hasLeftItem')).toBeInTheDocument();
  });

  it('renders with secondary label', () => {
    const { getByText } = render(
      <SelectInputMenuItem secondaryLabel="Secondary Label">
        Item
      </SelectInputMenuItem>,
    );
    expect(getByText('Secondary Label')).toBeInTheDocument();
  });

  it('applies highlighted style when isHighlighted prop is true', () => {
    const { container } = render(
      <SelectInputMenuItem isHighlighted>Item</SelectInputMenuItem>,
    );
    expect(
      container.querySelector('.item.itemHighlighted'),
    ).toBeInTheDocument();
  });

  it('does not apply highlighted style when isHighlighted prop is false', () => {
    const { container } = render(
      <SelectInputMenuItem isHighlighted={false}>Item</SelectInputMenuItem>,
    );
    expect(container.querySelector('.item.itemHighlighted')).toBeNull();
  });
});
