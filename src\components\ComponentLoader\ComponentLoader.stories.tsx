import { Meta } from '@storybook/react';

import ComponentLoader from './ComponentLoader';

/**
 * The ComponentLoader component displays a isLoading indicator while content is being loaded.
 *
 * ## Overview
 *
 * The ComponentLoader component is used to indicate that content is being loaded. It displays a isLoading spinner or any other isLoading indicator.
 *
 * ## Usage
 *
 * To use the ComponentLoader component, import it into your React application and render it with the appropriate props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { ComponentLoader } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, include the ComponentLoader component in your JSX:
 *
 * ```jsx
 * <ComponentLoader isLoading={true}>
 *   <div>Content goes here</div>
 * </ComponentLoader>
 * ```
 *
 * This will render a isLoading indicator when the `isLoading` prop is set to true.
 */

const meta: Meta<typeof ComponentLoader> = {
  component: ComponentLoader,
  title: 'Components/ComponentLoader',
  tags: ['autodocs'],
};

export const Default = ({ isLoading, children, size }) => (
  <ComponentLoader isLoading={isLoading} size={size}>
    {children}
  </ComponentLoader>
);

Default.args = {
  isLoading: true,
  children: <div style={{ padding: '20px' }}>Content goes here</div>,
  size: 24,
};
Default.parameters = {
  docs: {
    storyDescription:
      'This is the default state of the ComponentLoader with isLoading set to true.',
  },
};

export default meta;
