import { render, screen } from '@testing-library/react';

import Table from './Table';

describe('Table component', () => {
  const columns = [
    {
      id: 'id',
      header: 'id',
      accessorKey: 'id',
      maxSize: 30,
    },
    {
      id: 'Description',
      header: () => 'Description',
      accessorKey: 'description',
      maxSize: 150,
    },
    // Add other columns as needed
  ];

  const rowData = [
    { id: 1, description: 'Description 1' },
    { id: 2, description: 'Description 2' },
    // Add more rows as needed
  ];

  const rowDataLoading = [
    { id: 1, description: 'Description 1', isLoading: true },
    { id: 2, description: 'Description 2' },
    // Add more rows as needed
  ];

  it('renders table with correct data', () => {
    render(<Table columns={columns} rowData={rowData} />);

    // Assert table headers
    expect(screen.getByText('id')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();

    // Assert table rows
    expect(screen.getByText('Description 1')).toBeInTheDocument();
    expect(screen.getByText('Description 2')).toBeInTheDocument();
    // Add more assertions for other rows as needed
  });

  it('is should load the loading', () => {
    render(<Table columns={columns} rowData={rowDataLoading} />);

    // Assert table headers
    expect(screen.getByText('id')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();

    const shimmerElements = screen.getAllByTestId(/shimmer/i); // Matches elements with data-testid containing "shimmer"

    // Assert table rows
    expect(shimmerElements.length).toBe(2);
    expect(screen.getByText('Description 2')).toBeInTheDocument();
  });
});
