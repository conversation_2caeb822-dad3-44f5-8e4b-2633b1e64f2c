/* eslint-disable no-console */
import { useEffect, useMemo, useState } from 'react';
import { Meta } from '@storybook/react';
import { generateDatesBetween } from '@utils/date';
import formatScheduleWindows from '@utils/formatScheduleWindow';
import variableStyles from '@styles/variables.module.css';

import TimeSlotPicker from './TimeSlotPicker';
import { TimeSlotPickerPropTypes } from './TimeSlotPicker.types';

/**
 * The TimeSlotPicker component displays a list of time slots for selecting a time window.
 *
 * ## Overview
 *
 * The TimeSlotPicker component provides a user-friendly interface for selecting time ranges within a schedule. It visualizes available time slots and allows users to easily navigate and choose suitable time intervals.
 *
 * ## Usage
 *
 * To integrate the TimeSlotPicker component into your React application, follow these steps:
 *
 * Step 1: Import the TimeSlotPicker component:
 *
 * ```jsx
 * import { TimeSlotPicker } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Step 2: Include the TimeSlotPicker component in your JSX:
 *
 * ```jsx
 * <TimeSlotPicker
 *   onSelect={() => {}}
 *   rowItems={[
 *     {
 *       date: '2024-04-16T07:00',
 *       isDisabled: false,
 *       items: [
 *         {
 *           endAt: '2024-04-16T11:00',
 *           id: '2024-04-16T07:00',
 *           startAt: '2024-04-16T07:00',
 *           timeEndAt: '11:00',
 *           timeStartAt: '07:00'
 *         }
 *       ]
 *     },
 *     {
 *       date: '2024-04-18T07:00',
 *       isDisabled: false,
 *       items: []
 *     },
 *   ]}
 *   value="2024-04-17T15:00"
 * />
 * ```
 *
 * NOTE:
 * - Ensure that the data passed to `rowItems` and `items` within each `rowItem` is correctly formatted.
 * - Provide JSX only for the EmptyState component to be rendered when no time slots are available.
 *
 * This will render a TimeSlotPicker component with the specified time slots and time zone.
 */
const meta: Meta<typeof TimeSlotPicker> = {
  component: TimeSlotPicker,
  title: 'Components/TimeSlotPicker',
  tags: ['autodocs'],
};

export const TimeSlotPickerStory = (containerProp: TimeSlotPickerPropTypes) => {
  const { rowItems, value } = containerProp;
  const [selectValue, setSelectValue] = useState(value);
  useEffect(() => setSelectValue(value), [value]);

  const handleSelect = ({ id }) => {
    setSelectValue(id);
  };

  const formatedSchedulingWindows = useMemo(() => {
    const startDate = rowItems[0]?.date;
    const endDate = rowItems[rowItems.length - 1]?.date;
    return formatScheduleWindows(
      rowItems,
      generateDatesBetween(startDate, endDate),
    );
  }, [rowItems]);

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
        height: '100vh',
      }}
    >
      <TimeSlotPicker
        {...containerProp}
        onSelect={handleSelect}
        rowItems={formatedSchedulingWindows}
        value={selectValue}
      />
    </div>
  );
};

TimeSlotPickerStory.args = {
  isDisabled: false,
  rowItems: [
    {
      isDisabled: false,
      date: '2024-04-16T07:00',
      items: [
        {
          id: '2024-04-16T07:00',
          startAt: '2024-04-16T07:00',
          endAt: '2024-04-16T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-17T07:00',
      items: [
        {
          id: '2024-04-17T07:00',
          startAt: '2024-04-17T07:00',
          endAt: '2024-04-17T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
        {
          id: '2024-04-17T11:00',
          startAt: '2024-04-17T11:00',
          endAt: '2024-04-17T15:00',
          timeStartAt: '11:00',
          timeEndAt: '15:00',
        },
        {
          id: '2024-04-17T15:00',
          startAt: '2024-04-17T15:00',
          endAt: '2024-04-17T20:00',
          timeStartAt: '15:00',
          timeEndAt: '20:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-18T07:00',
      items: [],
    },
    {
      isDisabled: false,
      date: '2024-04-19T07:00',
      items: [
        {
          id: '2024-04-19T07:00',
          startAt: '2024-04-19T07:00',
          endAt: '2024-04-19T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
        {
          id: '2024-04-19T11:00',
          startAt: '2024-04-19T11:00',
          endAt: '2024-04-19T15:00',
          timeStartAt: '11:00',
          timeEndAt: '15:00',
        },
        {
          id: '2024-04-19T15:00',
          startAt: '2024-04-19T15:00',
          endAt: '2024-04-19T20:00',
          timeStartAt: '15:00',
          timeEndAt: '20:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-20T08:00',
      items: [
        {
          id: '2024-04-20T08:00',
          startAt: '2024-04-20T08:00',
          endAt: '2024-04-20T12:00',
          timeStartAt: '08:00',
          timeEndAt: '12:00',
        },
        {
          id: '2024-04-20T12:00',
          startAt: '2024-04-20T12:00',
          endAt: '2024-04-20T16:00',
          timeStartAt: '12:00',
          timeEndAt: '16:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-21T08:00',
      items: [
        {
          id: '2024-04-21T08:00',
          startAt: '2024-04-21T08:00',
          endAt: '2024-04-21T12:00',
          timeStartAt: '08:00',
          timeEndAt: '12:00',
        },
        {
          id: '2024-04-21T12:00',
          startAt: '2024-04-21T12:00',
          endAt: '2024-04-21T16:00',
          timeStartAt: '12:00',
          timeEndAt: '16:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-22T07:00',
      items: [
        {
          id: '2024-04-22T07:00',
          startAt: '2024-04-22T07:00',
          endAt: '2024-04-22T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
        {
          id: '2024-04-22T11:00',
          startAt: '2024-04-22T11:00',
          endAt: '2024-04-22T15:00',
          timeStartAt: '11:00',
          timeEndAt: '15:00',
        },
        {
          id: '2024-04-22T15:00',
          startAt: '2024-04-22T15:00',
          endAt: '2024-04-22T20:00',
          timeStartAt: '15:00',
          timeEndAt: '20:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-23T07:00',
      items: [
        {
          id: '2024-04-23T07:00',
          startAt: '2024-04-23T07:00',
          endAt: '2024-04-23T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
        {
          id: '2024-04-23T11:00',
          startAt: '2024-04-23T11:00',
          endAt: '2024-04-23T15:00',
          timeStartAt: '11:00',
          timeEndAt: '15:00',
        },
        {
          id: '2024-04-23T15:00',
          startAt: '2024-04-23T15:00',
          endAt: '2024-04-23T20:00',
          timeStartAt: '15:00',
          timeEndAt: '20:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-24T07:00',
      items: [
        {
          id: '2024-04-24T07:00',
          startAt: '2024-04-24T07:00',
          endAt: '2024-04-24T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
        {
          id: '2024-04-24T11:00',
          startAt: '2024-04-24T11:00',
          endAt: '2024-04-24T15:00',
          timeStartAt: '11:00',
          timeEndAt: '15:00',
        },
        {
          id: '2024-04-24T15:00',
          startAt: '2024-04-24T15:00',
          endAt: '2024-04-24T20:00',
          timeStartAt: '15:00',
          timeEndAt: '20:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-25T07:00',
      items: [
        {
          id: '2024-04-25T07:00',
          startAt: '2024-04-25T07:00',
          endAt: '2024-04-25T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
        {
          id: '2024-04-25T11:00',
          startAt: '2024-04-25T11:00',
          endAt: '2024-04-25T15:00',
          timeStartAt: '11:00',
          timeEndAt: '15:00',
        },
        {
          id: '2024-04-25T15:00',
          startAt: '2024-04-25T15:00',
          endAt: '2024-04-25T20:00',
          timeStartAt: '15:00',
          timeEndAt: '20:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-26T07:00',
      items: [
        {
          id: '2024-04-26T07:00',
          startAt: '2024-04-26T07:00',
          endAt: '2024-04-26T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
        {
          id: '2024-04-26T11:00',
          startAt: '2024-04-26T11:00',
          endAt: '2024-04-26T15:00',
          timeStartAt: '11:00',
          timeEndAt: '15:00',
        },
        {
          id: '2024-04-26T15:00',
          startAt: '2024-04-26T15:00',
          endAt: '2024-04-26T20:00',
          timeStartAt: '15:00',
          timeEndAt: '20:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-27T08:00',
      items: [
        {
          id: '2024-04-27T08:00',
          startAt: '2024-04-27T08:00',
          endAt: '2024-04-27T12:00',
          timeStartAt: '08:00',
          timeEndAt: '12:00',
        },
        {
          id: '2024-04-27T12:00',
          startAt: '2024-04-27T12:00',
          endAt: '2024-04-27T16:00',
          timeStartAt: '12:00',
          timeEndAt: '16:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-28T08:00',
      items: [
        {
          id: '2024-04-28T08:00',
          startAt: '2024-04-28T08:00',
          endAt: '2024-04-28T12:00',
          timeStartAt: '08:00',
          timeEndAt: '12:00',
        },
        {
          id: '2024-04-28T12:00',
          startAt: '2024-04-28T12:00',
          endAt: '2024-04-28T16:00',
          timeStartAt: '12:00',
          timeEndAt: '16:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-29T07:00',
      items: [
        {
          id: '2024-04-29T07:00',
          startAt: '2024-04-29T07:00',
          endAt: '2024-04-29T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
        {
          id: '2024-04-29T11:00',
          startAt: '2024-04-29T11:00',
          endAt: '2024-04-29T15:00',
          timeStartAt: '11:00',
          timeEndAt: '15:00',
        },
        {
          id: '2024-04-29T15:00',
          startAt: '2024-04-29T15:00',
          endAt: '2024-04-29T20:00',
          timeStartAt: '15:00',
          timeEndAt: '20:00',
        },
      ],
    },
    {
      isDisabled: false,
      date: '2024-04-30T07:00',
      items: [
        {
          id: '2024-04-30T07:00',
          startAt: '2024-04-30T07:00',
          endAt: '2024-04-30T11:00',
          timeStartAt: '07:00',
          timeEndAt: '11:00',
        },
        {
          id: '2024-04-30T11:00',
          startAt: '2024-04-30T11:00',
          endAt: '2024-04-30T15:00',
          timeStartAt: '11:00',
          timeEndAt: '15:00',
        },
        {
          id: '2024-04-30T15:00',
          startAt: '2024-04-30T15:00',
          endAt: '2024-04-30T20:00',
          timeStartAt: '15:00',
          timeEndAt: '20:00',
        },
      ],
    },
  ],
  onSelect: () => {},
  timeZone: undefined,
  value: '2024-04-17T15:00',
  renderEmptyState: null,
};
TimeSlotPickerStory.argTypes = {};

export default meta;
