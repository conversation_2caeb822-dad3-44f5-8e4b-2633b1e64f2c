import { render } from '@testing-library/react';

import ComponentGrid from './ComponentGrid';

describe('ComponentGrid component', () => {
  it('renders children correctly', () => {
    const { getByTestId } = render(
      <ComponentGrid>
        <div data-testid="child">Child Component</div>
      </ComponentGrid>,
    );

    expect(getByTestId('child')).toBeInTheDocument();
    expect(getByTestId('child').textContent).toBe('Child Component');
  });
});
