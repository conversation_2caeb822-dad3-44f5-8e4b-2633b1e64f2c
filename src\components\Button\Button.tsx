import type { ElementType, ForwardedRef } from 'react';
import { forwardRef } from 'react';
import AnimatedLoaderIcon from '@components/AnimatedLoader';
import { BUTTON_TEST_ID } from '@constants/dataTestId';
import classNames from '@utils/classNames';

import themeStyles from '../../styles/themes.module.css';
import styles from './Button.module.css';
import { ButtonProps } from './Button.types';

const Button = forwardRef(
  (
    {
      label = '',
      left = null,
      right = null,
      size = 'large',
      theme = 'primary',
      type = 'button',
      isFullWidth = false,
      isLoading = false,
      isDisabled = false,
      isSquare = false,
      onClick = () => {},
      id = null,
      className,
      tabIndex = 0,
      ariaLabel = null,
      dataId = null,
      suppressHydrationWarning = false,
      onMouseDown = () => {},
      onMouseUp = () => {},
      onTouchStart = () => {},
      onTouchEnd = () => {},
      component = null,
      children,
      ...props
    }: ButtonProps,
    ref: ForwardedRef<HTMLButtonElement>,
  ) => {
    let Tag: ElementType = 'button';
    const target = null;
    const rel = null;
    if (component) {
      Tag = component;
    } else {
      Tag = 'button';
    }

    const animatedLoader = isLoading && (
      <AnimatedLoaderIcon size={size === 'small' ? '16px' : '20px'} />
    );

    const renderIconButton = (
      <Tag
        id={id}
        className={classNames(
          themeStyles[`${theme}Theme`],
          theme.includes('Inverted') && styles.disabledInverted,
          styles.iconButton,
          styles[`${size}SizeIcon`],
          isLoading && styles.fetching,
          className,
        )}
        type={type}
        disabled={isDisabled}
        onClick={onClick}
        ref={ref}
        data-id={dataId}
        data-testid={`${id}-${BUTTON_TEST_ID}`}
      >
        <>
          <span className={styles.childrenWrapper}>{children}</span>
          {isLoading ? (
            <span className={styles.fetchingIconWrapper}>{animatedLoader}</span>
          ) : null}
        </>
      </Tag>
    );

    if (!label) {
      return renderIconButton;
    }

    const buttonElement = (
      <Tag
        ref={ref}
        suppressHydrationWarning={suppressHydrationWarning}
        className={classNames(
          themeStyles[`${theme}Theme`],
          theme.includes('Inverted') && styles.disabledInverted,
          styles.button,
          styles[`${size}Size`],
          !!left && styles.hasLeft,
          !!right && styles.hasRight,
          isFullWidth && styles.fullWidth,
          isLoading && styles.loading,
          isSquare && styles.square,
          className,
        )}
        type={type}
        disabled={isDisabled}
        onClick={onClick}
        target={target}
        rel={rel}
        id={id}
        data-id={dataId}
        tabIndex={tabIndex}
        aria-label={ariaLabel}
        onMouseDown={onMouseDown}
        onMouseUp={onMouseUp}
        onTouchStart={onTouchStart}
        onTouchEnd={onTouchEnd}
        data-testid={`${id}-${BUTTON_TEST_ID}`}
        {...props}
      >
        {left && <span className={styles.leftWrapper}>{left}</span>}
        {label && <span className={styles.label}>{label}</span>}
        <span className={styles.fetchingIconWrapper}>{animatedLoader}</span>

        {right && (
          <span
            className={classNames(styles.rightWrapper, styles.rightWithLabel)}
          >
            {right}
          </span>
        )}
      </Tag>
    );

    return buttonElement;
  },
);

export default Button;
