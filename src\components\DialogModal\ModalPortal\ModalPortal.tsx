// Import dynamically without <PERSON>, as this is expected to be rendered clientside only
// e.g.  const ModalPortal = dynamic(() => import('~/components/ModalPortal'), { ssr: false });
//
// Expects an empty div with id of `modal-portal-root` somewhere in the DOM

import { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { GENERAL_MODAL_PORTAL_ID } from '@constants/components';
import useKeyDown from '@hooks/useKeyDown';
import classNames from '@utils/classNames';

import styles from './ModalPortal.module.css';
import { ModalPortalProps } from './ModalPortal.types';

const ModalPortal = ({
  children,
  onClose,
  className,
  overlayClassName,
  overlayRef,
  portalId = GENERAL_MODAL_PORTAL_ID,
}: ModalPortalProps) => {
  const $modalPortalRoot = useRef<HTMLElement>();
  const $el = useRef<HTMLDivElement>(document.createElement('div'));

  useEffect(() => {
    const $childEl = $el.current;
    $modalPortalRoot.current = document.getElementById(portalId);
    $modalPortalRoot.current.appendChild($childEl);

    return () => {
      if ($modalPortalRoot.current.contains($childEl)) {
        $modalPortalRoot.current.removeChild($childEl);
      }
    };
  }, [portalId]);

  useKeyDown('Escape', onClose);

  const modalElement = (
    <div role="dialog" className={classNames(styles.modalPortal, className)}>
      {onClose && (
        // we already handle this above with useKeyDown
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events
        <div
          ref={overlayRef}
          role="presentation"
          className={classNames(styles.overlay, overlayClassName)}
          onClick={onClose}
        />
      )}

      {children}
    </div>
  );

  return createPortal(children && modalElement, $el.current);
};

export default ModalPortal;
