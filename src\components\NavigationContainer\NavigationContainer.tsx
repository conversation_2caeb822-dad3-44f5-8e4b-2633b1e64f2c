import type { MutableRefObject } from 'react';
import React, { forwardRef, useEffect, useRef } from 'react';
import NavigationBar, {
  NavigationBarProps,
} from '@components/NavigationBar/NavigationBar';
import useMediaQueryState from '@hooks/useMediaQueryState';
import useResizeObserver from '@hooks/useResizeObserver';
import useWindowScroll, {
  SCROLL_DIRECTION_DOWN,
  SCROLL_DIRECTION_UP,
} from '@hooks/useWindowScroll';
import useWindowSize from '@hooks/useWindowSize';
import classNames from '@utils/classNames';
import variableStyles from '@styles/variables.module.css';

import styles from './NavigationContainer.module.css';

export interface NavigationContainerProps {
  environment?: string;
  isAdminFrameShown?: boolean;
  isNavigationHidden?: boolean;
  isAdminImpersonating?: boolean; // eslint-disable-line react/no-unused-prop-types
  isStickOnTop?: boolean;
  navigationProps?: NavigationBarProps;
  onNavigationHeightChange?: ({ height }: { height: number }) => void;
  isAdminPanelActive?: boolean;
  backgroundColor?: string;
  setIsMobileChromeCollapsed?: (isCollapsed: boolean) => void;
}

const NavigationContainer = forwardRef<
  HTMLDivElement,
  NavigationContainerProps
>(
  (
    {
      isAdminFrameShown = false,
      environment,
      isNavigationHidden = false,
      onNavigationHeightChange = () => {},
      isStickOnTop,
      navigationProps,
      isAdminPanelActive,
      backgroundColor,
      setIsMobileChromeCollapsed = () => {},
    },
    ref: MutableRefObject<HTMLDivElement>,
  ) => {
    const { scrollDirection, windowScrollTop, windowScrollBottom } =
      useWindowScroll();

    const { height: windowHeight } = useWindowSize();

    const scrollBottomThreshold = windowHeight * 0.1;

    const localRef = useRef<HTMLDivElement>(null);
    const refToUse = ref ?? localRef;
    const isMenuClosedRef = useRef(true);
    const navHeightWhenMenuClosedRef = useRef(0);

    const isSmallWidth = useMediaQueryState({
      query: `(max-width: ${variableStyles.smallWidth})`,
    });

    const navSize = useResizeObserver(refToUse);
    const navHeight = navSize?.contentRect?.height ?? (isSmallWidth ? 88 : 104);
    // We only want to update this value if the menu is closed.  If we ever deep link into a scenario where the menu is open, we may need to update this logic.
    navHeightWhenMenuClosedRef.current = isMenuClosedRef.current
      ? navHeight
      : navHeightWhenMenuClosedRef.current;

    const refOffsetHeight = refToUse?.current?.offsetHeight || 0;
    const isNavigationSticky =
      scrollDirection === SCROLL_DIRECTION_UP &&
      windowScrollTop > refOffsetHeight;

    // The bottom threshold logic handles the scroll bounce on mobile devices
    const isNavigationHiddenLocal =
      (scrollDirection === SCROLL_DIRECTION_DOWN &&
        windowScrollTop !== 0 &&
        windowScrollTop > refOffsetHeight - 50) ||
      (scrollDirection === SCROLL_DIRECTION_UP &&
        windowScrollBottom < scrollBottomThreshold &&
        windowScrollTop > refOffsetHeight - 50);

    // We can only externally force the navigation to be hidden.
    const isNavigationHiddenToUse =
      isNavigationHiddenLocal || isNavigationHidden;

    useEffect(() => {
      setIsMobileChromeCollapsed(
        isSmallWidth ? isNavigationHiddenLocal : false,
      );
    }, [isNavigationHiddenLocal, isSmallWidth, setIsMobileChromeCollapsed]);

    return (
      <div
        ref={refToUse}
        className={classNames(
          styles.navigationWrapper,
          isNavigationHiddenToUse && styles.isNavigationHidden,
          isNavigationSticky && styles.isNavigationSticky,
          isAdminFrameShown && styles.isAdminFrameShown,
          !isAdminFrameShown && !!environment && styles.environment,
        )}
      >
        <NavigationBar
          isNavigationSticky={isNavigationSticky}
          isStickOnTop={isStickOnTop}
          onNavigationHeightChange={onNavigationHeightChange}
          backgroundColor={backgroundColor}
          isAdminPanelActive={isAdminPanelActive}
          {...navigationProps}
        />
      </div>
    );
  },
);

export default NavigationContainer;
