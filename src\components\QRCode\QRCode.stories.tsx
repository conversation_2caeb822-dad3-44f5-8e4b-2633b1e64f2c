import { useRef, useState } from 'react';
import { FileExtension } from 'qr-code-styling';
import Button from '@components/Button';
import Select from '@components/Select';
import TextInput from '@components/TextInput';
import variableStyles from '@styles/variables.module.css';

import QRCode from './QRCode';

export default {
  title: 'Components/Utility',
};

export const QRCodeStory = () => {
  const [url, setUrl] = useState('https://peddle.com/');
  const [fileExt, setFileExt] = useState<FileExtension>('svg');
  const [size, setSize] = useState('300');
  const buttonRef = useRef(null);
  const onUrlChange = event => {
    event.preventDefault();
    setUrl(event.target.value);
  };

  const onExtensionChange = value => {
    setFileExt(value);
  };

  return (
    <div>
      <div
        style={{
          padding: variableStyles.fiveSpace,
        }}
      >
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%',
            marginTop: '10px',
            marginBottom: '10px',
            gap: '10px',
          }}
        >
          <div
            style={{
              width: '25%',
              boxSizing: 'border-box',
            }}
          >
            <TextInput
              label="Url"
              onChange={onUrlChange}
              placeholder="Url"
              // typebuttonRef="text"
              value={url}
            />
          </div>
          <div
            style={{
              width: '25%',
              boxSizing: 'border-box',
            }}
          >
            <Select
              items={[
                { value: '100', label: '100' },
                { value: '200', label: '200' },
                { value: '300', label: '300' },
                { value: '400', label: '400' },
              ]}
              label="Size"
              onChange={value => setSize(value)}
              value={size}
              isFixedMenu
            />
          </div>
          <div
            style={{
              width: '25%',
              boxSizing: 'border-box',
            }}
          >
            <Select
              items={[
                { value: 'svg', label: 'svg' },
                { value: 'png', label: 'png' },
                { value: 'jpeg', label: 'jpeg' },
              ]}
              label="Extension"
              onChange={onExtensionChange}
              value={fileExt}
              isFixedMenu
            />
          </div>

          <div
            style={{
              width: '25%',
              boxSizing: 'border-box',
            }}
          >
            <Button
              label="Download"
              size="large"
              tabIndex={0}
              theme="primary"
              type="button"
              ref={buttonRef}
              isDisabled={!url}
            />
          </div>
        </div>

        <QRCode url={url} size={size} fileExt={fileExt} ref={buttonRef} />
      </div>
    </div>
  );
};

QRCodeStory.storyName = 'QRCode';
