@value variables: "../../styles/variables.module.css";
@value typography: "../../styles/typography.module.css";
@value black, black10, black70, white, white10, white35, black35, primary, warning, smallWidth, neutralDarkest from variables;
@value oneSpace, twoSpace, threeSpace, fourSpace, fiveSpace, eightSpace, tenSpace, bodyBaseFontSize, h4FontSize, buttonLabelLargeFontSize from variables;

@value mediumWidth, swingTo from variables;
@value h4 from typography;

.list {
  list-style-type: none;
  display: flex;
  flex-direction: column;
}

.button {
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  transition: all 0.5s ease-out-expo;

  &:hover,
  &:focus {
    outline: none;
    color: primary;
  }
}

.customIconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  position: relative;

  & .customIcon {
    margin: threeSpace;

    @media (min-width: mediumWidth) {
      margin: fourSpace;
    }
  }

  & .verticalLine {
    background-color: white;
    width: 1px;
    height: threeSpace;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    @media (min-width: mediumWidth) {
      height: fiveSpace;
    }
  }

  & .horizontalLine {
    background-color: white;
    height: 1px;
    width: threeSpace;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    @media (min-width: mediumWidth) {
      width: fiveSpace;
    }
  }
}

.listItem {
  display: flex;
  flex-direction: column;
  transition: all 0.5s ease-out-expo;

  &:focus {
    outline: none;
  }

  &:focus-visible {
    box-shadow:
      0px 0px 0px 2px black,
      0px 0px 0px 4px white;
  }

  &:not(:last-of-type) {
    border-bottom: 1px solid;

    & .contentWrapper {
      padding-bottom: twoSpace;
    }
  }

  &.isBorderless {
    border-bottom: none;

    & .contentWrapper {
      padding-bottom: twoSpace;
    }
  }

  &.isOpen {
    & .icon {
      transform: rotate(180deg);
    }

    & .contentWrapper {
      display: block;
    }

    & .horizontalLine {
      background-color: black;
    }
  }

  &.isDisabled {
    cursor: not-allowed;

    & .button {
      pointer-events: none;
    }
  }

  &.primaryMain,
  &.primaryInverted {
    & .listItemLabel {
      font-size: h4FontSize;
      font-weight: 800;
      margin: threeSpace 0px threeSpace oneSpace;
    }
  }

  &.secondary,
  &.secondaryInverted {
    & .listItemLabel {
      font-size: buttonLabelLargeFontSize;
      font-weight: 700;
      margin: twoSpace 0px twoSpace oneSpace;
    }
  }

  &.primaryMain,
  &.secondary {
    border-bottom-color: black10;

    & .button {
      color: black70;

      &:hover,
      &:focus {
        outline: none;
        color: black;
      }
    }

    &.isDisabled {
      & .button {
        color: black35;
      }
    }

    &.isOpen {
      & .listItemLabel {
        color: black;
      }
    }
  }

  &.primaryInverted,
  &.secondaryInverted {
    border-bottom-color: white10;

    & .button {
      color: white;

      &:hover,
      &:focus {
        outline: none;
        color: warning;
      }
    }

    &.isDisabled {
      & .button {
        color: white35;
      }
    }

    &.isOpen {
      & .listItemLabel {
        color: warning;
      }
    }
  }

  &.displayV1,
  &.displayV1Inverted {
    border: 1px solid;
    border-radius: 20px;

    & .button {
      padding: twoSpace;
    }

    &.isOpen {
      & .button {
        padding-bottom: 0;
      }
    }

    & .contentWrapper {
      font-size: bodyBaseFontSize;
      margin-top: twoSpace;
      text-align: left;
      box-sizing: content-box;
      padding-left: twoSpace;
      padding-right: twoSpace;
      padding-bottom: twoSpace;
    }

    @media (min-width: mediumWidth) {
      & .button {
        padding-top: fourSpace;
        padding-bottom: fourSpace;
        padding-left: fiveSpace;
        padding-right: fiveSpace;
      }

      & .contentWrapper {
        padding-left: fiveSpace;
        padding-right: fiveSpace;
        padding-bottom: fiveSpace;
      }
    }

    &:not(:last-of-type) {
      margin-bottom: threeSpace;
    }

    @media (min-width: mediumWidth) {
      &:not(:last-of-type) {
        margin-bottom: fiveSpace;
      }
    }

    @media (max-width: smallWidth) {
      & svg {
        height: 20px;
        width: 20px;
      }
    }

    & .listItemLabel {
      font-size: h4FontSize;
      font-weight: 800;
      line-height: 1.33;
      letter-spacing: -0.03em;

      @media (max-width: smallWidth) {
        line-height: 1.2;
        letter-spacing: -0.02em;
      }
      text-align: left;
    }

    & .icon {
      border: 1px solid white;
      background-color: white;
      border-radius: 50%;
      padding: oneSpace;
      margin-right: 0;
      margin-left: threeSpace;
      & svg {
        stroke-width: 1px;
      }
    }
  }

  &.displayV1Inverted {
    &.isOpen {
      & .icon {
        border: 1px solid white;
        background-color: warning;
      }
    }
  }

  &.display,
  &.displayInverted {
    border: 1px solid;
    border-radius: 20px;

    & .button {
      padding: fourSpace;
    }

    &.isOpen {
      & .button {
        padding-bottom: 0;
      }
    }

    & .contentWrapper {
      font-size: bodyBaseFontSize;
      margin-top: twoSpace;
      text-align: left;
      box-sizing: content-box;
      padding-left: fourSpace;
      padding-right: fourSpace;
      padding-bottom: fourSpace;
    }

    @media (min-width: mediumWidth) {
      & .button {
        padding-top: tenSpace;
        padding-bottom: tenSpace;
        padding-left: eightSpace;
        padding-right: eightSpace;
      }

      & .contentWrapper {
        padding-left: eightSpace;
        padding-right: eightSpace;
        padding-bottom: eightSpace;
      }
    }

    &:not(:last-of-type) {
      margin-bottom: threeSpace;
    }

    @media (min-width: mediumWidth) {
      &:not(:last-of-type) {
        margin-bottom: fiveSpace;
      }
    }

    & .listItemLabel {
      font-size: h4FontSize;
      font-weight: 800;
      line-height: 1.33;
      letter-spacing: -0.03em;

      @media (max-width: smallWidth) {
        line-height: 1.2;
        letter-spacing: -0.02em;
      }
      text-align: left;
    }

    & .icon {
      border: 1px solid black;
      border-radius: 50%;
      padding: twoSpace;
      margin-right: 0;
      margin-left: threeSpace;
      & svg {
        stroke-width: 1px;
      }
    }
  }

  &.displayV1Inverted {
    border-color: white10;

    & .button {
      color: white;
    }

    & .icon {
      &.isOpen {
        border-color: black10 !important;
        color: black;
      }
    }

    &.isOpen {
      background-color: white;
      color: black;

      & .listItemLabel {
        color: black;
      }
    }

    &:hover,
    &:focus {
      border-color: white;

      & .icon {
        border-color: white;
      }
    }
  }

  &.displayInverted {
    border-color: white10;

    & .button {
      color: white;
    }

    & .icon {
      color: white;

      &.isOpen {
        border-color: black10 !important;
        color: black;
      }
    }

    &.isOpen {
      background-color: white;
      color: black;

      & .listItemLabel {
        color: black;
      }
    }

    &:hover,
    &:focus {
      border-color: white;

      & .icon {
        border-color: white;
      }
    }
  }

  &.display {
    border-color: black10;

    & .button {
      color: black70;
    }

    & .icon {
      color: black70;
      border-color: black10;
    }

    & .horizontalLine {
      background-color: black70;
    }

    & .verticalLine {
      background-color: black70;
    }

    &:hover,
    &:focus,
    &.isOpen {
      border-color: black;

      & .icon {
        border-color: black;
      }

      & .horizontalLine {
        background-color: black;
      }

      & .verticalLine {
        background-color: black;
      }
    }
  }

  &.displayV1 {
    border-color: black10;

    & .button {
      color: black70;
    }

    & .icon {
      color: black70;
      border-color: black10;
    }

    & .horizontalLine {
      background-color: black70;
    }

    & .verticalLine {
      background-color: black70;
    }

    &:hover,
    &:focus,
    &.isOpen {
      border-color: black;

      & .icon {
        border-color: black;
      }

      & .horizontalLine {
        background-color: black;
      }

      & .verticalLine {
        background-color: black;
      }
    }
  }
}

.iconWrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.listItemLabel {
  display: flex;
}

.icon {
  line-height: 0;
  margin-right: oneSpace;
  transition: swingTo 500ms;
  font-size: inherit;
}

.contentWrapper {
  display: none;
}

.scrollableBody {
  position: relative;
  max-height: 60vh;
  overflow: auto;
}

.bottomClassName {
  @media (max-width: smallWidth) {
    border-bottom-left-radius: 20px !important;
  }
}

.contentWrapperRoot {
  position: relative;
}
