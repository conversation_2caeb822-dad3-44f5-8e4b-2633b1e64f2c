import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import { BUTTON_TEST_ID } from '@constants/dataTestId';

import TableText from './TableText';

describe('TableText Component', () => {
  test('renders default text correctly', () => {
    const { getByText } = render(
      <TableText label="Hello World" variant="text" />,
    );
    expect(getByText('Hello World')).toBeInTheDocument();
  });

  test('renders active text correctly', () => {
    const { getByText } = render(
      <TableText label="Active" isActive variant="text" />,
    );
    expect(getByText('Active')).toHaveClass('active');
  });

  test('click event works for copyClipboard variant', () => {
    const onClickMock = jest.fn();
    const { getByTestId } = render(
      <TableText label="Copy" variant="copyClipboard" onClick={onClickMock} />,
    );

    fireEvent.click(getByTestId(`copyClipboard-${BUTTON_TEST_ID}`));
    expect(onClickMock).toHaveBeenCalled();
  });

  test('click event works for masked variant', () => {
    const onClickMock = jest.fn();
    const { getByTestId } = render(
      <TableText
        label="Sensitive Data"
        variant="masked"
        onClick={onClickMock}
      />,
    );

    fireEvent.click(getByTestId(`masked-${BUTTON_TEST_ID}`));
    expect(onClickMock).toHaveBeenCalled();
  });

  test('renders icon correctly', () => {
    const { getByText } = render(
      <TableText
        variant="icon"
        icons={[
          {
            id: 'test',
            icon: 'icon',
          },
        ]}
      />,
    );
    expect(getByText('icon')).toBeInTheDocument();
  });

  test('renders statusIndicator correctly', () => {
    const { getByText } = render(
      <TableText
        label="Assigned"
        variant="statusIndicator"
        indicatorTheme="warning"
      />,
    );
    expect(getByText('Assigned')).toBeInTheDocument();
  });
});
