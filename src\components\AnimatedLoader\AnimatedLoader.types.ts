export type SimpleTheme = 'dark' | 'light';

export interface AnimatedLoaderProps {
  /**
   * The color of the loader icon. Accepts any valid CSS color value.
   */
  color?: string;

  /**
   * The theme of the loader icon. Determines the visual appearance of the icon.
   */
  theme?: SimpleTheme;

  /**
   * The size of the loader icon. Can be specified as a number representing pixels or a CSS string representing size (e.g., '2em', '50%').
   */
  size?: number | string;

  /**
   * Determines whether the loader icon is disabled. If set to `true`, the icon will appear in a disabled state.
   */
  isDisabled?: boolean;
}
