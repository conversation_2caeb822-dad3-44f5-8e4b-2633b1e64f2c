@value typography: "../../styles/typography.module.css";
@value variables: "../../styles/variables.module.css";
@value utility: "../../styles/utility.module.css";
@value bodyLarge, bodySmall,bodyBaseStrong from typography;
@value threeSpace, oneSpace, twoSpace, white, white70, black, black5, black10, black35, black50, black70, warning, neutralLight from variables;
@value smallWidth, mediumWidth, largeWidth,xLargeWidth,ease-out-expo, belowZIndex, buoyantZIndex, topZIndex from variables;
@value responsivePageGutters from utility;

.nav {
  position: relative;

  @media (max-width: smallWidth) {
    border-bottom-left-radius: threeSpace;
    border-bottom-right-radius: threeSpace;
  }
}

.isSticky {
  position: fixed;
  top: 0;
  width: 100%;
}

.hasFlatLayout {
  @media (max-width: smallWidth) {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
  }
}

.isAdminPanelActive {
  @media (max-width: largeWidth) {
    top: var(--navigationHeightWhenHidden);
  }
}

.isNavigationSticky {
  color: transparent !important;
}

.overlay {
  @media (max-width: smallWidth) {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: belowZIndex;
    background-color: black35;
    opacity: 0;
    pointer-events: none;
  }

  &.isMenuOpen {
    @media (max-width: smallWidth) {
      opacity: 1;
      pointer-events: auto;
    }
  }
}

.barWrapper {
  composes: responsivePageGutters;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-top: threeSpace;
  padding-bottom: threeSpace;
  z-index: buoyantZIndex;
  position: relative;
  transition:
    background-color 1s ease-out-expo,
    transform 1s ease-out-expo;

  @media (max-width: smallWidth) {
    /* Add important here to overwrite responsivePageGutters padding */
    padding: twoSpace oneSpace !important;
  }

  &.isMenuOpen {
    border: 0;
    border-radius: 0;
    transition: none;

    @media (max-width: smallWidth) {
      background-color: white;
    }

    &.isDesktop {
      background-color: transparent;
    }
  }

  &.isInverted {
    &:not(.isNavSticky) {
      color: white;
    }

    @media (max-width: mediumWidth) {
      &.isMenuOpen {
        color: black;
      }
    }
  }

  &.isNavSticky {
    background-color: white;
    color: black;
  }

  &.isNavHidden {
    transform: translateY(-200%);
    transition: background-color unset;
  }
}

.menuListItem {
  color: black;
  font-weight: 600;
}

.navBarLeft {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.logo {
  margin-left: twoSpace;
  display: flex;
  width: 75px;

  @media (min-width: mediumWidth) {
    width: 90px;
  }
}

.navBarRight,
.sellerNavbarRight {
  & button {
    margin-left: oneSpace;
  }

  display: flex;

  @media (max-width: mediumWidth) {
    display: none;
    & .authenticationButton,
    & .isHidden {
      display: none;
    }
    & .adminAuthenticationButton {
      display: flex;
    }
    & .callButton {
      margin-right: oneSpace;
    }
  }

  & > .disabledProfile {
    color: white70;
    pointer-events: none;
  }
}

.mobileNavRight {
  display: flex;
  @media (min-width: mediumWidth) {
    display: none;
  }
}

.leftNavMenu {
  display: block;

  @media (min-width: xLargeWidth) {
    display: none;
  }
}

.leftNavigation {
  display: none;
  margin-left: 32px;

  @media (min-width: xLargeWidth) {
    display: block;
  }
}

.sellerNavbarRight {
  @media (max-width: smallWidth) {
    justify-content: space-between;
    align-items: center;
    width: 95px;
  }
}
.sellerNavbarAdminRight {
  @media (max-width: smallWidth) {
    width: auto;
  }
}
.marginRight {
  margin-right: 10px;
}
.isHideCarButton {
  display: none !important;

  @media (max-width: smallWidth) {
    display: flex !important;
  }
}
.menuFooterContainer {
  padding-top: threeSpace;
}

.menu {
  width: 100%;
  z-index: topZIndex;
  background: white;
  color: black;
  display: flex;
  flex-flow: column;
  overflow-y: auto;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;

  &.isDesktop {
    position: absolute;
    left: 5%;
    top: 110px;
    width: 320px;
    border-radius: 16px;
    box-shadow: 4px 4px 1px black5;
    border: 1px solid neutralLight;

    @media (max-width: smallWidth) {
      position: absolute;
      left: 0;
      top: unset;
      width: 100%;
      border-radius: 0;
      border-bottom-left-radius: 16px;
      border-bottom-right-radius: 16px;
      padding: 0;
    }
  }
}

.navigationRight {
  right: 5% !important;
  left: unset !important;
}

.primaryLinksWrapper {
  width: 100%;
  list-style: none;
  composes: bodyLarge;
  font-weight: 500;

  &:not(:first-child) {
    margin-top: oneSpace;
  }

  & a {
    color: black50;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    & svg {
      visibility: hidden;
    }

    &:hover {
      color: black;
      font-weight: 600;

      & svg {
        visibility: visible;
      }
    }
  }
}

.primaryListLink {
  composes: bodyBaseStrong;

  &:not(:last-child) {
    padding-bottom: twoSpace;
  }
}

.supportContainer {
  composes: bodySmall;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-left: oneSpace;
  padding-right: oneSpace;
  margin-bottom: threeSpace;

  & .supportLabel {
    color: black70;
  }

  & .supportNumber {
    font-weight: 600;
    line-height: inherit;
  }
}

.menuButtonContainer {
  display: flex;
  flex-direction: column;
  border-top: 1px solid black10;

  & > :not(:first-child) {
    margin-top: twoSpace;
  }
}

.agentViewCarButton {
  @media (max-width: smallWidth) {
    margin-left: oneSpace;
  }
}

.normalCarButton {
  @media (max-width: smallWidth) {
    margin-left: oneSpace;
  }
}

.leftNavigationBtn {
  padding: 0 twoSpace !important;
}

.buttonContainer {
  display: inline-block;
  position: relative;
}
