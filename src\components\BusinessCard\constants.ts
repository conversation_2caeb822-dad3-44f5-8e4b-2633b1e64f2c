import { StyleSheet } from '@react-pdf/renderer';
import { BusinessCardFormData } from './BusinessCard.types';

export const INITIAL_FORM_DATA: BusinessCardFormData = {
    firstName: '',
    lastName: '',
    phoneNumber: '',
    email: '',
    organization: '',
    title: '',
};

// PDF Styles 
export const PDF_STYLES = StyleSheet.create({
    // Page container
    page: {
        flexDirection: 'column',
        backgroundColor: '#FFFFFF',
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },

    // Card dimensions - shared constants
    card: {
        width: 252,
        height: 144,
        position: 'relative',
    },

    // Front side styles
    frontSide: {
        width: 252,
        height: 144,
        position: 'relative',
        marginBottom: 20,
    },
    frontImage: {
        width: '100%',
        height: '100%',
        objectFit: 'cover',
    },

    // Back side styles
    backSide: {
        width: 252,
        height: 144,
        paddingHorizontal: 16,
        paddingVertical: 10,
        position: 'relative',
        backgroundColor: '#FFD566',
    },

    // Typography styles
    name: {
        fontSize: 40,
        fontFamily: 'Aventa',
        fontWeight: 900,
        marginBottom: -10,
        color: '#212121',
        lineHeight: 1.2,
    },
    titleText: {
        fontSize: 8,
        fontFamily: 'Aventa',
        fontWeight: 500,
        marginBottom: -2,
        marginTop: 10,
        color: '#212121',
    },
    organizationText: {
        fontSize: 8,
        fontFamily: 'Aventa',
        fontWeight: 500,
        marginBottom: 10,
        color: '#212121',
    },
    contactText: {
        fontSize: 8,
        fontFamily: 'Aventa',
        fontWeight: 500,
        color: '#212121',
        marginBottom: -1,
    },

    // QR code positioning
    qrCode: {
        position: 'absolute',
        bottom: 12,
        right: 10,
        width: 40,
        height: 40,
    },
});