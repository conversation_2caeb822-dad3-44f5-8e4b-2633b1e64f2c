name: Check conventional commits

on:
  push:
    branches-ignore: []

jobs:
  check-commit-message:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'
          registry-url: https://npm.pkg.github.com/
          cache: 'npm'

      - name: Install dependencies
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GIT_TOKEN }}

      - name: Validate current commit (last commit) with commitlint
        if: github.event_name == 'push'
        run: npx commitlint $(echo $(git log --pretty=%s | grep -iv "merge" -m 1) > temp.txt) -e temp.txt

      - name: Validate PR commits with commitlint
        if: github.event_name == 'pull_request'
        run: npx commitlint $(echo $(git log -1 --pretty=%s) > temp.txt) -e temp.txt --verbose
