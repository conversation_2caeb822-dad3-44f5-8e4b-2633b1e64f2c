@value variables: "../../styles/variables.module.css";
@value oneSpace, twoSpace, threeSpace from variables;
@value h5FontSize, h6FontSize, black10, black50, black70, neutralDark, black5Opaque from variables;
@value smallWidth, mediumWidth from variables;
@value typography: "../../styles/typography.module.css";
@value h5Strong, overline, captionStrong from typography;

.carouselWrapper {
  width: 100%;
  border-radius: 8px;

  @media (max-width: smallWidth) {
    border: none;
  }
}

.container {
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  max-width: 100%;
  overflow-x: auto;
}

.slider {
  align-self: center;
  flex: 1;
}

.slide {
  text-align: center;
}

.buttonBack,
.buttonNext {
  &[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.back,
.next {
  padding: twoSpace;
  background-color: black5Opaque;
  border-radius: 32px;
  color: black70;
  line-height: 0;
  font-size: h5FontSize;

  @media (max-width: smallWidth) {
    font-size: h6FontSize;
    border-radius: 50%;
    background-color: black10;
    padding: oneSpace;
  }
}

.next {
  margin-right: twoSpace;
  margin-left: twoSpace;

  @media (max-width: mediumWidth) {
    margin-right: oneSpace;
    margin-left: oneSpace;
  }
}

.back {
  margin-left: twoSpace;
  margin-right: twoSpace;

  @media (max-width: mediumWidth) {
    margin-right: oneSpace;
    margin-left: oneSpace;
  }
}
.dotGroup {
  text-align: center;
}
.dotGroup button {
  height: oneSpace;
  width: oneSpace;
  margin: oneSpace;
  border-radius: 50%;
  background: black10;
  &[disabled] {
    background: black70;
  }
}

.footer {
  display: flex;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  padding-top: oneSpace;
}
