import classNames from '@utils/classNames';

import styles from './Radio.module.css';
import { RadioPropTypes } from './Radio.types';

/**
 * The Radio component provides a radio input field with customizable options.
 * It supports features such as labels, default checked state, custom CSS classes,
 * custom label styles, and onChange event handling.
 *
 * @param {Object} props - The props for the Radio component.
 * @param {string} props.id - Required. The ID for the radio input.
 * @param {string} props.label - Required. The label text for the radio input.
 * @param {boolean} [props.isChecked=false] - Optional. Specifies whether the radio input is checked.
 * @param {string} [props.className=''] - Optional. Additional CSS classes for styling purposes.
 * @param {string} [props.labelClassName=''] - Optional. Additional CSS classes for styling the label.
 * @param {boolean} [props.isInButtonShape=false] - Optional. Specifies whether the radio input should have a button-like appearance.
 * @param {number} [props.tabIndex] - Optional. Specifies the tab index of the radio input.
 * @param {Function} [props.onChange] - Optional. The onChange event handler for the radio input.
 * @returns {JSX.Element} - The rendered Radio component.
 */
const Radio = ({
  id,
  label = '',
  name,
  value,
  isChecked,
  isDisabled = false,
  tabIndex = null,
  onChange = null,
  className = null,
  isInButtonShape,
  dataTestId,
}: RadioPropTypes) => (
  <label
    className={classNames(
      styles.radio,
      isChecked && styles.checked,
      isDisabled && styles.disabled,
      !onChange && styles.disabledPointerEvents,
      isInButtonShape &&
        classNames(
          styles.radioButton,
          !isDisabled && isChecked && styles.checked,
        ),
      className,
    )}
    data-testid={dataTestId}
    htmlFor={id}
  >
    <span className={styles.indicator} />

    {label.length > 0 && <span className={styles.label}>{label}</span>}

    <input
      id={id}
      type="radio"
      name={name}
      value={value}
      checked={isChecked}
      disabled={isDisabled}
      tabIndex={tabIndex}
      onChange={onChange}
    />
  </label>
);

export default Radio;
