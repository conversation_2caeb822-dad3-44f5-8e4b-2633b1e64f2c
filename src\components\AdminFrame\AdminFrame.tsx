import React from 'react';
import Frame from '@components/Frame';

export interface AdminFrameProps {
  label?: string;
  right?: React.ReactNode;
  left?: React.ReactNode;
  isCollapsed?: boolean;
}

const AdminFrame = ({
  label = 'Editing',
  right = null,
  left = null,
  isCollapsed = false,
}: AdminFrameProps) => (
  <Frame
    label={label}
    isBorderShown
    right={right}
    left={left}
    isCollapsed={isCollapsed}
  />
);

export default AdminFrame;
