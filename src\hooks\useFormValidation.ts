import { useState, useCallback } from 'react';
import { validateField } from '../utils/validation';
import { INITIAL_FORM_DATA } from '../components/BusinessCard/constants';
import { BusinessCardFormData, BusinessCardFormErrors } from '../components/BusinessCard/BusinessCard.types';

interface UseFormValidationProps {
    initialData?: Partial<BusinessCardFormData>;
}

interface UseFormValidationReturn {
    formData: BusinessCardFormData;
    errors: BusinessCardFormErrors;
    handleInputChange: (
        name: keyof BusinessCardFormData,
        value: string,
    ) => void;
    validateForm: () => boolean;
    resetForm: () => void;
}

export const useFormValidation = ({
    initialData,
}: UseFormValidationProps = {}): UseFormValidationReturn => {
    const [formData, setFormData] = useState<BusinessCardFormData>({
        ...INITIAL_FORM_DATA,
        ...initialData,
    });
    const [errors, setErrors] = useState<BusinessCardFormErrors>({});

    const handleInputChange = useCallback(
        (name: keyof BusinessCardFormData, value: string) => {
            setFormData(prev => ({ ...prev, [name]: value }));
            if (errors[name]) {
                setErrors(prev => ({ ...prev, [name]: undefined }));
            }
        },
        [errors],
    );

    const validateForm = useCallback((): boolean => {
        const newErrors: BusinessCardFormErrors = {};
        let isValid = true;

        (Object.keys(formData) as Array<keyof BusinessCardFormData>).forEach(
            key => {
                const error = validateField(key, formData[key]);
                if (error) {
                    newErrors[key] = error;
                    isValid = false;
                }
            },
        );

        setErrors(newErrors);
        return isValid;
    }, [formData]);

    const resetForm = useCallback(() => {
        setFormData(INITIAL_FORM_DATA);
        setErrors({});
    }, []);

    return {
        formData,
        errors,
        handleInputChange,
        validateForm,
        resetForm,
    };
}; 