/* eslint-disable no-console */
import { useEffect, useState } from 'react';
// import {
//   CarUTurnIllustration,
//   MoreHorizontalIcon,
// } from '@peddleon/ped-ux-react-icons';
import type { Meta, StoryObj } from '@storybook/react';
import StatusIndicator from '@components/StatusIndicator';
import {
  TABLE_NO_RECORD_DEFAULT_BODY_TEXT,
  TABLE_NO_RECORD_DEFAULT_HEADER_TEXT,
} from '@constants/components';

import { Dropdown } from '..';
import Table from './Table';
import styles from './Table.module.css';
import TableText from './TableText';
/**
 * ## Overview
 *
 * This Storybook Meta object provides documentation and examples for the Table component.
 * It includes stories for rendering a basic table and a table with server-side pagination.
 *
 * To use the Table component in your project, follow these steps:
 *
 * 1. Import the Table component into your file:
 *    ```
 *    import { Table } from '@peddleon/ped-ux-react-library';
 *    ```
 *
 * 2. Define your data and column configurations.
 *
 * 3. Render the Table component with the appropriate props:
 *    ```
 *    <Table
 *      rowData={rowData}
 *      columns={columns}
 *      manualPagination={false}
 *      isInverted={false}
 *    />
 *    ```
 *
 * Props:
 * - `rowData`: Array of row data objects.
 * - `columns`: Array of column configurations for the table.
 * - `manualPagination`: Flag to indicate manual pagination.
 * - `isInverted`: Flag to indicate if the table is inverted.
 * - `footerConfig`: Array of footer configuration objects. Each object should have a `left` and `right` key.
 *
 * For server-side pagination:
 *
 * 1. Set the `manualPagination` prop to `true`.
 *
 * 2. Provide a `handlePagination` function to handle pagination changes.
 *
 * 3. Pass the `totalCount` and `paginationDetails` props to indicate the total count of items and pagination details respectively.
 *
 * Example usage for server-side pagination:
 * ```
 * <Table
 *   rowData={visibleRows}
 *   columns={columnsData}
 *   hasFirstColumnPinned
 *   handlePagination={handlePagination}
 *   manualPagination
 *   isInverted={false}
 *   totalCount={30}
 *   paginationDetails={paginationDetails}
 * />
 * ```
 *
 *  For row selection:
 *
 * 1. Set the `hasRowSelection` prop to `true`.
 *
 * 2. Provide a `setRowSelection`  function and `rowSelection` props to it.
 *
 * Example usage for row selection:
 * ```
 * <Table
 *   rowData={tableData}
 *   columns={tableColumns}
 *   hasRowSelection={true}
 *   setRowSelection={setRowSelection}
 *   rowSelection={selectedRows}
 * />
 * ```
 *
 * Do use the `TableText` Component for columns
 *
 * ## Note
 *
 * The `Table` component now supports statistics related changes. You can use the `isUsedForStatistics` prop in the column configuration to indicate if a column should be used for statistics. Additionally, you can provide a `grandTotal` prop to display the grand total value in the footer.
 */

const meta: Meta<typeof Table> = {
  component: Table,
  title: 'Components/Table',
  tags: ['autodocs'],
};

const generateDescription = () => {
  const descriptions = [
    'Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
    'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
    'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
    'Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam.',
    'Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.',
  ];
  return descriptions[Math.floor(Math.random() * descriptions.length)];
};

// Function to generate random clientID
const generateClientID = () => `${Math.floor(Math.random() * 900000) + 100000}`;

// Function to generate random secret
const generateSecret = () => {
  const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let secret = '';
  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < 6; i++) {
    secret += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return secret;
};

// Function to generate random data
const generateData = () => {
  const environments = ['Staging', 'Testing', 'Production'];
  const statuses = ['Active', 'Inactive'];
  const progresses = ['In Progress', 'Complete'];

  return {
    environment: environments[Math.floor(Math.random() * environments.length)],
    description: generateDescription(),
    clientID: generateClientID(),
    secret: generateSecret(),
    offerAmount: Math.floor(Math.random() * 10000),
    status: statuses[Math.floor(Math.random() * statuses.length)],
    progress: progresses[Math.floor(Math.random() * progresses.length)],
  };
};

const ITEM_LIST = {
  lists: [
    {
      label: 'Accept',
    },
    { label: 'Cancel' },
  ],
};

// Generate 50 objects
const data = Array.from({ length: 230 }, () => generateData()).map(
  (i, index) => ({ ...i, id: index + 1 }),
);

const columnsData = [
  {
    id: 'id',
    header: 'id',
    accessorKey: 'id',
    type: 'number',
    maxSize: 90,
    cell: info => <TableText variant="text" label={info.getValue()} />,
    footer: info => (
      <td colSpan={4}>
        <TableText
          className={styles.footer}
          label={info.column.id}
          isInverted
        />
      </td>
    ),
  },
  {
    id: 'Description',
    cell: info => <TableText variant="text" label={info.getValue()} />,
    header: () => 'Description',
    accessorKey: 'description',
    maxSize: 350,
  },
  {
    header: () => 'Client ID',
    cell: info => <TableText label={info.getValue()} variant="copyClipboard" />,
    accessorKey: 'clientID',
    type: 'number',
  },
  {
    header: () => <span>Secret</span>,
    accessorKey: 'secret',
    cell: info => <TableText label={info.getValue()} variant="masked" />,
    enableSorting: false,
  },
  {
    header: 'Status',
    footer: info => (
      <td colSpan={2}>
        <TableText
          className={styles.footer}
          label={info.column.id}
          isInverted
        />
      </td>
    ),
    maxSize: 150,
    accessorKey: 'status',
    cell: info => (
      <TableText
        label={info.getValue()}
        variant="statusIndicator"
        indicatorTheme={info.getValue() === 'Active' ? 'success' : 'danger'}
      />
    ),
  },
  {
    header: () => 'Offer amount',
    cell: info => <TableText label={info.getValue()} variant="currency" />,
    accessorKey: 'offerAmount',
    type: 'number',
    isUsedForStatistics: true,
    grandTotal: '$1,687,794',
    maxSize: 150,
  },
  {
    header: '',
    enableSorting: false,
    maxSize: '40px',
    accessorKey: 'id',
    cell: info => (
      <div className="w-fit-content">
        <Dropdown
          id={`Dropdown-${info.getValue()}`}
          place="bottom"
          item={ITEM_LIST}
          onClick={() => {
            //
          }}
          openOnClick
        >
          <div className="w-fit-content">
            {/* <MoreHorizontalIcon
              id={`Dropdown-${info.getValue()}`}
              stroke="#1872ED"
              height={30}
              width={30}
            /> */}
          </div>
        </Dropdown>
      </div>
    ),
  },
];

/**
 * test
 */
type Story = StoryObj<typeof Table>;

export const Basic: Story = {
  args: {
    rowData: data,
    columns: columnsData,
    hasCardView: true,
    // noRecordConfig: {
    //   illustration: <CarUTurnIllustration height="160" width="100%" />,
    //   body: TABLE_NO_RECORD_DEFAULT_BODY_TEXT,
    //   heading: TABLE_NO_RECORD_DEFAULT_HEADER_TEXT,
    // },
  },
  render: ({ rowData, columns, hasCardView, footerConfig, noRecordConfig }) => (
    <div className="m-4">
      <Table
        rowData={rowData}
        searchableColumns={['id']}
        columns={columns}
        isInverted={false}
        paginationDetails={{
          pageIndex: 0,
          pageSize: 20,
        }}
        noRecordConfig={noRecordConfig}
        hasCardView={hasCardView}
        hasFirstColumnPinned
        mobileCardProps={props => ({
          bodyTitle: props.description,
          bodySubTitle: props.clientID,
          cardHeaderLeft: (
            <div className={styles.indicatorRoot}>
              <div className={styles.indicatorWrapper}>
                <StatusIndicator
                  theme={props.status === 'Active' ? 'success' : 'danger'}
                />
              </div>
              <span className={styles.label}>{props.status}</span>
            </div>
          ),
          id: props.id,
          actionItems: {
            lists: [{ label: 'Test dropdown' }],
          },
        })}
        hasRowSelection
        onRowsSelection={rows => {
          console.log('Selected rows: ', rows);
        }}
        footerConfig={footerConfig}
      />
    </div>
  ),
};

export const WithServer = () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [visibleRows, setVisibleRows] = useState<any>(
    data.slice(0, 20).map(item => ({
      ...item,
      isLoading: false,
    })),
  );
  const [paginationDetails, setPaginationDetails] = useState({
    pageIndex: 0,
    pageSize: 20,
  });

  useEffect(() => {
    setVisibleRows(data.slice(0, 20));
  }, []);

  useEffect(() => {
    const page = paginationDetails.pageIndex * paginationDetails.pageSize;
    setVisibleRows(data.slice(page, page + paginationDetails.pageSize));
  }, [paginationDetails.pageIndex, paginationDetails.pageSize]);

  const handlePagination = updatedPagination => {
    setPaginationDetails(updatedPagination);
  };

  return (
    <div
      style={{
        margin: '16px',
      }}
    >
      <Table
        rowData={visibleRows}
        columns={columnsData}
        hasFirstColumnPinned
        handlePagination={handlePagination}
        isInverted={false}
        totalCount={30}
        paginationDetails={paginationDetails}
        hasManualPagination
      />
    </div>
  );
};

export default meta;
