@value variables: "../../styles/variables.module.css";
@value black5, primaryFade, black, black10, black35, black50, black70, primary, primaryDark, white from variables;
@value oneSpace, displayMobileFontSize, threeSpace, twoSpace from variables;
@value ease-out-expo from variables;

.radio {
  cursor: pointer;
  display: inline-flex;
  flex-flow: row;
  align-items: center;
  user-select: none;
}

.disabledPointerEvents {
  pointer-events: none;
}

.disabled {
  composes: disabledPointerEvents;
}

.checked {
  & .indicator {
    border-color: primary;

    &:after {
      transform: translate(-50%, -50%) scale(1);
    }
  }

  & .label {
    color: black;
  }

  &.radioButton {
    color: primaryDark;
  }
}

.disabled {
  & .label {
    color: black35;
  }

  & .indicator {
    border-color: black10;

    &:after {
      background: black35;
    }
  }
}

.indicator {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: solid 2px black50;
  position: relative;
  transition: border-color 0.5s ease-out-expo;
  flex: 0 0 auto;

  &:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    transform-origin: center center;
    background: primary;
    width: 0.5em;
    height: 0.5em;
    border-radius: 50%;
    transition: transform 0.2s ease-out-expo;
  }
}

.label {
  margin-left: twoSpace;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.01em;
  color: black70;
  transition: color 0.5s ease-out-expo;
}

.radioButton {
  padding: twoSpace threeSpace;
  cursor: pointer;
  border-radius: oneSpace;
  transition:
    background-color 0.5s ease-out-expo,
    border-color 0.5s ease-out-expo,
    color 0.5s ease-out-expo;
  background-color: white;
  border: 1px solid black10;
  color: black;
  min-height: displayMobileFontSize;
  font-size: var(--buttonLabelLargeFontSize);
  line-height: 1.5;
  letter-spacing: 0em;
  font-weight: 500;
  max-width: 100%;

  &:hover {
    color: black35;
    border-color: black35;
  }

  &:active,
  &:focus {
    outline: none;
    color: primaryDark;
    border-color: primary;
  }

  &.checked {
    color: primaryDark;
    background-color: primaryFade;
    border-color: primary;
    & .label {
      color: primaryDark;
    }
  }

  &.disabled {
    pointer-events: none;
    background-color: white;
    color: black35;
    border-color: black5;
    & .label {
      color: black35;
    }
  }

  & .label {
    color: black;
  }
}
