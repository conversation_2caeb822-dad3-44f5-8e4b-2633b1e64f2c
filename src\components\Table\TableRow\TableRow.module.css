@value variables: "../../../styles/variables.module.css";
@value primary, black, black5, primary5, white5, white70, white, neutralLightest, ease-out-expo, neutralLightTable, oneSpace, twoSpace from variables;
@value typography: "../../../styles/typography.module.css";
@value bodySmall from typography;

.arrowIconWrapper {
  display: inline-block;
  transition: transform 0.5s ease-out-expo;
  line-height: 0;
  vertical-align: sub;
  margin-left: oneSpace;

  &.ascending {
    transform: rotate(180deg);
  }
}

.bodyWrapper {
  flex-grow: 1;
  overflow: auto;
}

.body {
  position: relative;
  width: 100%;
}

.row {
  --footerColor: black;
  border-radius: 16px;
  padding-left: oneSpace;
  cursor: pointer;
  align-items: center;
  background-color: neutralLightest;
  transition: 0.2s;

  &.isSecondary {
    background-color: neutralLightTable;
  }

  &.isLoading {
    pointer-events: none;
    background-color: white;
  }
  &.isFaded {
    opacity: 0.35;
  }

  &.rowClickable {
    cursor: pointer;
  }

  &:hover {
    background-color: primary5;
    /* add background-color for all childredn */
    & td {
      background-color: #e7f0fe !important;
    }
  }
}

.isSelected {
  background-color: primary5;
  /* add background-color for all childredn */
  & td {
    background-color: #e7f0fe !important;
  }
}

.isInverted {
  color: white70;
  & .rowClickable {
    padding-left: twoSpace;
    &:hover {
      background-color: white5;
      border-radius: 16px;
    }
  }

  & .cell {
    color: white;
  }

  & .header {
    color: neutralLightest;
  }

  & .headerGroup {
    background-color: inherit;
    padding-left: twoSpace;
    color: white70;
    &.headerSortable {
      &:focus {
        color: primary;
      }
    }
    &.headerSorted {
      color: white;
    }
  }
}

.shimmer {
  padding: twoSpace;
}

.virtual {
  & .row {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    transform: translateY(var(--virtualRowStart));
  }
}

.cell {
  color: black;
  padding-right: oneSpace;
  transition: background-color 0.5s ease-out-expo;

  &:first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  &:last-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}

.cellContents {
  composes: bodySmall;
}

.topScrim {
  transform: translateY(60px);
}
