import React, { useState } from 'react';
import { Meta } from '@storybook/react';
import PhoneInput from '@components/PhoneInput';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

/**
 * The PhoneInput component provides an input interface for users to enter and format phone numbers with a specific pattern.
 *
 * ## Overview
 *
 * The PhoneInput component is designed to handle phone number input, ensuring that the value is formatted as a phone number (e.g., `xxx-xxx-xxxx`). It includes various event handlers to manage user interactions, such as deletion, pasting, and cutting, to maintain the correct phone number format.
 *
 * ## Usage
 *
 * To use the PhoneInput component in your React application, import it from the appropriate directory and render it with the desired props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { PhoneInput } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the PhoneInput component in your JSX:
 *
 * ```jsx
 * <PhoneInput
 *   id="phone-input"
 *   label="Phone Number"
 *   value={phone}
 *   onChange={setPhone}
 *   error=""
 *   isDisabled={false}
 *   required
 *   name="userPhone"
 * />
 * ```
 *
 * This will render a phone number input field with a label, validation, and controlled value.
 *
 */
const meta: Meta<typeof PhoneInput> = {
  title: 'Components/Phone Input',
  tags: ['autodocs'],
  component: PhoneInput,
};

export const PhoneInputStory = () => {
  const [phone, setPhone] = useState('');

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <ComponentGrid countColumns={2}>
        <ComponentTile label="Phone Input field">
          <PhoneInput onChange={setPhone} value={phone} />
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
};

PhoneInputStory.args = {};

export default meta;
