import useIsomorphicLayoutEffect from '@hooks/useIsomorphicLayoutEffect';

// detects if fonts have loaded, and if they have, sets [data-fonts-loaded]
// attribute on doucment
const useDocumentFontLoad = () => {
  useIsomorphicLayoutEffect(() => {
    const aventaLoadPromise = document?.fonts?.load('1em Aventa');
    const helveticaNowLoadPromise = document?.fonts?.load(
      '1em Helvetica Now Var',
    );

    Promise.all([aventaLoadPromise, helveticaNowLoadPromise]).then(() => {
      const docEl = document.documentElement;
      docEl.setAttribute('data-fonts-loaded', '');
    });
  }, []);
};

export default useDocumentFontLoad;
