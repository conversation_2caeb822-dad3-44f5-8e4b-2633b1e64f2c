/* eslint-disable no-console */
// import { CopyIcon } from '@peddleon/ped-ux-react-icons';
import { Meta } from '@storybook/react';

import CodeBlock from './CodeBlock';

/**
 * The CodeBlock component displays a code snippet with an optional icon.
 *
 * ## Overview
 *
 * The CodeBlock component renders a code snippet enclosed in a pre tag. It also supports displaying an optional icon.
 *
 * ## Usage
 *
 * To use the CodeBlock component, import it into your React application and render it with the appropriate props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { CodeBlock } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, include the CodeBlock component in your JSX:
 *
 * ```jsx
 * <CodeBlock
 *   code="<script>
 *   PeddlePublisherEmbedConfig={publisherID: &quot;5214&quot; }, function(){ if(&quot;function&quot;!=typeof window.PeddlePublisherEmbedConfig) }
 * </script>"
 *   icon={<CopyIcon size={14} />}
 *   onClick={() => {}}
 * />
 * ```
 *
 * This will render a code snippet enclosed in a pre tag with an optional copy button icon.
 */

const meta: Meta<typeof CodeBlock> = {
  component: CodeBlock,
  title: 'Core/CodeBlock',
  tags: ['autodocs'],
};

export const CodeBlockStory = ({ code, icon, onClick }) => (
  <CodeBlock code={code} icon={icon} onClick={onClick} />
);

CodeBlockStory.args = {
  code: `<script>
  PeddlePublisherEmbedConfig={publisherID: "5214" }, function(){ if("function"!=typeof window.PeddlePublisherEmbedConfig) }
</script>`,
};
CodeBlockStory.argTypes = {
  code: {
    control: 'text',
    description:
      'Code in string format to display in UI which provides looke & feel like code block',
  },
};

export const CodeBlockWithoutIconStory = ({ code, icon, onClick }) => (
  <CodeBlock code={code} icon={icon} onClick={onClick} />
);

CodeBlockWithoutIconStory.args = {
  code: 'PeddlePublisherEmbedConfig={publisherID: "5214" }, function(){ if("function"!=typeof window.PeddlePublisherEmbedConfig) }',
  icon: null,
  onClick: code => {
    console.log(`Copied : ${code}`);
  },
};
CodeBlockWithoutIconStory.argTypes = {
  code: {
    control: 'text',
    description:
      'Code in string format to display in UI which provides looke & feel like code block',
  },
};

export default meta;
