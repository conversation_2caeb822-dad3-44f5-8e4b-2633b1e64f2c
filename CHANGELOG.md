# Changelog

<br />

## [1.16.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.15.0...v1.16.0) (2025-01-23)


### Features

* [UX-252] add support for the timezone ([324004c](https://github.com/peddleon/ped-ux-react-library/commit/324004c756dd126f704d7467eb09b0cb31a96db8))
* [UX-252] export date picker component ([45bbcc2](https://github.com/peddleon/ped-ux-react-library/commit/45bbcc2fb76aab866dc8f47f7c3d90b9a63792af))
* [UX-252] export the date while selected today ([97f1f8a](https://github.com/peddleon/ped-ux-react-library/commit/97f1f8a23d5f1877ebb9ae5d10b8fae19f7ffd02))
* [UX-252] expose today click event ([f8a36b7](https://github.com/peddleon/ped-ux-react-library/commit/f8a36b7aec8823a2439beada3401433452cc7c77))
* [UX-252] make it controlled component ([45d5b96](https://github.com/peddleon/ped-ux-react-library/commit/45d5b96d8bfe4adb4e01df6c1e1e1006936af14b))
* [UX-252] make it picker center to parent ([b840c79](https://github.com/peddleon/ped-ux-react-library/commit/b840c79b77cd4f055bc862e6fce1ecc96513f8a5))
* [UX-252] merge support for default value for the control selection ([c336884](https://github.com/peddleon/ped-ux-react-library/commit/c336884afc31b71725ae86ada6f7f3ccc9cf4010))
* [UX-252] update storybook ([de6468f](https://github.com/peddleon/ped-ux-react-library/commit/de6468f90cbe390b324cea76d73bc3e7e26e2971))
* [UX-252] update storybook description for the default value of timezone ([217d44a](https://github.com/peddleon/ped-ux-react-library/commit/217d44a92804f5eb2e70e01111bcc41cc893f91e))
* [UX-253] add support for a new UI to display when no records are found ([f142884](https://github.com/peddleon/ped-ux-react-library/commit/f142884b9ab5c5833fc91cf65fe0a112f098b4bc))
* [UX-253] added no records found support in storybook ([dd83277](https://github.com/peddleon/ped-ux-react-library/commit/dd832773158fcf6a2320c19bbd160892ff580004))
* [UX-259] update total rows format in pagination details ([0eaa7cc](https://github.com/peddleon/ped-ux-react-library/commit/0eaa7cc3cacff21876ef80af9e64290822eee603))
* [UX-310] update toast component width in mobile view ([7abd05b](https://github.com/peddleon/ped-ux-react-library/commit/7abd05b66bcfbf6ed3e84e29cb1e01ead842de3a))
* [UX-312] fix shift key issue while selecting a rows ([cd08f3e](https://github.com/peddleon/ped-ux-react-library/commit/cd08f3e70b3b4dcd9cad537c74fb2aecf01686a8))
* [UX-312] fixed issues regarding loading flicker in mobile screen ([87b2be2](https://github.com/peddleon/ped-ux-react-library/commit/87b2be29a8198f7d68ec894cacc02ddef55a2fdb))
* add AutoComplete component with Google Place Search integration and related stories ([ce6de6d](https://github.com/peddleon/ped-ux-react-library/commit/ce6de6dc33a3f510434bfd9a75d9ae2fe95f4ef3))
* add AutoComplete component with types and styles ([df852e5](https://github.com/peddleon/ped-ux-react-library/commit/df852e58eb47820300fb083c0a7502d7ca55cc2f))
* add fixed menu option to AutoComplete component with positioning adjustments ([d7c5396](https://github.com/peddleon/ped-ux-react-library/commit/d7c5396d2a70a534a0b39f50cd3ad75cceb2c4c8))
* add hasMaxHeight prop to Modal for improved height management ([9a5dbec](https://github.com/peddleon/ped-ux-react-library/commit/9a5dbec684d2331a5cbe1c08483ff4a4d910b1a1))
* add Logo component export to index for easier access ([ea34e63](https://github.com/peddleon/ped-ux-react-library/commit/ea34e639fc1887976375cf3b3adbc71d92cf4aa8))
* add max-height and overflow styles to dialogWrapper for improved responsiveness ([f2d1d55](https://github.com/peddleon/ped-ux-react-library/commit/f2d1d55f3be916abd6e29fb418ed3ccc26d19fbd))
* add new variant support to SelectSearch component with updated styles and props ([614ed17](https://github.com/peddleon/ped-ux-react-library/commit/614ed17a5b83b71594c4da04e03c90c3828214d4))
* add new variant support to TextInput component with responsive styles ([91ca2aa](https://github.com/peddleon/ped-ux-react-library/commit/91ca2aa92b7b11052fec74b3c6d8980c558981b0))
* add row selection functionality to table cards ([ff48ec3](https://github.com/peddleon/ped-ux-react-library/commit/ff48ec3bd0f7af8e47e46c7c09aa1bdf17e1bfa8))
* add TableCard component to index exports ([8e4e2e0](https://github.com/peddleon/ped-ux-react-library/commit/8e4e2e0ef9c52f3da330030a72f426c17388b5f5))
* enhance AutoComplete component with grouping support and styling adjustments ([7a92010](https://github.com/peddleon/ped-ux-react-library/commit/7a92010ce17a2a14afa82a8e3bdd509d8435ab1c))
* enhance card header styling for row selection in table component ([174d017](https://github.com/peddleon/ped-ux-react-library/commit/174d017d93210c9de1a7b1badfa8a475d62d78a6))
* enhance DialogModal styles with max-height ([b82561a](https://github.com/peddleon/ped-ux-react-library/commit/b82561a9f387ab20299f911406709ed34e73e4a8))
* implement date selection functionality in calendar component ([ccead75](https://github.com/peddleon/ped-ux-react-library/commit/ccead75cfbd2072f1b6c97552c7f5c020e790fe6))
* update SelectSearch component to support new font variants with corresponding styles ([c1fd1ba](https://github.com/peddleon/ped-ux-react-library/commit/c1fd1ba056c98cd0d4f1a4392f3735769438e308))


### Bug Fixes

* [UX-252] button selected css not getting applied ([3f4fedd](https://github.com/peddleon/ped-ux-react-library/commit/3f4fedd2ac043d6ba2f853e69f1cbecf25c3f23c))
* [UX-252] infinate rendering for controlled varient ([769dfc9](https://github.com/peddleon/ped-ux-react-library/commit/769dfc92cbe1cb6b66a7583798c114f2a3b7fe91))
* [UX-252] today button position ([6a2a243](https://github.com/peddleon/ped-ux-react-library/commit/6a2a24351b5ef737047ccd4e639575116db6259d))
* [UX-259] padding issue for the loading and no records found error message ([c5fd9c0](https://github.com/peddleon/ped-ux-react-library/commit/c5fd9c05c45595b4c3105db0b5b8896e5f40f6ac))
* add early return for menuListHeight check in AutoComplete component ([0c91dad](https://github.com/peddleon/ped-ux-react-library/commit/0c91dad89e80554b2e3a5b226f24a91012f18d76))
* add padding-top to Table component styles for improved layout ([b6dadbf](https://github.com/peddleon/ped-ux-react-library/commit/b6dadbf7ee370c96795145a5430a115b5d0681a5))
* adjust ChevronDownIcon size in SelectSearch component for consistency ([74f881b](https://github.com/peddleon/ped-ux-react-library/commit/74f881b74a5ce377cea179c080629f46ba750de7))
* adjust top positioning logic in AutoComplete component ([05b4dd8](https://github.com/peddleon/ped-ux-react-library/commit/05b4dd8d441f8055641e0ee08a827cf8e8281477))
* clarify hasMaxHeight prop description in Modal types for better understanding ([4238a42](https://github.com/peddleon/ped-ux-react-library/commit/4238a4276d2affda68d83391a4c55798ecdbe2f7))
* clean up SelectSearch component by removing unused styles and simplifying JSX structure ([3affe90](https://github.com/peddleon/ped-ux-react-library/commit/3affe90d44be66f86c69abd18c693316989d54cb))
* correct input value handling in SelectSearch for dynamic fetching ([a854ea6](https://github.com/peddleon/ped-ux-react-library/commit/a854ea64bfefeff262037a1b61aaa15262001268))
* correct isFirst property type in AutoComplete component ([63eab7e](https://github.com/peddleon/ped-ux-react-library/commit/63eab7e3084903f395aeb799466b1907f09510fb))
* enhance selection logic and input handling in SelectSearch for dynamic fetching ([bbff523](https://github.com/peddleon/ped-ux-react-library/commit/bbff5237c44b802a9072723f0edb481f36b03e27))
* ensure default values for statistics in TablePagination component ([dbed6c4](https://github.com/peddleon/ped-ux-react-library/commit/dbed6c46ba6a0f3a7e7c4cfafebf7f815947f591))
* handle input value update for dynamic fetching in SelectSearch component ([cf2be85](https://github.com/peddleon/ped-ux-react-library/commit/cf2be85a4615c84849ce2031e83599fae99f504b))
* improve allRowsSelected logic in Table component for better accuracy ([e53a2a6](https://github.com/peddleon/ped-ux-react-library/commit/e53a2a617033808f5f3b6a7b9b3d10139db3189f))
* improve input value handling in SelectSearch for dynamic fetching ([0dfa418](https://github.com/peddleon/ped-ux-react-library/commit/0dfa4182c2e898a1b6d0a3c91ca674caaf126bc9))
* improve modal opening logic in SelectSearch component for better performance ([dd6aad3](https://github.com/peddleon/ped-ux-react-library/commit/dd6aad389a2fec821f745b53dea99b3a3121f4c1))
* pass selectedRowsData to Table component ([036f102](https://github.com/peddleon/ped-ux-react-library/commit/036f102173df22d9de81cdfd56c54415b28d3930))
* react-day-picker installation ([0cdd1b5](https://github.com/peddleon/ped-ux-react-library/commit/0cdd1b5f4b79d7bfd0e3529e695ba482e8b68afc))
* remove unnecessary padding-top from Table component styles ([d3521f7](https://github.com/peddleon/ped-ux-react-library/commit/d3521f7904d180fa8d1363809923d6389f14afee))
* replace date formatting function with formatDateTable for consistency ([c739217](https://github.com/peddleon/ped-ux-react-library/commit/c7392175e5754439112445f30e60cc227372294c))
* reset highlighted index on input focus for dynamic fetching in SelectSearch ([4845daf](https://github.com/peddleon/ped-ux-react-library/commit/4845daf4cf2ce3242e5a0a699cd817805e6110c1))
* simplify condition for fixed menu in AutoComplete component ([38e4af3](https://github.com/peddleon/ped-ux-react-library/commit/38e4af314a4d10a324726170cde96cf0a933edbc))
* simplify JSX structure in SelectSearch component by removing unnecessary conditional rendering ([47a64e5](https://github.com/peddleon/ped-ux-react-library/commit/47a64e5c205ac46c052b79af99456591f03b0fb2))
* spread link props in Dropdown component for improved functionality ([b014990](https://github.com/peddleon/ped-ux-react-library/commit/b014990c55eb49ca615b8f16b228555c7df997bd))
* streamline item pushing logic in AutoComplete component ([8cd54b0](https://github.com/peddleon/ped-ux-react-library/commit/8cd54b001fd8158a2a6c912ac572d15b5c76395f))
* update class name for center alignment in Table component ([474a71a](https://github.com/peddleon/ped-ux-react-library/commit/474a71a49c9727914f3677d99d5871483f05ad0b))
* update date formatting to allow custom separators ([ccdbacd](https://github.com/peddleon/ped-ux-react-library/commit/ccdbacdfbb8092e67604750fc74610de1b05ccdd))
* update input value handling in SelectSearch for dynamic fetching ([b55e8b5](https://github.com/peddleon/ped-ux-react-library/commit/b55e8b562231ce77052489f04e0f94777e85ad1e))
* update key press handling in AccordionListItem component ([26ae028](https://github.com/peddleon/ped-ux-react-library/commit/26ae0284cfeb687291d3a8f8e628e5f352d678c7))
* update listItems state management in SelectSearch component for dynamic fetching ([3bdfdff](https://github.com/peddleon/ped-ux-react-library/commit/3bdfdffd63cce4dab224e27c2292f3f6b399f83c))
* update row selection handler to use table model rows ([97100e5](https://github.com/peddleon/ped-ux-react-library/commit/97100e591ead82deaf326a14c1b6be34cfe31446))
* update SelectSearch component to improve input value handling and menu visibility logic ([d740081](https://github.com/peddleon/ped-ux-react-library/commit/d740081efcbd6bf53d14a2a4085f911ab3d04c08))


### Miscellaneous

* [UX-252] seperated footer component for better readablity ([80e0b50](https://github.com/peddleon/ped-ux-react-library/commit/80e0b500621c9b865f9f7bf03cc41e751e572ea9))
* [UX-253] removed unused code ([7b1cb1c](https://github.com/peddleon/ped-ux-react-library/commit/7b1cb1cef7b89bdeff1222e12c378016899c128f))
* change export of ITEM_LIST and ITEM_LIST_WITH_LEFT_ICON to const in Dropdown stories ([8002888](https://github.com/peddleon/ped-ux-react-library/commit/8002888c4881ad2ec2837d8ae2e289a2d58eaecf))
* remove AutoComplete component and update SelectSearch types and functionality ([45a242f](https://github.com/peddleon/ped-ux-react-library/commit/45a242fdcb582995e19f0cf814e1f92c44c87d1a))
* rename default export to named export for isInViewport utility ([00f6223](https://github.com/peddleon/ped-ux-react-library/commit/00f622356b36297c14d8b3e2c86c65680875e44f))
* simplify SelectSearch component by removing dynamic fetching logic and unused props ([b09d2aa](https://github.com/peddleon/ped-ux-react-library/commit/b09d2aadbc6e61460951e8db71c422ccde5f61fb))
* streamline menu handling in SelectSearch component for improved focus management ([8f62836](https://github.com/peddleon/ped-ux-react-library/commit/8f628366516c55814747ab002e5a4a43310c7c33))
* update select search for having forwardref ([1406bdb](https://github.com/peddleon/ped-ux-react-library/commit/1406bdbb978bb78c1fca1a311a335caab0ab6ac5))

## [1.15.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.14.0...v1.15.0) (2024-12-30)


### Features

* [UX-201] table style update ([f73158e](https://github.com/peddleon/ped-ux-react-library/commit/f73158ecd9e8885d5c6c1d0829082e024e2b1e37))
* [UX-249] expose selected rows actual data ([5c24cab](https://github.com/peddleon/ped-ux-react-library/commit/5c24cab7e1270be73f28524809073a299254ed49))
* [UX-249] reset selection state when pagination update ([2e2f1db](https://github.com/peddleon/ped-ux-react-library/commit/2e2f1db92cf44523e5cb55e4bc240fad6b65c0ed))
* [UX-249] update page selection ([725cab9](https://github.com/peddleon/ped-ux-react-library/commit/725cab95863b9b1c0cee67fe8ce44f4d48513163))
* [UX-250] add loading state ([74ff697](https://github.com/peddleon/ped-ux-react-library/commit/74ff6970b236e88351c0b3c60909dbd98799c72c))
* [UX-250] fixed loading issue ([08696cb](https://github.com/peddleon/ped-ux-react-library/commit/08696cbcf8d448aa70f30b9b5600f6e494e59f3d))
* [UX-250] update header is not function issue ([7ec4bd0](https://github.com/peddleon/ped-ux-react-library/commit/7ec4bd001f73dbfc40df55af1a7fc0c7ca560078))
* [UX-250] update storybook for the changes ([4d2c74f](https://github.com/peddleon/ped-ux-react-library/commit/4d2c74f6d7a1af5f4fd232d7fab615e25d239536))
* [UX-250] update table manual pagination flag related change ([f8388c0](https://github.com/peddleon/ped-ux-react-library/commit/f8388c01ab50b7da56617a6edf78059fe05412e5))
* [UX-252] added type description and update the storybook for the same ([da4ffde](https://github.com/peddleon/ped-ux-react-library/commit/da4ffded9e484ed9c8369f5d95e0bf336c511824))
* [UX-252] excluded react-day-picker from roll up config ([3486f4c](https://github.com/peddleon/ped-ux-react-library/commit/3486f4ccde0436b6fc27a05e4d6bc45e8cb96926))
* [UX-252] intial date picker setup ([3d1deff](https://github.com/peddleon/ped-ux-react-library/commit/3d1deff22be8adb4645c9a38878672caa227a735))
* [UX-252] refactored code for the issue related to disable dates ([c9e6bd3](https://github.com/peddleon/ped-ux-react-library/commit/c9e6bd3108b83fe2965094e269f18f45fd646223))
* [UX-252] removed unused type ([1f64526](https://github.com/peddleon/ped-ux-react-library/commit/1f6452653e21c94e6ab0be8e671be26b89e198f3))
* [UX-252] update for week day ([c84de47](https://github.com/peddleon/ped-ux-react-library/commit/c84de470b3e274c24febec9677af7d5251d4b1bb))
* [UX-253] fixed svg exported issue ([e96a029](https://github.com/peddleon/ped-ux-react-library/commit/e96a029304a16472b23f6799c9cf9b053199825b))
* [UX-253] fixed url change issue ([50b5f1c](https://github.com/peddleon/ped-ux-react-library/commit/50b5f1c4fac46532b6760797de4f5d410022be6f))
* [UX-253] update prod to dev url for storybook ([cbbaed6](https://github.com/peddleon/ped-ux-react-library/commit/cbbaed681394348dddc82ad479be95cc2ad8dcf9))
* [UX-255] fixed checkbox not being pressed ([db7f03b](https://github.com/peddleon/ped-ux-react-library/commit/db7f03b89f3a450838bf509b0a56f6143d6f5319))
* [UX-255] optimized and update statistics label as column name to be used for stats ([4c138b2](https://github.com/peddleon/ped-ux-react-library/commit/4c138b2008829a312c3ffa9a9f391f0e79de08ea))
* [UX-255] updated TableText component for currency ([d9d18d4](https://github.com/peddleon/ped-ux-react-library/commit/d9d18d42fdc6972424ae8bae3f49cb2dc9e1fe86))
* add column visibility feature to Table component ([0fe1b63](https://github.com/peddleon/ped-ux-react-library/commit/0fe1b63cc8f9466c1568ca0f74b0653f9c04c648))
* add ComponentLoader component with loading indicator and styles ([950d328](https://github.com/peddleon/ped-ux-react-library/commit/950d3282f4047a9c6cab372e5147c1ec3567837b))
* add EMPTY_TABLE_VALUE constant ([2684b24](https://github.com/peddleon/ped-ux-react-library/commit/2684b24001b09e5b0d487f6a2a16bd1884b4b34f))
* add isInViewport utility and optimize scrollIntoView behavior ([721af84](https://github.com/peddleon/ped-ux-react-library/commit/721af84d4eb2497df595b21f63f5276e1b2fd00b))
* add isWrapped prop to Textarea component for text wrapping support ([7785ca5](https://github.com/peddleon/ped-ux-react-library/commit/7785ca5287e7a5e78794f79b0f74b444b75d47a3))
* add isWrapped prop to Textarea story for demonstration ([91715e7](https://github.com/peddleon/ped-ux-react-library/commit/91715e7c21a460baccf1912615914c78d6bab6ac))
* add overflow-wrap support to Textarea component ([198aaf6](https://github.com/peddleon/ped-ux-react-library/commit/198aaf6c0607a03957fc37f4d94b42fe23629406))
* add shouldAutoScroll prop to Table component for conditional scrolling ([1ec5a6c](https://github.com/peddleon/ped-ux-react-library/commit/1ec5a6c509ac22e32ea255abfa47b86733a8ab0e))
* add smooth scrolling to table header on page index change ([bc088a2](https://github.com/peddleon/ped-ux-react-library/commit/bc088a235535c78cde80dca78b27b45188823338))
* add TableText components ([f9e8c15](https://github.com/peddleon/ped-ux-react-library/commit/f9e8c15bf3d8a10a1fec4183c0dc1029a59784a3))
* add TableText components for enhanced table functionality and styling ([d4157fe](https://github.com/peddleon/ped-ux-react-library/commit/d4157fe5f9301cac22f5fa54909cbb01d80b36dd))
* add title attribute to TableTextText for improved accessibility ([490206c](https://github.com/peddleon/ped-ux-react-library/commit/490206c964a40ea47f1239560f7315a6e8ae3ee3))
* enhance pagination visibility logic based on statistics column presence ([8624c82](https://github.com/peddleon/ped-ux-react-library/commit/8624c821ac64afe9d8eb7c2b1474bf75feeff70e))
* enhance TableText component ([003f6c2](https://github.com/peddleon/ped-ux-react-library/commit/003f6c22cc5fc55a71c452545fd6c62960c4a067))
* export ITEM_LIST and ITEM_LIST_WITH_LEFT_ICON ([724ba29](https://github.com/peddleon/ped-ux-react-library/commit/724ba29af715186f3d6a97a47fc9a12e9c11a0f9))
* handle invalid date formatting in TableTextDate and TableTextDateTime components ([e53ee11](https://github.com/peddleon/ped-ux-react-library/commit/e53ee11226900eb84b11ac0775f2a732247b941f))
* implement Tooltip component and integrate with TableTextTooltip ([07c3d7e](https://github.com/peddleon/ped-ux-react-library/commit/07c3d7e2d1cd2e90c62999ebb07f6d74a2400066))
* optimize smooth scrolling ([32725d6](https://github.com/peddleon/ped-ux-react-library/commit/32725d68b12f859f392a1df09a01ed3846fb94e1))
* remove btnSize prop from PaginationRangeButtons component ([b10f99a](https://github.com/peddleon/ped-ux-react-library/commit/b10f99a84c12b9776ba3676de422a9d19f0c39ad))
* update ComponentLoader props to improve loading state handling ([01c5f86](https://github.com/peddleon/ped-ux-react-library/commit/01c5f865b7e77c3e464f366b5be1a454316d68ff))
* update ComponentLoader stories to use isLoading prop for loading state ([4ca39c6](https://github.com/peddleon/ped-ux-react-library/commit/4ca39c6a5cc442a958c231c513fe70355bd00384))
* update currency formatting to use formatCurrency ([0dbe072](https://github.com/peddleon/ped-ux-react-library/commit/0dbe0729656d9ca34e9b6886dce73878838f119a))
* update Table component to use 'id' ([68f2ebe](https://github.com/peddleon/ped-ux-react-library/commit/68f2ebeb4bbc8fba0f30e8a01e7188def35701bd))
* update title attribute in TableTextCurrency to use formatted currency value ([e84ca67](https://github.com/peddleon/ped-ux-react-library/commit/e84ca6716cb8668acecc84488632b734517aa282))
* use requestAnimationFrame for smooth row selection animation ([3cda2dc](https://github.com/peddleon/ped-ux-react-library/commit/3cda2dc6a96a5ac7d043d0c6d679aac89917bb69))


### Bug Fixes

* [UX-249] update table for the selected rows callback issue ([7ee2764](https://github.com/peddleon/ped-ux-react-library/commit/7ee2764376e078b57d3ae60b7213c6d0be503c4e))
* [UX-250] loading issue ([74e7e61](https://github.com/peddleon/ped-ux-react-library/commit/74e7e61f9a85d660dd23a013f466214394e0f69b))
* [UX-250] z-index issue ([05bd657](https://github.com/peddleon/ped-ux-react-library/commit/05bd657989cbfcc68b8cea808c8a8249c15dd0d7))
* [UX-252] default and disable dates together ([98725bf](https://github.com/peddleon/ped-ux-react-library/commit/98725bf6a6843941a01f3f70219e06aba2c3a62f))
* handle empty timestamps in formatTimeRange function ([e1cff58](https://github.com/peddleon/ped-ux-react-library/commit/e1cff5880aaf5e505395dcd62fe6a67ea9a78dc8))
* improve date formatting to handle invalid date cases ([cbbc736](https://github.com/peddleon/ped-ux-react-library/commit/cbbc736657e2bebe07111abef8e3647573f96f54))
* improve time range formatting to handle invalid date cases ([3822774](https://github.com/peddleon/ped-ux-react-library/commit/3822774305a95d7a58eecdeca49b84e3010307c3))
* update ComponentLoader story title and ([30f943d](https://github.com/peddleon/ped-ux-react-library/commit/30f943d20236ff8f39eca4811b16d367414db19f))
* update topZIndex value in variables.module.css ([233c7e0](https://github.com/peddleon/ped-ux-react-library/commit/233c7e01bc7d9b2de2bbed00354a937da196c40e))
* update z-index values for table and modal components ([12fd672](https://github.com/peddleon/ped-ux-react-library/commit/12fd67278b46d1b9aaff3ed3d854c24fb6dd5f69))


### Miscellaneous

* added support for the svg download for the qr code ([a4de6c8](https://github.com/peddleon/ped-ux-react-library/commit/a4de6c87281ec2c5e2f355608fbb8487c90904b1))
* remove unused code in Table.stories.tsx ([358b7cb](https://github.com/peddleon/ped-ux-react-library/commit/358b7cb3bab6155ef3238abe8028e16beb17d9ec))

## [1.14.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.13.0...v1.14.0) (2024-11-29)


### Features

* [UX-255] added page statistics support ([92df5b9](https://github.com/peddleon/ped-ux-react-library/commit/92df5b991a3d61a0c8d3a064f0128aea7b9524b1))
* [UX-255] fixed css issue with devider ([d48c437](https://github.com/peddleon/ped-ux-react-library/commit/d48c4371213b8cbc19ac625d8f9296abcadd4573))
* [UX-255] fixed css issue with devider ([363085d](https://github.com/peddleon/ped-ux-react-library/commit/363085d8267c7ef45742f18ef154866162c3710c))
* [UX-255] fixed storybook ([1660913](https://github.com/peddleon/ped-ux-react-library/commit/1660913fdaa4b630cd2fc121474015c23920be42))
* [UX-255] fixed storybook ([14816ac](https://github.com/peddleon/ped-ux-react-library/commit/14816ac6ef6f5d78ca1858cf81079ce36f265054))
* [UX-255] update currency function ([9e141dc](https://github.com/peddleon/ped-ux-react-library/commit/9e141dc6b0ff2ca142c9e30cac7ec56756207309))
* [UX-255] update footer component ([f434f48](https://github.com/peddleon/ped-ux-react-library/commit/f434f4891720212599d877cad9e6c41b63cf3906))
* [UX-255] update footer component ([6dbfc80](https://github.com/peddleon/ped-ux-react-library/commit/6dbfc800a17bf32375023c4a4edc77712762c949))
* [UX-255] update label copy ([62cd6fe](https://github.com/peddleon/ped-ux-react-library/commit/62cd6feecc32d0409a489a6c1c54a2799840bbd2))
* [UX-255] update storybook for review ([8424467](https://github.com/peddleon/ped-ux-react-library/commit/8424467077eb186f14f84259f70a9a8ad40b8f99))
* [UX-255] update storybook for review ([aa8181d](https://github.com/peddleon/ped-ux-react-library/commit/aa8181dcb8ea53b5bc5e70cc93f1704f840fb12c))
* add className prop to SelectSearch component for custom styling ([f8c9006](https://github.com/peddleon/ped-ux-react-library/commit/f8c90061c48eb09d97822c6ab0291a0f5279c765))
* add hideError prop to Select component to control error message visibility ([363fe72](https://github.com/peddleon/ped-ux-react-library/commit/363fe72f70a9df1657ab7c3e26247acaee85587a))
* add hideErrorMessage prop to SelectSearch ([8c33715](https://github.com/peddleon/ped-ux-react-library/commit/8c33715505f7c65e94396dace07613ebfd6d4a87))
* add isScrollable prop to DialogModal for scrollable content support ([645685a](https://github.com/peddleon/ped-ux-react-library/commit/645685a3952a34d83eefca2e06965e80b7627d2c))
* add right prop to AccordionItem for additional content display ([8deb4fc](https://github.com/peddleon/ped-ux-react-library/commit/8deb4fcc83e66f860d27a07a025c23d8f5503b9a))
* add title prop to Select component ([3070e3b](https://github.com/peddleon/ped-ux-react-library/commit/3070e3b747762ae3981b94d5752a2cd647152c1a))
* add title prop to SelectSearch and TextInput components for improved accessibility ([41ecb40](https://github.com/peddleon/ped-ux-react-library/commit/41ecb40deaa47d65d14f91d1b1fc619def902dc1))
* enhance Accordion component to manage active index changes with callback ([0ea2fef](https://github.com/peddleon/ped-ux-react-library/commit/0ea2fefac2515edff1e9842e7a61dc0ae5cc7afb))
* enhance AccordionListItem accessibility and update Textarea styles ([66f054a](https://github.com/peddleon/ped-ux-react-library/commit/66f054acf74558a8c3f2670f55e45affe0d7216c))
* rename hideError prop to hideErrorMessage for clarity ([63e1f74](https://github.com/peddleon/ped-ux-react-library/commit/63e1f74576f9d7b93e64b21d0ecf6b4139ca9bd6))
* update Accordion component ([f386284](https://github.com/peddleon/ped-ux-react-library/commit/f3862845571c0461075d850e33022667a4ffa11e))
* update Accordion component to support external active index prop ([5fa6294](https://github.com/peddleon/ped-ux-react-library/commit/5fa6294ba3abe0864b05212a7f4c707dbc002df4))
* update Textarea styles to ensure block display ([bd97f78](https://github.com/peddleon/ped-ux-react-library/commit/bd97f78a5514131a8518e6a0663fc28ca1d12261))
* wrap Dropdown stories in a consistent layout component for improved positioning ([fee9b26](https://github.com/peddleon/ped-ux-react-library/commit/fee9b266b5e4e57c8f700c7cc05d9e4497bf054f))


### Bug Fixes

* add !important to padding-right in Textarea styles for consistency ([fc0309c](https://github.com/peddleon/ped-ux-react-library/commit/fc0309cdf323a0aa225ddbe2e7da9b836baef1e6))
* adjust padding in Textarea component for improved layout ([847fda2](https://github.com/peddleon/ped-ux-react-library/commit/847fda21c35673262eac0294da2dfbbaca9dcc1b))
* update Accordion component to handle undefined propsActiveIndex gracefully ([e276829](https://github.com/peddleon/ped-ux-react-library/commit/e27682962c1a3a826e4dfec9eed598897308c61d))
* update caption handling in TextInput component for improved type safety ([1341528](https://github.com/peddleon/ped-ux-react-library/commit/134152885cf8b78954ba0d5f68156c558e941700))


### Miscellaneous

* resolve conflicts ([823722b](https://github.com/peddleon/ped-ux-react-library/commit/823722b2b0c191e169385ce94c67d21ca607e751))

## [1.13.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.12.0...v1.13.0) (2024-11-12)


### Features

* [UX-152] fixed lint warning ([1b25628](https://github.com/peddleon/ped-ux-react-library/commit/1b25628bccecaf380a7e129ece71ef72cebacee3))

## [1.12.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.11.0...v1.12.0) (2024-10-30)


### Features

* [UX-197] updated copy ([5a2cb11](https://github.com/peddleon/ped-ux-react-library/commit/5a2cb11ba0af7e9e7d6b95c4ce64943e1e6fe02d))
* add Accordion component to component index ([17d519e](https://github.com/peddleon/ped-ux-react-library/commit/17d519eb90b7c27ba11697aaf07911f60f7a99e2))
* add className prop to Select component ([bc564e3](https://github.com/peddleon/ped-ux-react-library/commit/bc564e32f192c11cfd08bfb623a49cfaec28ec9a))
* add SearchInput component ([346d643](https://github.com/peddleon/ped-ux-react-library/commit/346d643253675749001f8eaabd3236e8d74f63a2))
* add StatusIndicator component to component index ([2b2bd40](https://github.com/peddleon/ped-ux-react-library/commit/2b2bd404881d8db11a39f5a3e2aa0f6a227aba43))
* qr code demo ([da5c0ff](https://github.com/peddleon/ped-ux-react-library/commit/da5c0ffba9e0d853a51f9ff0c369d6ba448fe684))
* update SectionTitle and SectionWrapper components to accept headingProps ([4cff4e2](https://github.com/peddleon/ped-ux-react-library/commit/4cff4e27415d4df72908e6d2bf3445bf95890135))
* update SectionWrapper component to include headingProps ([d306683](https://github.com/peddleon/ped-ux-react-library/commit/d306683a0317def97d9ffcd1073ce064cff7a39f))
* update Select component to use label prop instead of hard-coded headingContent ([952938c](https://github.com/peddleon/ped-ux-react-library/commit/952938cccd903f6dd3cdeec4845b6a24d7c262bd))


### Bug Fixes

* codeQL on PR and develop branch ([dedcee9](https://github.com/peddleon/ped-ux-react-library/commit/dedcee9f48fcea5fd469c1f0922949c961f24d0f))
* dependabot issue ([9fdbb87](https://github.com/peddleon/ped-ux-react-library/commit/9fdbb87e91c092906a18f9cf6dedadbdfea9dab9))


### Miscellaneous

* accordion styling with variables ([002596c](https://github.com/peddleon/ped-ux-react-library/commit/002596cb5b5462071ec5516a15823a95ffb09cc9))
* add ButtonSize prop to SearchInput component ([b8f3f4b](https://github.com/peddleon/ped-ux-react-library/commit/b8f3f4b1b03c06fde7d9ccb97493eb37bba2aca9))
* add conditional rendering for SectionWrapper content ([17b4d6d](https://github.com/peddleon/ped-ux-react-library/commit/17b4d6d110c2997b2437e8b3cc74e2a4dc1b33b7))
* add forwardRef to SearchInput component ([8fa94a4](https://github.com/peddleon/ped-ux-react-library/commit/8fa94a4143154b7beac0397f6e7c612c1aee08d7))
* add hideClearButton prop to SearchInput component ([462a1d7](https://github.com/peddleon/ped-ux-react-library/commit/462a1d702a264c2bceda68fce1c59317940f021b))
* add left prop to Dropdown ItemType and MenuItem link object ([eed8104](https://github.com/peddleon/ped-ux-react-library/commit/eed8104dd2b4b39305064779a1fa98d6f8ac8d95))
* add onColumnOptionClick prop to TableHeader component ([429c396](https://github.com/peddleon/ped-ux-react-library/commit/429c396afa00b7ca2967803c8eda3966936eab27))
* add onRowCountChange prop to Table component ([96935b3](https://github.com/peddleon/ped-ux-react-library/commit/96935b303bc0c59883880fd4e48fc0481770f5e4))
* add pagination functionality to Table component and introduce useDebouncedState hook ([2937ab0](https://github.com/peddleon/ped-ux-react-library/commit/2937ab03768d00b8bacaaa8b0f0f48a6c9737245))
* add PaginationRangeButtons component to TablePagination module ([de1dc29](https://github.com/peddleon/ped-ux-react-library/commit/de1dc29bb6f62cfa8a9cfceefa6db370f840094d))
* add searchableColumns prop to Table component ([4d940b1](https://github.com/peddleon/ped-ux-react-library/commit/4d940b1fde7495befc88be75c8514b3f1de8c383))
* add SearchInput component with CSS module and stories ([3422a0c](https://github.com/peddleon/ped-ux-react-library/commit/3422a0c2f2d15ded58144113c1fb1b4f4b36e169))
* add visibleStyles class to Dropdown module CSS ([c151949](https://github.com/peddleon/ped-ux-react-library/commit/c151949bfe6823d830c4acf60dc51df4cf35b233))
* add wrapperClassName prop to SectionWrapper component ([7d613aa](https://github.com/peddleon/ped-ux-react-library/commit/7d613aa73db7b76131cc93c7a68e40c5277b5c7c))
* adjust padding in Select component CSS ([02dd3d1](https://github.com/peddleon/ped-ux-react-library/commit/02dd3d11b20fb574a813f5c61b4971182bf44f9e))
* change QR code options ([b6cc021](https://github.com/peddleon/ped-ux-react-library/commit/b6cc021e378f9a479e7684bc7348ab1d08e332d0))
* code refactor ([424589f](https://github.com/peddleon/ped-ux-react-library/commit/424589f7fbb25812813ccc139631a741d1e93235))
* dev ([946f779](https://github.com/peddleon/ped-ux-react-library/commit/946f779f92632b5cebc329ee9946708b191ec91d))
* disable auto reset for all filters in Table component ([6d9f668](https://github.com/peddleon/ped-ux-react-library/commit/6d9f668cbcd42c481c3739c34913e14c4738f434))
* fix pagination range bug in TablePagination component ([3037494](https://github.com/peddleon/ped-ux-react-library/commit/3037494c1cfe23cd6c98a361c34e6126da43ca8f))
* fix pagination range bug in TablePagination component ([4041995](https://github.com/peddleon/ped-ux-react-library/commit/404199550614659869d277394059e4b244f937f9))
* imports ([8c7c312](https://github.com/peddleon/ped-ux-react-library/commit/8c7c31274ccf104c431bdf749eb7f7f9c2304674))
* imports and update accordion styling with variables ([208ec37](https://github.com/peddleon/ped-ux-react-library/commit/208ec377f7a38f30f3914278b26b4a1f91956230))
* optimize dropdown animation and sorting labels ([97c25d9](https://github.com/peddleon/ped-ux-react-library/commit/97c25d97eab4378086af60b54df1d5139661a210))
* optimize Table component for responsive layout ([c58b4c9](https://github.com/peddleon/ped-ux-react-library/commit/c58b4c9c121843ad5027d0088ffc784f575e4116))
* qr code component refactor ([256fe03](https://github.com/peddleon/ped-ux-react-library/commit/256fe031165df3008c8365c5196278fda8f1825a))
* qrCode component event listener ([815f7ee](https://github.com/peddleon/ped-ux-react-library/commit/815f7ee6dd1756c6e9094ca6ad4eb392f358ddda))
* remove important ([fc85b4c](https://github.com/peddleon/ped-ux-react-library/commit/fc85b4c6df2565db507b18c8390d98fedcb5f01f))
* remove min-height property from paginationButton in TablePagination.module.css ([57524a0](https://github.com/peddleon/ped-ux-react-library/commit/57524a09ba5c2e87652b583f7788430d2d5314b3))
* remove openOnClick prop from Dropdown ([a799a72](https://github.com/peddleon/ped-ux-react-library/commit/a799a727fecae27ed96619b8f052b1474ad3cb32))
* remove unnecessary condition in Table component useEffect ([ae7e5dd](https://github.com/peddleon/ped-ux-react-library/commit/ae7e5dd17fcabe38ec787fae4e20c27af2404c21))
* remove unnecessary margin-right property in Tag component CSS ([935ac26](https://github.com/peddleon/ped-ux-react-library/commit/935ac26e962c62953ced6d18442587595b030b29))
* remove unused code ([fc3e9c4](https://github.com/peddleon/ped-ux-react-library/commit/fc3e9c404cffca8a676cb3947e710cc5f6649b91))
* remove unused package ([3b7073b](https://github.com/peddleon/ped-ux-react-library/commit/3b7073b426bdfad43c241ba1e7337597b3d4146c))
* revert code ([a2626fc](https://github.com/peddleon/ped-ux-react-library/commit/a2626fc92b18c55c02b2b1462bf242ba3b18dc63))
* table card margin-top to use consistent spacing ([bdae743](https://github.com/peddleon/ped-ux-react-library/commit/bdae74320a7aa456a5cacb04ca30c3d5d9d8fca1))
* table component to handle empty records and pagination visibility ([a4dacaa](https://github.com/peddleon/ped-ux-react-library/commit/a4dacaa9ca77277ee59ee1922a5101eb97f89de3))
* table sort labels for numbers to use consistent capitalization ([82348e0](https://github.com/peddleon/ped-ux-react-library/commit/82348e0b08628926db3e7635223fc421cc466bb7))
* table styles to add visual indicators for first and last columns ([407c0a1](https://github.com/peddleon/ped-ux-react-library/commit/407c0a1981693f61f6477ac95f13bde401f27a5e))
* table styles to add visual indicators for first and last columns ([0c851aa](https://github.com/peddleon/ped-ux-react-library/commit/0c851aab47705fbeab0c9bb0d694d45ba39387f7))
* table styles to add visual indicators for first and last columns ([244a59d](https://github.com/peddleon/ped-ux-react-library/commit/244a59dc6ea0a9fe38e33b32b8c39ad331e832c5))
* try fixing default export ([6a6987e](https://github.com/peddleon/ped-ux-react-library/commit/6a6987e846c1394da10ac242625fe82aa2d642a3))
* update Accordion component to add onActiveIndexChange prop ([015e399](https://github.com/peddleon/ped-ux-react-library/commit/015e399edadacd1b3b544045ab86f2afa369d296))
* update Accordion component to add option for custom plus/minus icons ([f88b8dd](https://github.com/peddleon/ped-ux-react-library/commit/f88b8ddb947d4ae4eca90982c4c3ff95383e4cb6))
* update Accordion component to add option for custom right element ([985774f](https://github.com/peddleon/ped-ux-react-library/commit/985774f33accd8d7678d3ed9d71b098345b82ce4))
* update Accordion component to add option for custom right element and activeIndex prop ([ec0e5f4](https://github.com/peddleon/ped-ux-react-library/commit/ec0e5f4c1b89231a20367c46226b9bba307de249))
* update base.css with margin and padding utility classes ([6efe01e](https://github.com/peddleon/ped-ux-react-library/commit/6efe01ea700155fcd354d7afdc89ba88bfafe41d))
* update Divider component to accept custom className prop ([db09d07](https://github.com/peddleon/ped-ux-react-library/commit/db09d0726fa738a171b1dfff5c24cccd5ac35931))
* update Divider component to accept custom className prop ([3875d09](https://github.com/peddleon/ped-ux-react-library/commit/3875d09af79b97fbb91229b8bfcc23e705d09e04))
* update package version ([6a0c4e6](https://github.com/peddleon/ped-ux-react-library/commit/6a0c4e68ff85894ebc3860f3782576660b25d0bc))
* update PaginationRangeButtons component in TablePagination module ([6a7e55a](https://github.com/peddleon/ped-ux-react-library/commit/6a7e55a8061a987a45d48003b5417967d16100e1))
* update PaginationRangeButtons component in TablePagination module ([0eeac46](https://github.com/peddleon/ped-ux-react-library/commit/0eeac46b4604bafafaefef73478d45d23da8bcea))
* update PaginationRangeButtons component in TablePagination module ([586ef7e](https://github.com/peddleon/ped-ux-react-library/commit/586ef7e03b67e4cdaf41a7449fe087323af6f7a1))
* update SectionTitle component CSS to align actions with flexbox ([119c1bd](https://github.com/peddleon/ped-ux-react-library/commit/119c1bd9d11a8ec3bef0dc13f847a6c70d10960b))
* update table card margin-top and spacing ([6413647](https://github.com/peddleon/ped-ux-react-library/commit/6413647511324b95d5b97e2462948c7916bcb5df))
* update Table component with useWindowSize hook and improve pagination logic ([c75165e](https://github.com/peddleon/ped-ux-react-library/commit/c75165e7d89b8c45b8604ef630ae6ac738f5c0fd))
* update Table component with useWindowSize hook and improve pagination logic ([79a1190](https://github.com/peddleon/ped-ux-react-library/commit/79a1190f907b3a2e70d0121c2e2b659001a70776))
* update Table module CSS and Table component ([411dc3b](https://github.com/peddleon/ped-ux-react-library/commit/411dc3b2a6f925e40aa24d2d6c1a2b761a5c5b5d))
* update TableCard component to add isLoading prop ([a5ba7fa](https://github.com/peddleon/ped-ux-react-library/commit/a5ba7fad9a5c7b63f0c2a117b912a2a96d1aa280))
* update TableCard component to add isLoading prop and useRef hook ([173d854](https://github.com/peddleon/ped-ux-react-library/commit/173d854bdcea48b6a20835adacbc57c29659b496))
* update TableCard component to add isLoading prop and useRef hook ([dce2ace](https://github.com/peddleon/ped-ux-react-library/commit/dce2ace4892e5352bd963fb9fe9e9d01c760d482))
* update TablePagination component ([8b6d0ef](https://github.com/peddleon/ped-ux-react-library/commit/8b6d0ef8f67ea75e762e91b509bda33bd2ea9a68))
* update TablePagination component to fix pagination range bug ([8fd4969](https://github.com/peddleon/ped-ux-react-library/commit/8fd4969bd108d3266465f010f5c3d1742151fcd9))
* update TablePagination component to fix pagination range bug ([2cdfdf1](https://github.com/peddleon/ped-ux-react-library/commit/2cdfdf16f6758313c204016a58c151878bc9fc76))
* update TablePagination component to reset page when changing page size ([56347fb](https://github.com/peddleon/ped-ux-react-library/commit/56347fbdb1bd26289b30845fe044455ac3b60777))

## [1.11.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.10.0...v1.11.0) (2024-10-04)


### Features

* [UX-163] added multi select features ([26f2b84](https://github.com/peddleon/ped-ux-react-library/commit/26f2b8400bd625f86ee233ee8c94622622f97e61))
* [UX-163] make it break free by adding optional chaining ([54d5c15](https://github.com/peddleon/ped-ux-react-library/commit/54d5c15c03ec1f9656090d7befe9e2859f248c54))
* [UX-176] added form element to wrapper ([aa8132f](https://github.com/peddleon/ped-ux-react-library/commit/aa8132f85ce7354d12249ab1042fa1a289a3463e))
* [UX-176] prevent auto submit ([42312a1](https://github.com/peddleon/ped-ux-react-library/commit/42312a1160f057ff94a15e61455fb70335a49b39))
* [UX-176] test ios safari issue ([6801fbe](https://github.com/peddleon/ped-ux-react-library/commit/6801fbea2e63cf2b4fc632ad19fef2696eb4a933))
* improve TablePagination component ([fa979f7](https://github.com/peddleon/ped-ux-react-library/commit/fa979f7cf440e8d8c1b13b637f1c1ffa4bca7189))


### Bug Fixes

* [UX-176] import issue ([d0187c1](https://github.com/peddleon/ped-ux-react-library/commit/d0187c1df7716da6d029506e659c0cd2928d8f65))
* [UX-176] import issue ([bfefa9a](https://github.com/peddleon/ped-ux-react-library/commit/bfefa9a275e3674e9e6c4d67fb75f0e6329c476c))
* [UX-176] ref issue ([a8c2a9b](https://github.com/peddleon/ped-ux-react-library/commit/a8c2a9b292af42aea29396bbf00257dd84852e05))


### Miscellaneous

* optimize Table component for responsive layout ([8f8ff2b](https://github.com/peddleon/ped-ux-react-library/commit/8f8ff2ba1de1932047d7477a28d0f51f74324291))
* optimize TablePagination component for responsive layout ([cfe1d85](https://github.com/peddleon/ped-ux-react-library/commit/cfe1d856ef5a786a71bd134f0591269c1432ee0a))
* optimize TablePagination component for responsive layout ([c44d3be](https://github.com/peddleon/ped-ux-react-library/commit/c44d3beb2e17dbd26f2b2df76e1efaf8cdc1e7f9))
* optimize TablePagination component for responsive layout ([7e82e89](https://github.com/peddleon/ped-ux-react-library/commit/7e82e89e6460923d7f7508c822933cd185df833f))
* update Button and TablePagination components ([a237180](https://github.com/peddleon/ped-ux-react-library/commit/a237180e799bff8e27ebf164e8e8117b9c56c02f))
* update Table component with paginationDetails prop ([db87358](https://github.com/peddleon/ped-ux-react-library/commit/db87358cab714e8cd7cd6c622b050cb2c2c39f79))
* update Table component with useWindowSize hook ([1df268d](https://github.com/peddleon/ped-ux-react-library/commit/1df268d6ee3163ddeea05f2657a1bc2c16b07b73))
* update Table data generation and pagination logic ([225dd52](https://github.com/peddleon/ped-ux-react-library/commit/225dd521b247f2e076660787340aac811ae21ceb))
* update Table data generation and pagination logic ([493f85b](https://github.com/peddleon/ped-ux-react-library/commit/493f85b7a96b45c7d8cb018abecc091b38a6205e))
* update Table data generation and pagination logic ([e8f43f6](https://github.com/peddleon/ped-ux-react-library/commit/e8f43f69a2d902c68157156268bc5e95b2682442))
* update Table data generation and pagination logic ([5ae1518](https://github.com/peddleon/ped-ux-react-library/commit/5ae1518f86aa6f72ee55383b2a75fe38dcd80539))
* update Table module CSS and Table component ([a97a0b8](https://github.com/peddleon/ped-ux-react-library/commit/a97a0b8c57ccd28f1e3cc2edf2ce3ec67f396810))
* update Table module CSS and Table component styles ([bf51408](https://github.com/peddleon/ped-ux-react-library/commit/bf51408bb4798466052b4054687def5f5ba8efac))
* update TablePagination component ([d1cfff4](https://github.com/peddleon/ped-ux-react-library/commit/d1cfff4c9a8b893cadc41576f91951bbdc30914a))
* update TablePagination styles for responsive layout ([ec70b34](https://github.com/peddleon/ped-ux-react-library/commit/ec70b34a5ed50bf5f46f874b1e28f776abbf982f))
* update TablePagination styles for responsive layout ([7b184a0](https://github.com/peddleon/ped-ux-react-library/commit/7b184a09a43868ff517c86c9a766256588137165))

## [1.10.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.9.0...v1.10.0) (2024-09-24)


### Features

* [UX-169] Explore ways to define Better guidelines for UX component ([39e964a](https://github.com/peddleon/ped-ux-react-library/commit/39e964ad4843e01bace166a369329838f2fbe1bf))
* [UX-176] add reset functionality in form ([b8227f9](https://github.com/peddleon/ped-ux-react-library/commit/b8227f9ceb206a773687d57bb53d81f641a8db66))
* [UX-176] added pattern props ([eb85faa](https://github.com/peddleon/ped-ux-react-library/commit/eb85faa84e04ff4017ac79d73b6e2d179aab79a5))
* [UX-176] fixed reset state issue ([60950d8](https://github.com/peddleon/ped-ux-react-library/commit/60950d80dd0b63427c2feb3d18005b4d62cec206))
* [UX-176] fixed state update changed rapidly ([72bd12c](https://github.com/peddleon/ped-ux-react-library/commit/72bd12ccc2a85776c4424ceb3a1edeac9991d84d))
* [UX-176] provide fallback to var to fix older safari isssue ([4f0c5e9](https://github.com/peddleon/ped-ux-react-library/commit/4f0c5e9bf686694b6257e46396683d56303792a9))
* [UX-176] provide isValid prop to check validity ([0b6646b](https://github.com/peddleon/ped-ux-react-library/commit/0b6646ba7bed90534c70d8c337e7b9ddc502267e))
* [UX-176] refactored reset issue ([3cc09f6](https://github.com/peddleon/ped-ux-react-library/commit/3cc09f6cd902b07f7743fc1bf234987d70c18e6d))
* [UX-176] refactored useForm ([54c16dc](https://github.com/peddleon/ped-ux-react-library/commit/54c16dcb51caf477edd8ebca0d1f1eb930bd0da4))
* [UX-176] set reset as previous ([fac3ed0](https://github.com/peddleon/ped-ux-react-library/commit/fac3ed097a46ca33847e1a97cdf35e375301eacf))
* [UX-176] update disable select color ([674d306](https://github.com/peddleon/ped-ux-react-library/commit/674d306781a07f34ff923845f14a59000965cf5c))
* [UX-176] update disabled input color ([8f1a037](https://github.com/peddleon/ped-ux-react-library/commit/8f1a037ed4ae95bf93084857a75f6b8d7c2deb03))
* [UX-176] update eachFieldValid validation ([57f189c](https://github.com/peddleon/ped-ux-react-library/commit/57f189cd17186a540afdcce4144abc3334598967))
* [UX-176] updated max length validation ([be57d9f](https://github.com/peddleon/ped-ux-react-library/commit/be57d9fde61d060ed67122a80aed50261e078c7f))


### Bug Fixes

* [UX-176] add console to debug ([578151f](https://github.com/peddleon/ped-ux-react-library/commit/578151f9c0166a159a511065084ca32d77c55c30))
* [UX-176] error messages issue ([0d08bfa](https://github.com/peddleon/ped-ux-react-library/commit/0d08bfa72f6af85527211abc3fbcbbe918333016))
* [UX-176] error reset issue ([0977e9c](https://github.com/peddleon/ped-ux-react-library/commit/0977e9cdef0f054807d7c64bc6e50a88e11d2763))
* [UX-176] exlcudes icon lib from build ([f7c0364](https://github.com/peddleon/ped-ux-react-library/commit/f7c03644e593cc7735d65ff201a440260efd1dd1))
* [UX-176] import var issue ([b62e5f8](https://github.com/peddleon/ped-ux-react-library/commit/b62e5f836fbe9425dbf85937b5e98e1810f02c56))
* [UX-176] removed unnessary argument from reset ([888e44a](https://github.com/peddleon/ped-ux-react-library/commit/888e44a722aec66efa0869adcdfd1791c03627c4))
* form state reset issue ([0bd8fa8](https://github.com/peddleon/ped-ux-react-library/commit/0bd8fa8178cd4d3e71248059ade0974cb7756e3f))


### Miscellaneous

* move story and control to first ([b482c11](https://github.com/peddleon/ped-ux-react-library/commit/b482c11a39c8ac61b4089e27cc2d906ff13e6773))
* security md file ([ed7d010](https://github.com/peddleon/ped-ux-react-library/commit/ed7d0105ab085f428fbc8759db02ee5af78f2419))

## [1.9.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.8.0...v1.9.0) (2024-09-06)


### Features

* [UX-164] created phone input and zip input component ([46e94fb](https://github.com/peddleon/ped-ux-react-library/commit/46e94fbf4bcf9c0d630e1cf1971f04ee61f028de))
* [UX-164] removed masking package and fixed the issue with firefox ([5c6b275](https://github.com/peddleon/ped-ux-react-library/commit/5c6b27548576c726afe36d03c2ee3cabdc4f2178))
* [UX-164] update storybook for zip and phone input ([e248eea](https://github.com/peddleon/ped-ux-react-library/commit/e248eeacf888a4d36078be5a0b0102e87eb93447))
* **ErrorBoundary:** [UX-160] created the error boundary component ([39c9ccf](https://github.com/peddleon/ped-ux-react-library/commit/39c9ccf42308e3891b9f4bc14881e9a5672646fe))


### Bug Fixes

* [UX-164] issues with google place search, phoneinput and useForm ([a9710fd](https://github.com/peddleon/ped-ux-react-library/commit/a9710fd2df3a1b367fcfeeeaf01720e17f6fe2ed))


### Miscellaneous

* **Index:** import errorboundary ([6986686](https://github.com/peddleon/ped-ux-react-library/commit/6986686549090ea4b6c5f3b16f03459a02820db0))
* update documentation for storybook ([4731446](https://github.com/peddleon/ped-ux-react-library/commit/473144683cb80ebeed4b30b928473e540e13b1bf))

## [1.8.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.7.0...v1.8.0) (2024-08-08)


### Features

* [UX-149] integrated landscape mode with app layout ([063d23a](https://github.com/peddleon/ped-ux-react-library/commit/063d23a29675226213dca1de56ad2bf5da105d0f))
* **rollup:** [UX-149] integrated landscape mode with app layout ([0e887eb](https://github.com/peddleon/ped-ux-react-library/commit/0e887eb009b990d7c312d46503996c48a057a1c7))


### Miscellaneous

* **rollup:** fix json file issue ([5dbec84](https://github.com/peddleon/ped-ux-react-library/commit/5dbec84a293d78b0ff3ee10afa7f562f5c992a92))

## [1.7.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.6.1...v1.7.0) (2024-07-26)


### Features

* [UX-143] update select and select search for the mobile view fix ([671fa1f](https://github.com/peddleon/ped-ux-react-library/commit/671fa1f9c43f35d3eb0c4dfab6aaa6e82f43a300))
* added inline data list component ([eb4d314](https://github.com/peddleon/ped-ux-react-library/commit/eb4d314625fedbb17ba30f32bc1ba64e5754941d))
* image carousel component added ([0fdbd29](https://github.com/peddleon/ped-ux-react-library/commit/0fdbd29fcb9e57f45e0628cb7bbaa89528e3a0d7))
* **Input:** allow readonly mode ([a2ec5f0](https://github.com/peddleon/ped-ux-react-library/commit/a2ec5f070a253034b5873d7197eef8578aed6048))
* provided callback for active list item carousel ([3a520b9](https://github.com/peddleon/ped-ux-react-library/commit/3a520b90c393843cdcabddf72632c3a67f2c5c8f))


### Bug Fixes

* removed the callbacck ([1da2ab9](https://github.com/peddleon/ped-ux-react-library/commit/1da2ab9f58a5b4e053c8e4dc55afbb24d03d8bd7))
* set default mobile menu modal open to false ([f251ebd](https://github.com/peddleon/ped-ux-react-library/commit/f251ebd49540669b5d77a7698cedf6ccd7f71de5))


### Miscellaneous

* base styling ([e1662f6](https://github.com/peddleon/ped-ux-react-library/commit/e1662f6411d97bdb5d00cdb5cf85ac676ca0f24b))
* **cardView:** data ([7147596](https://github.com/peddleon/ped-ux-react-library/commit/71475964de9e37df8abca801d533a46d6b8d6aa5))
* carousel component ([995df32](https://github.com/peddleon/ped-ux-react-library/commit/995df327c9908dbbc7e3c2b59cf1230e87066f86))
* change onlcik ([8ed3693](https://github.com/peddleon/ped-ux-react-library/commit/8ed369348e2415d8b43e73445e450ddc9d3f367b))
* copy clipboard callback ([c801e82](https://github.com/peddleon/ped-ux-react-library/commit/c801e828b6ef6203e2a6d7e3be7a211066f70989))
* copy clipboard callback ([62f8b03](https://github.com/peddleon/ped-ux-react-library/commit/62f8b03d42b22f925a61510095a153934fc4e096))
* default export for component ([7df7134](https://github.com/peddleon/ped-ux-react-library/commit/7df71342942b4f04cb0c60f13ee1da25ff895397))
* distance b/w inline list ([27b1482](https://github.com/peddleon/ped-ux-react-library/commit/27b1482653d0f064d321ce3702860dc1df42da5f))
* dropdown component ([50f5431](https://github.com/peddleon/ped-ux-react-library/commit/50f5431ff5b9588ecd6fa92de77cd84a61593e3e))
* dropdown component where added the propgation ([3d58c2e](https://github.com/peddleon/ped-ux-react-library/commit/3d58c2eae53049d046725ed8c5a71aa65afaff2a))
* **Dropdown:** file placement ([6358288](https://github.com/peddleon/ped-ux-react-library/commit/63582888ee903ea1e71dbf250786ff2bd0a525e7))
* **Dropdown:** file placement ([0114c34](https://github.com/peddleon/ped-ux-react-library/commit/0114c34dbac35c97cce9fd7dccd0eb5662a64185))
* **Dropdown:** file placement ([96edab3](https://github.com/peddleon/ped-ux-react-library/commit/96edab38f881b4b00e6d41068edfa4bd64e8ff9d))
* **eslintrc:** file ([90ca46a](https://github.com/peddleon/ped-ux-react-library/commit/90ca46aaa473b5be083b53317c9c8766d4feaed8))
* export relative path ([76186bd](https://github.com/peddleon/ped-ux-react-library/commit/76186bd706159c1567d159c3db462557c458bb55))
* exported the required components ([7091843](https://github.com/peddleon/ped-ux-react-library/commit/7091843ca69339a8790cb6d51a138cc7222e65b0))
* exported the required components ([2488bbc](https://github.com/peddleon/ped-ux-react-library/commit/2488bbc914752d8d11556f177979f2000513203d))
* **ImageCarousel:** styles for center align ([41a19bc](https://github.com/peddleon/ped-ux-react-library/commit/41a19bc19fd6284a9bf39ba53dbf9342eedae8b2))
* **package.json:** file ([6867528](https://github.com/peddleon/ped-ux-react-library/commit/686752892053a3ba25e64dc127d160d835bff5ff))
* provided callback carousel ([f0685b3](https://github.com/peddleon/ped-ux-react-library/commit/f0685b35e10065637708209d25bdda1a37170bd1))
* provided margin ([48116bf](https://github.com/peddleon/ped-ux-react-library/commit/48116bf9eff5242384181e19b30cb40a208a5e3c))
* provided prop for click ([a1be434](https://github.com/peddleon/ped-ux-react-library/commit/a1be4345aaa094ecbf582a1d591860596eb9f9b8))
* remove image carousel ([b7f226d](https://github.com/peddleon/ped-ux-react-library/commit/b7f226d5414a77e372ba42c4896d8b17e61a9d3f))
* table comoponent props ([02bab8e](https://github.com/peddleon/ped-ux-react-library/commit/02bab8e4063b2b9202142c177c17f1bd5a8d2144))
* **tablecard:** position for table card ([30e642f](https://github.com/peddleon/ped-ux-react-library/commit/30e642fbae54d64d3e3eb766ea9d38353027b0ea))
* **TableCard:** provided card types and support for dropdown ([9e1a4bb](https://github.com/peddleon/ped-ux-react-library/commit/9e1a4bb173b186d2b1346051c7bc6477c0af51c0))
* **Tablecard:** styles changes ([5f6543b](https://github.com/peddleon/ped-ux-react-library/commit/5f6543bc0437719a03166e33525cc73cc66541cf))
* **tableHeader:** remove default uppercase for header ([1571069](https://github.com/peddleon/ped-ux-react-library/commit/15710696577bf89748bc9f027a3e4f8706fb6791))
* **table:** sorting header style issue ([5eda85e](https://github.com/peddleon/ped-ux-react-library/commit/5eda85ed7bc6b2ad188f6a511c3133a381caa41c))
* **table:** sorting header style issue ([a52e700](https://github.com/peddleon/ped-ux-react-library/commit/a52e70018c80dbf6dd73d5c18d5c25b23a837c0d))
* **TableText:** hide success check on copy ([fd7ebd1](https://github.com/peddleon/ped-ux-react-library/commit/fd7ebd1932b07c7b45852a2d1ea4905ec5b5d394))

## [1.6.1](https://github.com/peddleon/ped-ux-react-library/compare/v1.6.0...v1.6.1) (2024-07-16)


### Bug Fixes

* css for the nav bar ([a27f62f](https://github.com/peddleon/ped-ux-react-library/commit/a27f62f914a6e8f7b2fa72b6b97ef2ee938a7812))

## [1.6.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.5.0...v1.6.0) (2024-07-12)


### Features

* [PUB-3485] fixed scroll issue while card in a landscape mode ([fa0d08f](https://github.com/peddleon/ped-ux-react-library/commit/fa0d08ff646e948937ea78bad0d27cef67a02a27))
* add toggle button ([1c8288c](https://github.com/peddleon/ped-ux-react-library/commit/1c8288c79ae91d855d2b3f8208f0f26df5e17430))


### Bug Fixes

* [UX-128] close icon in dialog ([9e7bf78](https://github.com/peddleon/ped-ux-react-library/commit/9e7bf7832b31bfedb53a9c2390c3dd9ad64f7eca))
* [UX-128] toast animation is not working in the safari ([3ac0bbe](https://github.com/peddleon/ped-ux-react-library/commit/3ac0bbe2e5d92086f4d5363f58e2671b038d73c5))
* [UX-128] toast animation issue in safari browser ([2102f0e](https://github.com/peddleon/ped-ux-react-library/commit/2102f0e241ffc6e9da4f8ae02966651cc50348d7))
* **CodeBlock:** issue with the styles ([a0b730f](https://github.com/peddleon/ped-ux-react-library/commit/a0b730f86d27419cf5fb4621437f960251b3dbd4))
* **Dropdown:** closing issue with the dropdown ([191a61c](https://github.com/peddleon/ped-ux-react-library/commit/191a61c7bf8b98c0995c4b91382eb918d4455b33))
* menu link click issue ([d6799c2](https://github.com/peddleon/ped-ux-react-library/commit/d6799c29084664f9dcbc2dbb9d17a922b7b365c3))
* navigation issue with link and mobile devices ([22fa025](https://github.com/peddleon/ped-ux-react-library/commit/22fa025dcf3b7871b171441951c1750805497c6d))
* navigation issue with link and mobile devices ([1dd4add](https://github.com/peddleon/ped-ux-react-library/commit/1dd4add8af844dd31d5840b926ebb33bb2842c12))
* navigation issue with link and mobile devices ([13873f5](https://github.com/peddleon/ped-ux-react-library/commit/13873f54f7b0fcb1c7e110b3ca40b924d7f3fa85))
* navigation issue with link and mobile devices ([39b3d7c](https://github.com/peddleon/ped-ux-react-library/commit/39b3d7ce40a62c671d595d97b607acb4198de6e1))
* navigation issue with link and mobile devices ([7ed8731](https://github.com/peddleon/ped-ux-react-library/commit/7ed8731b19795ee36d7565fa49f46b37cccffa88))
* navigation issue with link and mobile devices ([a7f6fbe](https://github.com/peddleon/ped-ux-react-library/commit/a7f6fbe6b826136e9088ca340d05dc29d1b98bfe))
* navigation issue with link and mobile devices ([ce66cda](https://github.com/peddleon/ped-ux-react-library/commit/ce66cda8b541fbca1edd5650f9c904bc5f7d8559))
* pagination and the codeblock style issue ([ba9f4cc](https://github.com/peddleon/ped-ux-react-library/commit/ba9f4cc93977bb636cb8e1d4d8195cefcbb86e7c))
* remove max-width from typography ([1966a51](https://github.com/peddleon/ped-ux-react-library/commit/1966a511eeb0f126bc84a73660c68d4effadb69e))
* scrim issue with the table and model ([117184f](https://github.com/peddleon/ped-ux-react-library/commit/117184f2489e8f84f5d48fd31d9f41ba238fe61e))
* scrim issue with the table and model ([e31de82](https://github.com/peddleon/ped-ux-react-library/commit/e31de82cca02a370fefbf6c59ccd14d077c80da7))
* **SelectInput:** styling with the select input ([5374dd7](https://github.com/peddleon/ped-ux-react-library/commit/5374dd780ac1a479fe6a219439182c0848464641))
* styling with app ([2eb86ce](https://github.com/peddleon/ped-ux-react-library/commit/2eb86ce6228724d5382b20169c4a17f4cd1a4f76))
* table scrims ([ade5332](https://github.com/peddleon/ped-ux-react-library/commit/ade53321b705dfecf1f983fe6c365b44a0c6991f))


### Miscellaneous

* [UX-141] fix ui issues ([5de8d6e](https://github.com/peddleon/ped-ux-react-library/commit/5de8d6eb9f5b0cb02aecef9b27e5cbc81f346498))
* added props for the masked text ([d3c7837](https://github.com/peddleon/ped-ux-react-library/commit/d3c7837706e033b478ba76911c13ba4f87861a4b))
* **ApplicationLayout:** height and width ([e399eea](https://github.com/peddleon/ped-ux-react-library/commit/e399eea7fa5ba56bcf8eaa540a866e7028fed0f3))
* **ApplicationLayout:** height and width ([ab2f9c6](https://github.com/peddleon/ped-ux-react-library/commit/ab2f9c6144c75a57cd9d23de77a624b652eabe82))
* **ApplicationLayout:** height and width ([039c8aa](https://github.com/peddleon/ped-ux-react-library/commit/039c8aabf27088a4d55e061f6533bdc76805f1cf))
* **ApplicationLayout:** height and width ([9d92472](https://github.com/peddleon/ped-ux-react-library/commit/9d924729ce258e78b827747249bb2cc4a4dd4738))
* **ApplicationLayout:** height and width ([73889e8](https://github.com/peddleon/ped-ux-react-library/commit/73889e86aa0a4404f534519b1ae17b8e49077396))
* common styles for multiwrapper ([cdff2cb](https://github.com/peddleon/ped-ux-react-library/commit/cdff2cb3ecdb36c1ffbb3dec65305b2fd5c3b605))
* **Dropdown:** fix position for the dropdown ([45fc5d1](https://github.com/peddleon/ped-ux-react-library/commit/45fc5d110456782b23523ed9fa445dd139dda0ad))
* export multisectionwrapper ([e5da891](https://github.com/peddleon/ped-ux-react-library/commit/e5da891f68ee2994116ce087f94bb4ae1c60f9e2))
* fix icon and loading issue with app layout ([8dc8917](https://github.com/peddleon/ped-ux-react-library/commit/8dc89170d494575c781b14a3a1ea4b9bc21f62e8))
* fix issue with the codeblock padding ([06be3e5](https://github.com/peddleon/ped-ux-react-library/commit/06be3e5c526008ec8c1a312270cac35d13a6841d))
* fix table click on more menu ([0205ff8](https://github.com/peddleon/ped-ux-react-library/commit/0205ff8bdff720792c17e0f6c0fb55cc8dfc93ed))
* mobile multiwraper ([0370fc0](https://github.com/peddleon/ped-ux-react-library/commit/0370fc017b9c4bdf2ba886100d6c72fd22f4b43e))
* model body styles ([c88ce84](https://github.com/peddleon/ped-ux-react-library/commit/c88ce8442b394545e5ea8eed73aee2988dae485f))
* navigation bar ([b17fb4e](https://github.com/peddleon/ped-ux-react-library/commit/b17fb4e48565796fd752fe9cbe4efb755095c9a7))
* **NavigationBar:** added hidden in the body when menu open ([d09129c](https://github.com/peddleon/ped-ux-react-library/commit/d09129c7345372918c0017a6d554c849b50dd7a6))
* table component ([5585c4f](https://github.com/peddleon/ped-ux-react-library/commit/5585c4fd6c90f6373c069aa6ac533e8baea0e0a2))
* **Table:** card view and navigation issue ([e94e765](https://github.com/peddleon/ped-ux-react-library/commit/e94e765292328e72ffc8cc9f22c3c4682465a516))
* **TableText:** max len issue with masked text ([6f3e516](https://github.com/peddleon/ped-ux-react-library/commit/6f3e516c382f43cfc6c477c9e26edffa5386fdf6))
* tooltip and dropdown extra props ([6c2dd78](https://github.com/peddleon/ped-ux-react-library/commit/6c2dd78d3af3d738cd3af007840e618571de23c7))
* tooltip and dropdown extra props ([31762ad](https://github.com/peddleon/ped-ux-react-library/commit/31762ad41904d0de5ae5726dd286ac585db4f678))
* tooltip and dropdown extra props ([693e6c0](https://github.com/peddleon/ped-ux-react-library/commit/693e6c0408f85a62126a70a868975645a6b95d0d))

## [1.5.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.4.0...v1.5.0) (2024-06-27)


### Features

* [UX-115] solve dependa bot ([9dbd0cc](https://github.com/peddleon/ped-ux-react-library/commit/9dbd0cc15a6fbf282884a89a4a1ffec9a14ba66a))


### Bug Fixes

* [UX-121] spacing in offer details tile ([bf6e27c](https://github.com/peddleon/ped-ux-react-library/commit/bf6e27c68046c0ce0fb7b3303abbacdad298a2e6))
* [UX-123] border radius issue in navbar ([43d8690](https://github.com/peddleon/ped-ux-react-library/commit/43d8690248ffd9607378f7e63b284f321924b56c))
* storybook deploy issue ([a5c0188](https://github.com/peddleon/ped-ux-react-library/commit/a5c01884de953d2ccd97a16c4c12a730f3d51665))

## [1.4.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.3.0...v1.4.0) (2024-06-13)


### Features

* created table card component ([91abdb4](https://github.com/peddleon/ped-ux-react-library/commit/91abdb47fcd6a87bd02b8571830d008a1796ae8e))


### Bug Fixes

* handle click for dropdown ([9e77f5f](https://github.com/peddleon/ped-ux-react-library/commit/9e77f5ff46017131206b16526ee8ab7e0570f826))
* important styling for disabled buttons and theme ([2efe359](https://github.com/peddleon/ped-ux-react-library/commit/2efe359ecc37762846fc0495293a300f0fc2b4a1))
* navigationbar issue with the styles ([b8b4b3c](https://github.com/peddleon/ped-ux-react-library/commit/b8b4b3ccb7649c99488b933cfbe671565fd05e71))
* provided the dropdown props for change ([23f591c](https://github.com/peddleon/ped-ux-react-library/commit/23f591c4836f62acd740bbd63b49dbc8e0396da7))
* storybook block issue ([ca1553e](https://github.com/peddleon/ped-ux-react-library/commit/ca1553eff7f59f6cfc3d26839fdd3fd5fe6df486))


### Miscellaneous

* add the library which was having issue ([956cf4b](https://github.com/peddleon/ped-ux-react-library/commit/956cf4bcb75d4948bab1e4583404c8bf72a0e6b9))
* added extra props onClickOutSideNavigation ([049cc4d](https://github.com/peddleon/ped-ux-react-library/commit/049cc4d2711f0d2c01e8fd7c27418a6ac3af3878))
* added flag for show/hide footer in table ([b388278](https://github.com/peddleon/ped-ux-react-library/commit/b388278ca3e5d5e276a64a9cb08bb1f782f56ecf))
* delete table card ([dc6266f](https://github.com/peddleon/ped-ux-react-library/commit/dc6266f177ddcdc82fe28f8550abc8d09bd990d9))
* remove the library which was having issue ([70b9027](https://github.com/peddleon/ped-ux-react-library/commit/70b9027c311b6979cadf81fe343cac6e7da2435e))
* styles and the props for navigation bar ([41e5da1](https://github.com/peddleon/ped-ux-react-library/commit/41e5da15961e3a3d900708ae677f55e0be032af2))

## [1.3.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.2.0...v1.3.0) (2024-06-03)


### Features

* [UX-108] added test cases ([a4a53a6](https://github.com/peddleon/ped-ux-react-library/commit/a4a53a686b629ae6b5110dd868458aba33736383))
* [UX-108] created google search api hook and component ([a4c2777](https://github.com/peddleon/ped-ux-react-library/commit/a4c2777ac20ecb8b30ee0a07acf0d9042b2d9eb6))
* [UX-108] made changes for powered by google flag ([9a0a03f](https://github.com/peddleon/ped-ux-react-library/commit/9a0a03f6d442bae307947391793b5637a5d4e78f))
* [UX-108] provide onInputChange, onFocus, onBlur events ([557a0a6](https://github.com/peddleon/ped-ux-react-library/commit/557a0a6e55181c5cd99c4f3b00416580abcbb152))
* [UX-45] create form component ([1c715c0](https://github.com/peddleon/ped-ux-react-library/commit/1c715c098fa2308c775b1a1e79faef097c7c8a60))
* add form hook ([4d806ac](https://github.com/peddleon/ped-ux-react-library/commit/4d806ac70cf29bb7ef1c25ae857f65a88949c597))
* expose extra prop for padding in page layout ([e4f7683](https://github.com/peddleon/ped-ux-react-library/commit/e4f768397883faae5bc4768e31ee70bc964d7697))


### Bug Fixes

* [UX-108] refactored css ([6a54edd](https://github.com/peddleon/ped-ux-react-library/commit/6a54edd5b8a2c9dabd979d51d39ca2ce65ab949a))
* change listener for the mobile navigation ([d1b4dd7](https://github.com/peddleon/ped-ux-react-library/commit/d1b4dd7c8ec52e8bff1dd8afa898eab7463073d1))
* form imports ([27601a5](https://github.com/peddleon/ped-ux-react-library/commit/27601a5859f5a656d922129f67661ae07f0d5bd2))
* toast position issue ([b43e786](https://github.com/peddleon/ped-ux-react-library/commit/b43e7864d57428ff98c7a504298a939f1e3d23bc))
* toast position issue ([d788534](https://github.com/peddleon/ped-ux-react-library/commit/d788534cccc96305800a4e66e898a9bc9e8f322f))
* typography issue ([d896930](https://github.com/peddleon/ped-ux-react-library/commit/d8969300081f373b7803687a7f9bbed7e4d659f0))


### Miscellaneous

* remove unused css ([484cc9e](https://github.com/peddleon/ped-ux-react-library/commit/484cc9e45d0aaee409664d4b0e214c903a133856))

## [1.2.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.1.0...v1.2.0) (2024-05-16)


### Features

* [UX-103] fixed date view component active issue ([3a11eef](https://github.com/peddleon/ped-ux-react-library/commit/3a11eefd2d06cf39a1180d5c091ced0f23fca884))
* [UX-103] fixed date view hover issue ([d880dd2](https://github.com/peddleon/ped-ux-react-library/commit/d880dd215949389568f567522ada28af6184bf35))
* [UX-103] fixed date view hover issue ([f34a875](https://github.com/peddleon/ped-ux-react-library/commit/f34a875eeb3f206ffd595754d7bb58814437c91b))
* [UX-103] update components for action links ([abbaff5](https://github.com/peddleon/ped-ux-react-library/commit/abbaff5230a504c145f9b1b10a17d553fd659215))
* add support for the custom navigation menu ([bc94db3](https://github.com/peddleon/ped-ux-react-library/commit/bc94db32645456344e5cd51806a8b101acf3623d))
* add support for the custom navigation menu ([fdbc9e7](https://github.com/peddleon/ped-ux-react-library/commit/fdbc9e79b6c3bf7553f972d73c0af2b2794b2df5))


### Bug Fixes

* [UX-103] update padding desktop page layout ([6152d1e](https://github.com/peddleon/ped-ux-react-library/commit/6152d1ec7aefd6121400e8bd44d35cd6374386a6))
* [UX-103] update padding mobile page layout ([7fc1edf](https://github.com/peddleon/ped-ux-react-library/commit/7fc1edf832b86000399c6158fbf266258c8b5333))
* console errors with the components and props ([37d8c7b](https://github.com/peddleon/ped-ux-react-library/commit/37d8c7bd1200e24d882708c3e06f1e9aaa93aeca))
* lint error ([884b39c](https://github.com/peddleon/ped-ux-react-library/commit/884b39c0cc50138b8767a582988994fd1d436f78))
* test cases for components ([6b3d4a9](https://github.com/peddleon/ped-ux-react-library/commit/6b3d4a963c18dacab078f1c53f44b39bf3ab8dcd))
* test cases for components ([6161f43](https://github.com/peddleon/ped-ux-react-library/commit/6161f430925d7eb09eae06bf7f3d5c877d86081a))


### Miscellaneous

* app-layout background color issue ([70211c3](https://github.com/peddleon/ped-ux-react-library/commit/70211c36ca210e73607e1dc6b12805bad0f0e2a0))
* application layout children props ([88e7afa](https://github.com/peddleon/ped-ux-react-library/commit/88e7afa16cb7d697ea486dc774f53e6e0be66cbf))
* **ApplicationLayout:** provided support for background color on sticky nav ([1953608](https://github.com/peddleon/ped-ux-react-library/commit/19536089356c3cb7e1790b1d8f61d296e806d6fb))
* **ApplicationLayout:** provided support for click ([2ac28a8](https://github.com/peddleon/ped-ux-react-library/commit/2ac28a89864d4c3fee703421256fe9b58f61cce7))
* detailed cta exports ([eaba706](https://github.com/peddleon/ped-ux-react-library/commit/eaba70682c12072a3dd33b1637ee0ad86db1c2a8))
* **DetailedCTA:** component to allow body in terms of child ([d4eb3e1](https://github.com/peddleon/ped-ux-react-library/commit/d4eb3e1525161c5fc31ce676742fcf0bb4638d1c))
* following components ([12d4152](https://github.com/peddleon/ped-ux-react-library/commit/12d415202666f185bfec8e02d871d7f8d7d7ddda))
* formatting and documentation for the components ([1b26b5f](https://github.com/peddleon/ped-ux-react-library/commit/1b26b5f56813629e04f47f03b09ef47a703bd04e))
* navigation props for the no left navigation ([96b30fb](https://github.com/peddleon/ped-ux-react-library/commit/96b30fbfd08e32b3c23bc9bce9b98c4391a3d359))
* navigation props for the no left navigation ([d9ae3e7](https://github.com/peddleon/ped-ux-react-library/commit/d9ae3e7dfd9b9ba764671061aca4bb028565ed84))
* **NavigationBar:** modified dropdown props for similar ID ([ccbf66c](https://github.com/peddleon/ped-ux-react-library/commit/ccbf66cd9e7843ee52854010b6348a1713ca7bfc))
* page layout sidebar position set default to top ([e60f550](https://github.com/peddleon/ped-ux-react-library/commit/e60f550c6fe71b6cfda73728a99ce2eed6bb1205))
* passing the backgroundcolor with application layout -&gt; navbar ([4a63a89](https://github.com/peddleon/ped-ux-react-library/commit/4a63a89a916f54d85781e4793aeda41d436247bf))
* remove commented css ([dd88c90](https://github.com/peddleon/ped-ux-react-library/commit/dd88c907220a0b10d9ad2ca1c907962be40ccc32))
* remove forButtom prop ([f97968e](https://github.com/peddleon/ped-ux-react-library/commit/f97968ed0cdff3448fa24f6d04e327dfc34363cb))
* remove unused id ([c198639](https://github.com/peddleon/ped-ux-react-library/commit/c19863974128238b3209e8af4c153336497cae00))
* test case id constant ([319ef8e](https://github.com/peddleon/ped-ux-react-library/commit/319ef8eedd47d4c8387e6d33c7a41d7ca3602a7c))

## [1.1.0](https://github.com/peddleon/ped-ux-react-library/compare/v1.0.0...v1.1.0) (2024-04-22)

### Features

- [UX-48] added description for the component ([35e2786](https://github.com/peddleon/ped-ux-react-library/commit/35e2786cff00b1852de6c1d9e84232bcd4abba08))
- [UX-48] create textarea component ([ac43d99](https://github.com/peddleon/ped-ux-react-library/commit/ac43d9986476d499dd77bde50396230aa438b926))
- [UX-48] update storybook path ([e72ec05](https://github.com/peddleon/ped-ux-react-library/commit/e72ec05209874b3137a3b859ad2aa7fb3fd1c032))
- [UX-51]Nav item desktop component ([11fa1e5](https://github.com/peddleon/ped-ux-react-library/commit/11fa1e50e76ccb4d823dd0103d212288e2ec64ea))
- [UX-52] added commit lint config ([93b4ed0](https://github.com/peddleon/ped-ux-react-library/commit/93b4ed0ac0ba9b0b7e42ad159ce5e79a7a687de5))
- [UX-52] fix import issues and create eyebrow dialog modal ([862c642](https://github.com/peddleon/ped-ux-react-library/commit/862c6425878aa06059492f43940e795c098d80d9))
- [UX-52] grouping core components ([be788fe](https://github.com/peddleon/ped-ux-react-library/commit/be788fe9abc526fa891a7f886385f3946461fac8))
- [UX-52] modal design component ([36a1223](https://github.com/peddleon/ped-ux-react-library/commit/36a12232bdc8bf888c1b1dfc97e87897a8b1e84f))
- [UX-52] update import path ([f08bd4c](https://github.com/peddleon/ped-ux-react-library/commit/f08bd4ce8ca0aff410615b37e52ece4709cb94f3))
- [UX-53] develop button component ([cb2bfdc](https://github.com/peddleon/ped-ux-react-library/commit/cb2bfdca94fb0164f3242937744655f24c8561cf))
- [UX-53] develop button component ([ca0cf89](https://github.com/peddleon/ped-ux-react-library/commit/ca0cf894abf3bc930a757980bc3d3a931ee07872))
- [UX-57] test cases for animeted loader component ([70ef924](https://github.com/peddleon/ped-ux-react-library/commit/70ef924886a96d97a469ef70e4e5c5430ecafb60))
- [UX-57] testing library setup ([bf1cd86](https://github.com/peddleon/ped-ux-react-library/commit/bf1cd8662ac77824137e96d4e348c94b1333c4ee))
- [UX-59] storybook configuration ([8e536a9](https://github.com/peddleon/ped-ux-react-library/commit/8e536a95d246c1580b388efe54e4e82c1f65d234))
- [UX-60] configure code pipeline to publish packages ([8aeb1dd](https://github.com/peddleon/ped-ux-react-library/commit/8aeb1dde1037cd9a5bed6c9ae1decd906e7c24c5))
- [UX-61] divider component ([943a681](https://github.com/peddleon/ped-ux-react-library/commit/943a681a06e909118412fdba7f7c1e5728eab0fa))
- [UX-62] Design a responsive Header with browser compatibility. ([9789479](https://github.com/peddleon/ped-ux-react-library/commit/97894799a22e2cd0d8a8aea351f42a3a62ada8c3))
- [UX-62] Design a responsive Header with browser compatibility. ([efe2372](https://github.com/peddleon/ped-ux-react-library/commit/efe2372e3f9b3b8c8b1dd648fcfe7c743ab05257))
- [UX-65] added isSearchable functionality with the some UI fix ([9426b14](https://github.com/peddleon/ped-ux-react-library/commit/9426b1427b22703234af23927e130108e8640744))
- [UX-65] added mobile center props support ([eaf0a51](https://github.com/peddleon/ped-ux-react-library/commit/eaf0a5141cf93ce3cd832507be86c5e6441b0f9c))
- [UX-65] added test case for the select ([abf7f1d](https://github.com/peddleon/ped-ux-react-library/commit/abf7f1d64c947a0a25135e3fd807bfb5e58d646c))
- [UX-65] added test cases ([de2726a](https://github.com/peddleon/ped-ux-react-library/commit/de2726aa7fb52c26dfa96af8dd9d6ba6d342165b))
- [UX-65] added to dependencies from devDependencies ([1a31c3e](https://github.com/peddleon/ped-ux-react-library/commit/1a31c3e607d14222ea8b03ca5e6e770af29cd73f))
- [UX-65] arrange exports ([f33e009](https://github.com/peddleon/ped-ux-react-library/commit/f33e00917033618938789bf8c970a23b76cdadda))
- [UX-65] create select component ([2253535](https://github.com/peddleon/ped-ux-react-library/commit/2253535a084e0c149274c054ece9cec1675b43ed))
- [UX-65] export select ([02f8036](https://github.com/peddleon/ped-ux-react-library/commit/02f8036c23a8b284f82a6c6b8041b9a84f205110))
- [UX-65] intial setup for the select input ([e0061a3](https://github.com/peddleon/ped-ux-react-library/commit/e0061a3ae723cec04618142f658cdf6d05e58450))
- [UX-65] make textinput stateless ([ebb33d6](https://github.com/peddleon/ped-ux-react-library/commit/ebb33d6f4332763585020bb66c12aa9efe188d53))
- [UX-65] removed isSearchable prop ([d25a18b](https://github.com/peddleon/ped-ux-react-library/commit/d25a18bb6707c5e62934979ef0f40d9c1495f76f))
- [UX-65] removed var ([6b51072](https://github.com/peddleon/ped-ux-react-library/commit/6b510729d485be938786244caae48edeb11945ea))
- [UX-65] rename isFetching to isLoading ([742b15a](https://github.com/peddleon/ped-ux-react-library/commit/742b15aabc7effc68d2fdad7c267aa756ae55277))
- [UX-65] update condition ([58dade2](https://github.com/peddleon/ped-ux-react-library/commit/58dade2adbaf7683313666f77d7acb023c6d457e))
- [UX-65] update downshift version ([d4eafe1](https://github.com/peddleon/ped-ux-react-library/commit/d4eafe18e8e590f3e0bd7f0b0862bcefd3286408))
- [UX-66] page layout component ([df7d097](https://github.com/peddleon/ped-ux-react-library/commit/df7d09770245f3c6c9411429edbda03634b8034a))
- [UX-67] alert component ([5a7af57](https://github.com/peddleon/ped-ux-react-library/commit/5a7af575077a3b29cd5cf5a95a71e75786005d03))
- [UX-68] added icon support in story ([c925616](https://github.com/peddleon/ped-ux-react-library/commit/c925616c041ae9245b9b56b31cee2d6871c17f41))
- [UX-68] created section wrapper component ([66b9134](https://github.com/peddleon/ped-ux-react-library/commit/66b9134a5b749b21a5efebb02c75ef47d525eb49))
- [UX-68] rename component location ([0893e31](https://github.com/peddleon/ped-ux-react-library/commit/0893e3127763f5d5668f13ed8864b45a84a2286e))
- [UX-68] rename story name ([52a78ca](https://github.com/peddleon/ped-ux-react-library/commit/52a78ca25703b7ee6bc0aadcf8839b22e6fae2af))
- [UX-70] section title component ([00829bb](https://github.com/peddleon/ped-ux-react-library/commit/00829bb4f50ab025434d9dac9975f1d2f6ba366c))
- [UX-71] toast component ([4132eed](https://github.com/peddleon/ped-ux-react-library/commit/4132eed2d743bf17a51fc3a96c7246d31b1b4f02))
- [UX-71] toast storybook ([ec1ee77](https://github.com/peddleon/ped-ux-react-library/commit/ec1ee773d6b1e280a59fa8ffbd6269878f803b8e))
- [UX-74] created radio,radio group component ([4508d12](https://github.com/peddleon/ped-ux-react-library/commit/4508d12ee183911e42be77a4f8fc55a57eac7bea))
- [UX-74] excluded radio from index ([c9e78c0](https://github.com/peddleon/ped-ux-react-library/commit/c9e78c0c9a1116043b64963f0aa3f00eed532347))
- [UX-75] created carousel component ([a35edc2](https://github.com/peddleon/ped-ux-react-library/commit/a35edc27c0bc9e8fca5bbf308c0416e5f9ee45c0))
- [UX-76] date view component ([6c88b64](https://github.com/peddleon/ped-ux-react-library/commit/6c88b641ed71a18cdda64a18848cd8a2c0c19d01))
- [UX-77] created timeslot picker component ([dcf953c](https://github.com/peddleon/ped-ux-react-library/commit/dcf953c8efda28c4a435c55b10278881ebb51e11))
- [UX-77] refactored code to adjust emptystate prop ([023ea89](https://github.com/peddleon/ped-ux-react-library/commit/023ea8981d487e9dac911fd2509f2832496d2d7e))
- [UX-77] remove infinate prop ([3af170f](https://github.com/peddleon/ped-ux-react-library/commit/3af170f11485f963db101d0a376de6722e172f9b))
- [UX-78] detailed CTA component ([5bc7537](https://github.com/peddleon/ped-ux-react-library/commit/5bc7537996061626e2609cc490991166b18c2c5a))
- [UX-80] added scrim to modal body & dropdown ([308e1ac](https://github.com/peddleon/ped-ux-react-library/commit/308e1ac4f36ada9ce749463f8490477e6ec77eec))
- [UX-80] created scrim component ([60bbe83](https://github.com/peddleon/ped-ux-react-library/commit/60bbe83f00fb579b170cd4349e193c05a8674946))
- [UX-81] added component description ([be0db98](https://github.com/peddleon/ped-ux-react-library/commit/be0db98c949b241d2e20d84d5ae2d62931c26cb7))
- [UX-81] design a code block component ([6682cac](https://github.com/peddleon/ped-ux-react-library/commit/6682cac00233f34bd9c5688d44122cf8904ef7f0))
- [UX-82] added table header component for table ([93fa8fb](https://github.com/peddleon/ped-ux-react-library/commit/93fa8fb0e951e499f3652d4acc8e5735915a2466))
- [UX-83] added tile components ([4b26402](https://github.com/peddleon/ped-ux-react-library/commit/4b26402b81d6f9285166b427de486c0517bf191c))
- [UX-83] create a table component ([d0dbcba](https://github.com/peddleon/ped-ux-react-library/commit/d0dbcba23f74f7d387fb3a448a56dafbce640d25))
- [UX-83] create a table component ([3f0ad30](https://github.com/peddleon/ped-ux-react-library/commit/3f0ad303b74f2ca9484e95b7bcb42878b713def7))
- [UX-85] created table row component ([73b8717](https://github.com/peddleon/ped-ux-react-library/commit/73b8717f8c656c1f4fe41d72229198ac10b37975))
- [UX-86] design a table text component for table ([e332970](https://github.com/peddleon/ped-ux-react-library/commit/e3329708a0d73b5a97824d0ecff294e29679a143))
- [UX-86] design a table text component for table ([b2d3e92](https://github.com/peddleon/ped-ux-react-library/commit/b2d3e9258849d9772c3cd43990c5875144b0ccb7))
- [UX-87] added container from the apex ([9bd55d7](https://github.com/peddleon/ped-ux-react-library/commit/9bd55d7eacd145f13c92a669861d0b8d768e990f))
- [UX-87] added env frame from the apex ([544db66](https://github.com/peddleon/ped-ux-react-library/commit/544db6651e4491c251a37200429348dd2c98f035))
- added hooks required for the navigation ([be62c5f](https://github.com/peddleon/ped-ux-react-library/commit/be62c5fc4c1692701140e2cd580103fe5d96d2b0))
- added react-table package ([05029f6](https://github.com/peddleon/ped-ux-react-library/commit/05029f672595c02816f0e36fdf93296cac986fba))
- added test case for the navigation ([2cecde3](https://github.com/peddleon/ped-ux-react-library/commit/2cecde3477a37335baae46ba07eafae6d2702fc4))
- added test coverage setup ([f2a3814](https://github.com/peddleon/ped-ux-react-library/commit/f2a381478f6e6bc4e26f8000364628cef79af095))
- Added welcome doc and the styles folder from apex ([1b8c315](https://github.com/peddleon/ped-ux-react-library/commit/1b8c31502ef7121458b56c8c64950d5b6ea48a22))
- create the frame component also fix error with tag component ([d3ae3ee](https://github.com/peddleon/ped-ux-react-library/commit/d3ae3ee8bcef162e94ff18b9ace9a7b303246ae3))
- create the tag component ([a64afe6](https://github.com/peddleon/ped-ux-react-library/commit/a64afe6c8262867c4f88956634ddf80fd40a5359))
- inital setup for heading ([ac35fd4](https://github.com/peddleon/ped-ux-react-library/commit/ac35fd46cf563f774621b27937bec5026e77c8dd))
- inital setup for heading ([2ae2f63](https://github.com/peddleon/ped-ux-react-library/commit/2ae2f637285e1eea8da7da92ef362cfca6919214))
- **Shimmer:** added component from the apex ([ea46524](https://github.com/peddleon/ped-ux-react-library/commit/ea4652433facb1d00e0d4b37a04b3b9cb10f7752))

### Bug Fixes

- [UX-48] removed unnecessary prop ([6db6cb8](https://github.com/peddleon/ped-ux-react-library/commit/6db6cb8118990feda8229ea656a0deca1302b9dd))
- [UX-52] refactor code ([d53e34c](https://github.com/peddleon/ped-ux-react-library/commit/d53e34ce5c19210f3f0d360a296f90fa6580becb))
- [UX-52] refactor code for button ([5711288](https://github.com/peddleon/ped-ux-react-library/commit/571128822d912a9084f54495996ccb439c27d1f0))
- [UX-52] separate out types from tsx ([096c86c](https://github.com/peddleon/ped-ux-react-library/commit/096c86c32dc63ff3ed7e341199126b3a794c0fc0))
- [UX-52] update lint issues ([1ac9190](https://github.com/peddleon/ped-ux-react-library/commit/1ac919020a8bf2c2019f9563dc54ae36da4f13dd))
- [UX-65] added external for rollup build ([ad24761](https://github.com/peddleon/ped-ux-react-library/commit/ad24761f12714ffdab293725cfa035c522d3ce6f))
- [UX-65] build issue ([f2f00d9](https://github.com/peddleon/ped-ux-react-library/commit/f2f00d9b5e56b228df6f2eaa121545ab5c0f4542))
- [UX-65] lint issues ([311fc97](https://github.com/peddleon/ped-ux-react-library/commit/311fc9768df7fd887532533d964d620d1ce3c6b1))
- [UX-65] loading import issue ([9ea02d6](https://github.com/peddleon/ped-ux-react-library/commit/9ea02d6c5c6dd58369a565d877f96689fad95d46))
- [UX-65] removed unwanted props & fix blur/focus issues ([34d1001](https://github.com/peddleon/ped-ux-react-library/commit/34d10013a629f16235c908f5600ee0b59e126e2d))
- [UX-65] rename fetching in stories ([03fcc9b](https://github.com/peddleon/ped-ux-react-library/commit/03fcc9bf6a3d5a0b8605aebe451e826b4230ceef))
- [UX-65] set default onchange ([d6153fd](https://github.com/peddleon/ped-ux-react-library/commit/d6153fd0614d1566fda86468293765c37741177a))
- [UX-65] shift downshift to dependencies ([d1f4269](https://github.com/peddleon/ped-ux-react-library/commit/d1f4269e07c99c9b129c2fcf78e1395c6cd35c23))
- [UX-65] update onclick handler for icon ([3e3b405](https://github.com/peddleon/ped-ux-react-library/commit/3e3b405e0806e5fc1000ac0d97116288239c7108))
- [UX-74] removed unnecessary props ([846f416](https://github.com/peddleon/ped-ux-react-library/commit/846f416b0eeedc42a3238e555d07b18035b17800))
- [UX-77] scroll issues ([7a71525](https://github.com/peddleon/ped-ux-react-library/commit/7a71525fed48b55e980e04e018b713e2c2d1ef53))
- add new npmrc file for publishing packages ([e42474e](https://github.com/peddleon/ped-ux-react-library/commit/e42474e41d4ae7c94933e6dbf9add44a7d34dfdb))
- add step for login ([973fd80](https://github.com/peddleon/ped-ux-react-library/commit/973fd801b8b43887aa8b87e33c01c7886744884d))
- add step for login ([2f58e14](https://github.com/peddleon/ped-ux-react-library/commit/2f58e14c00b474c16331d0cee86de4b2dc2fd3cd))
- added pre publish command ([386cff1](https://github.com/peddleon/ped-ux-react-library/commit/386cff1d61841900e83e43ae4c733b8885fc73fa))
- auth token changes ([ca9acb7](https://github.com/peddleon/ped-ux-react-library/commit/ca9acb71920ebf7c4720bc500c830a2aec671c70))
- broken test cases ([39339c9](https://github.com/peddleon/ped-ux-react-library/commit/39339c96976e14560d9fcb2c3883cc6c43a09037))
- code pipeline issue ([5113699](https://github.com/peddleon/ped-ux-react-library/commit/5113699b5e0fb82d8a6ab8dfc166f329d67cbcc2))
- code pipeline issue ([78bfd00](https://github.com/peddleon/ped-ux-react-library/commit/78bfd004f81dc8f47c1d168f5fbf7ed1bb6c8ef4))
- css config ([bff1a2b](https://github.com/peddleon/ped-ux-react-library/commit/bff1a2b8bd7d0c925c49036a1975069e6b22b56e))
- css config ([d741552](https://github.com/peddleon/ped-ux-react-library/commit/d741552275f8aadb4b28d4c9dbc33f9ae0c8c53a))
- css config ([a33b997](https://github.com/peddleon/ped-ux-react-library/commit/a33b9975aeae429542e02fc52f60a1fdeb88a4de))
- css config ([d739799](https://github.com/peddleon/ped-ux-react-library/commit/d739799edb36c44c43298c854e3ea2472c32b603))
- css config ([b01773a](https://github.com/peddleon/ped-ux-react-library/commit/b01773a41ea97690aec32f4fcccdc5782f523d6b))
- css config ([8f49895](https://github.com/peddleon/ped-ux-react-library/commit/8f49895ef36df01aa5dd5593b3bfc94676df075a))
- css config ([d567f33](https://github.com/peddleon/ped-ux-react-library/commit/d567f3319386e22ed5363154d4c4834aca33cb02))
- css config ([a00bd31](https://github.com/peddleon/ped-ux-react-library/commit/a00bd3128681f901f861700a5828491e6470372f))
- css config ([67dc753](https://github.com/peddleon/ped-ux-react-library/commit/67dc7536b34206b2fe70f6ca646d59059ee68337))
- css config ([db1534c](https://github.com/peddleon/ped-ux-react-library/commit/db1534c2e001243dd77f435b9234b103410fc246))
- css config ([c3892f9](https://github.com/peddleon/ped-ux-react-library/commit/c3892f9875a83be8c32b261d532933f7ef1842c9))
- css config ([9d20ce1](https://github.com/peddleon/ped-ux-react-library/commit/9d20ce17a6aae30428fc83a396203f98f15fcc6f))
- css path resolved ([65b122d](https://github.com/peddleon/ped-ux-react-library/commit/65b122d3517111df59edaf16e7be5b03040517a1))
- css path resolved ([a6f1bb1](https://github.com/peddleon/ped-ux-react-library/commit/a6f1bb188974e134f5b9d9fe7c946fac21003a94))
- disable console for stories ([6c60307](https://github.com/peddleon/ped-ux-react-library/commit/6c60307e096b5adc46c5cb2ed8ede1e96385a4f6))
- divider documentation ([ba5590f](https://github.com/peddleon/ped-ux-react-library/commit/ba5590f8de2a3665b74137ade038fcf263e0080a))
- esbuild css issue ([9b710de](https://github.com/peddleon/ped-ux-react-library/commit/9b710dece8d46d47b5ba578aa3325e21186a4f09))
- esbuild css issue ([57e89ba](https://github.com/peddleon/ped-ux-react-library/commit/57e89ba4de34066ca68b07499377c1a4b74a7a50))
- esbuild css issue ([1dbeca0](https://github.com/peddleon/ped-ux-react-library/commit/1dbeca06817211f69d648dc5789ddf3fc2c18d79))
- esbuild css issue ([0d8c9c5](https://github.com/peddleon/ped-ux-react-library/commit/0d8c9c55299da4cfe56c37d152894c8b54c02265))
- esbuild css issue ([e21220f](https://github.com/peddleon/ped-ux-react-library/commit/e21220f23a1dc94b272d66d500b3dadf13be5218))
- esbuild css issue ([d66a8e4](https://github.com/peddleon/ped-ux-react-library/commit/d66a8e41849a31f0e71f193c700377cfa1430e17))
- eslint console error ([e9efe47](https://github.com/peddleon/ped-ux-react-library/commit/e9efe47d78525b8dc6e66e4f71e5baaa2429a4fd))
- eslint issue resolved ([b17cefe](https://github.com/peddleon/ped-ux-react-library/commit/b17cefed56584805719c2e672a739448d6faf3ed))
- eslint issues ([b6f6074](https://github.com/peddleon/ped-ux-react-library/commit/b6f6074a1ada9fb80e98cd200f8c37e81e0cf00b))
- expose fluid root component ([d1a9cc2](https://github.com/peddleon/ped-ux-react-library/commit/d1a9cc2a50f1503855057941ab1f0cdff4061e3f))
- fluid root component css ([85a205b](https://github.com/peddleon/ped-ux-react-library/commit/85a205b26ddd081f74f88c42d6c907ade9be0361))
- issue with the navigation bar link component ([3155100](https://github.com/peddleon/ped-ux-react-library/commit/3155100143de55b42f950c9a09837fc415c0ef4d))
- issues with typography component and eslint errors ([5025603](https://github.com/peddleon/ped-ux-react-library/commit/5025603ee9a333987c5e07a0f85771433db18f2f))
- jest config for alias configuration ([4667d3d](https://github.com/peddleon/ped-ux-react-library/commit/4667d3d3c607ea71d2f6dea11507eea60e8a193f))
- lint errors ([c4863c1](https://github.com/peddleon/ped-ux-react-library/commit/c4863c1bcf07572ff296558b0715102f86f5af90))
- lint errors ([a1f8cce](https://github.com/peddleon/ped-ux-react-library/commit/a1f8ccef4dfb19339a4b378ee2892c89cfd79b5e))
- main release github action changes ([a88bfa3](https://github.com/peddleon/ped-ux-react-library/commit/a88bfa315f29a56d09e71443f72256504b8e8954))
- post css config ([0007fb5](https://github.com/peddleon/ped-ux-react-library/commit/0007fb5ea7afb5a1fc85ee40ec237826158bed0b))
- pre commit hook ([7d29fc8](https://github.com/peddleon/ped-ux-react-library/commit/7d29fc8f9a28eaf1c835198e6f5d0de475082da5))
- removed unused package ([1fdc025](https://github.com/peddleon/ped-ux-react-library/commit/1fdc025dd86fac7284e49400e2090d197a40e4a1))
- removed unused props button component ([323c211](https://github.com/peddleon/ped-ux-react-library/commit/323c211fa319087136be28444b3cdd453607fe87))
- repo name change ([dd4cca5](https://github.com/peddleon/ped-ux-react-library/commit/dd4cca57359684a92ac592f5f71a22d253923dc6))
- required prop as well as eslint file ([5fbc08e](https://github.com/peddleon/ped-ux-react-library/commit/5fbc08e52b122ebd7c425381b699c2ebb82e2d24))
- required prop as well as eslint file ([1a63b20](https://github.com/peddleon/ped-ux-react-library/commit/1a63b20248d23381dc0df6f9b8b178d04ca5f1cf))
- styles related changes ([bfe62c7](https://github.com/peddleon/ped-ux-react-library/commit/bfe62c755283b6bebdcaab7885a94272b96da07c))
- styling and props destribution for app layout ([0ca3757](https://github.com/peddleon/ped-ux-react-library/commit/0ca375784c5513d771326f6fcc0c50c8473bd6ac))
- styling issue with table header module css ([4c00455](https://github.com/peddleon/ped-ux-react-library/commit/4c0045511daa99b3c658d363a95a15eae0c18d5c))
- **TableText:** test cases for the table text ([5d31f6a](https://github.com/peddleon/ped-ux-react-library/commit/5d31f6a4cc73d0f7554cb9e5b2c067c9fc8dc4dd))
- test cases for application layout and dropdown ([d66e5a6](https://github.com/peddleon/ped-ux-react-library/commit/d66e5a6544fb43f2d4fbd6572d28d63b8f54aa4e))
- types issue ([9048157](https://github.com/peddleon/ped-ux-react-library/commit/90481579963b26b8b2685ffce56caf837e5dabdc))
- unit test cases for the dropdown and navigation bar ([94e8c84](https://github.com/peddleon/ped-ux-react-library/commit/94e8c845593f416b4af7eeb25232344d6eff1807))
- update external lib ([de31dab](https://github.com/peddleon/ped-ux-react-library/commit/de31dabd30d13d53ddda454d1945d3e7216eaefd))
- warning for text row ([8ffc8b9](https://github.com/peddleon/ped-ux-react-library/commit/8ffc8b99f4f467674061aed89a58376e52f037dc))

### Miscellaneous

- [UX-51] navigation for the desktop ([f28b596](https://github.com/peddleon/ped-ux-react-library/commit/f28b596fea508b7fccdc5a07cc65af1b03f2e33e))
- [UX-82] design the responsive table header ([116fb68](https://github.com/peddleon/ped-ux-react-library/commit/116fb68d30a93b039244ee0f9709740ece0b5fa3))
- added build command ([ac07ac1](https://github.com/peddleon/ped-ux-react-library/commit/ac07ac1726f1d195e3b7e76f6bdc841dd4baa1a0))
- added build command ([1b21a97](https://github.com/peddleon/ped-ux-react-library/commit/1b21a9764d46efbb330beda4ce7842c17f858b20))
- added build command ([2b39eba](https://github.com/peddleon/ped-ux-react-library/commit/2b39eba31634e4c8bff84a9026c84c480e01c418))
- added build command ([7704d69](https://github.com/peddleon/ped-ux-react-library/commit/7704d69d35e097d1aaf545b6391ac850d15eca71))
- added build command ([cfe0c7e](https://github.com/peddleon/ped-ux-react-library/commit/cfe0c7e919fce821fc5a937eab69da8d49ed09a8))
- added build command ([2adf9b2](https://github.com/peddleon/ped-ux-react-library/commit/2adf9b2e044fb55b74100d8b7c361a7cb93cd2a4))
- added build command ([ac1e30a](https://github.com/peddleon/ped-ux-react-library/commit/ac1e30a22e15a67c1acf1f8d16b44fd06fa9db6e))
- added button test cases ([19ebae9](https://github.com/peddleon/ped-ux-react-library/commit/19ebae90dc75be2a26bde39a04a9a639aa9569af))
- added default export ([c77411c](https://github.com/peddleon/ped-ux-react-library/commit/c77411cd3c5651b1967edc18810d013310164c8a))
- added documentation for typography ([1b6fd53](https://github.com/peddleon/ped-ux-react-library/commit/1b6fd53662b64293d128bb1769cfbe47c4a05fcf))
- added more test case ([6bf74fe](https://github.com/peddleon/ped-ux-react-library/commit/6bf74fedea0d876b9d691544ec6f1e728abd5251))
- added storybook theme configuration ([a0314cd](https://github.com/peddleon/ped-ux-react-library/commit/a0314cd514b6c7729da66b184a5cd3ce75dc9c8e))
- allow vscode config to get passed with git ([2c2f024](https://github.com/peddleon/ped-ux-react-library/commit/2c2f024fe09152f874fd4a84a852a8d0dda64dac))
- application layout code import path change ([b5788cb](https://github.com/peddleon/ped-ux-react-library/commit/b5788cb739209b363405d840d9ffdd4a3e39b76f))
- **base.css:** remove the google default styles ([791339b](https://github.com/peddleon/ped-ux-react-library/commit/791339bf75216b9dc1fb626c16c0b35c36437b99))
- change prop types name ([a1f03cd](https://github.com/peddleon/ped-ux-react-library/commit/a1f03cd04f704f2e018732c5c487e7a52d13dc1a))
- change typography types import ([41c9f4d](https://github.com/peddleon/ped-ux-react-library/commit/41c9f4d9798d369ac09716244c0abd745d4bc187))
- code for app layout ([bb525e7](https://github.com/peddleon/ped-ux-react-library/commit/bb525e72e12db69fe93f91bc3976db1bc9decfb1))
- code refactor ([f8f59ab](https://github.com/peddleon/ped-ux-react-library/commit/f8f59ab5cce4e42eb2d790fd2e43676d07c0a39c))
- code refactor ([08b301d](https://github.com/peddleon/ped-ux-react-library/commit/08b301d399912e2c913009c4020e20d7c0154c76))
- code refactor ([9ca746e](https://github.com/peddleon/ped-ux-react-library/commit/9ca746e39770e7128141cd337a7896e86de952d3))
- code refactor ([2fe7888](https://github.com/peddleon/ped-ux-react-library/commit/2fe788800ac86cd2a78964a13a2d1cd8ea1cf1e0))
- code refactor ([fdecfbe](https://github.com/peddleon/ped-ux-react-library/commit/fdecfbeb973bdb0134bfb88cab54b00a0395d30d))
- code refactor ([b825d81](https://github.com/peddleon/ped-ux-react-library/commit/b825d81e1c6b4bb87b1568ba979e9b1566459d09))
- code refactor ([fcf7716](https://github.com/peddleon/ped-ux-react-library/commit/fcf7716e6e5d00a46c4755dac3a2ac484f9d4cb1))
- code refactor ([943f261](https://github.com/peddleon/ped-ux-react-library/commit/943f2611d745ec4c2e998e4f01a3448f37ec0a50))
- code refactor ([48df109](https://github.com/peddleon/ped-ux-react-library/commit/48df10930d41036378a8fa0c3943f23fafaca71a))
- code refactor ([2dc1531](https://github.com/peddleon/ped-ux-react-library/commit/2dc1531c68b49eb333a5246286f8b073aaa92926))
- code refactor ([9568677](https://github.com/peddleon/ped-ux-react-library/commit/95686770ba4dc9b28ffc6c58740ddfaf411ebdb3))
- component name change from DateViewComponent to DateView ([94696fc](https://github.com/peddleon/ped-ux-react-library/commit/94696fcc975c963fd5fe73335d251cef99713a65))
- **deps-dev:** bump express from 4.18.3 to 4.19.2 ([808e0eb](https://github.com/peddleon/ped-ux-react-library/commit/808e0ebe4c1eceb527d120be27b8dcc2a3309406))
- **deps-dev:** bump webpack-dev-middleware from 6.1.1 to 6.1.2 ([99203c3](https://github.com/peddleon/ped-ux-react-library/commit/99203c3597a3f862265d6aa3709f31ad855b4e48))
- **develop:** release 1.0.0 ([8a4e67d](https://github.com/peddleon/ped-ux-react-library/commit/8a4e67d37d23ff42fae45228836373bc1df5b98b))
- **develop:** release 1.0.0 ([4d500dd](https://github.com/peddleon/ped-ux-react-library/commit/4d500ddd69b6cdceab72dc2ee04c807846ce01ab))
- document for the button ([2430c5e](https://github.com/peddleon/ped-ux-react-library/commit/2430c5e0529ab776c71040c0b144e92cff603865))
- document for the dropdown ([c7ddce3](https://github.com/peddleon/ped-ux-react-library/commit/c7ddce36dc8922c32f90b6af4937ee5aed91235b))
- document for the Typography ([1b2802f](https://github.com/peddleon/ped-ux-react-library/commit/1b2802f6646286d836971c7edc5c12f235850b51))
- export button component ([9ea0a96](https://github.com/peddleon/ped-ux-react-library/commit/9ea0a96160eff65c1990c44c2197fc658b2f184d))
- export date utils functions ([4f29731](https://github.com/peddleon/ped-ux-react-library/commit/4f29731464708b42d77471b26312ae4165d44aaa))
- fix eslint github action ([46851e1](https://github.com/peddleon/ped-ux-react-library/commit/46851e1698a5471fbaf73485fc3df9bf3199b29d))
- fix eslint github action ([3c07cb5](https://github.com/peddleon/ped-ux-react-library/commit/3c07cb559ef2a0ac18161ededb57fb2a5d8ef1e2))
- fix imports ([765c937](https://github.com/peddleon/ped-ux-react-library/commit/765c937cd8cb0845884fc8c7553d2deba474b3f9))
- fix issues with theme ([2f6ad15](https://github.com/peddleon/ped-ux-react-library/commit/2f6ad15488a51ac7c364c333cbdb63ba8a9e5c76))
- fix naming for navigation ([5c10639](https://github.com/peddleon/ped-ux-react-library/commit/5c1063998520aeac097e8c5c648d2950fea3a67c))
- fix naming for navigation ([865e30f](https://github.com/peddleon/ped-ux-react-library/commit/865e30f88540e93516e92f381a637c000cee9620))
- fix the imports ([8bbf38c](https://github.com/peddleon/ped-ux-react-library/commit/8bbf38ce57c5f7e986955a6842cc6d688ab5f036))
- fix the imports ([dabebf9](https://github.com/peddleon/ped-ux-react-library/commit/dabebf996e5b0f657c7a810a2f59a131c3f4f478))
- folder for the mobile navigation ([540092a](https://github.com/peddleon/ped-ux-react-library/commit/540092a46400e815a8f182fcd28c130dbd5b7ef3))
- git merge suggestions ([b2196d1](https://github.com/peddleon/ped-ux-react-library/commit/b2196d166e0a225c447fec331c70a9edccc0c1f0))
- initial commit ([c0ed8c2](https://github.com/peddleon/ped-ux-react-library/commit/c0ed8c2102432e410d0ffd6ab3aedc1a878a4573))
- logo imports ([049cbca](https://github.com/peddleon/ped-ux-react-library/commit/049cbca93c49444a27a9644a4e1c4f95c9188223))
- naming for admin frame ([98d9853](https://github.com/peddleon/ped-ux-react-library/commit/98d9853d5db75b2c0aeb90dcc3266ed95d1bb742))
- naming for the test id's ([63db47d](https://github.com/peddleon/ped-ux-react-library/commit/63db47d7872691c98ba8b1456890f5541f7c507a))
- naming for the test id's ([62039f2](https://github.com/peddleon/ped-ux-react-library/commit/62039f25960f285e9fa07a0b667d63cd805f1f8b))
- naming for the test id's ([2fdbecf](https://github.com/peddleon/ped-ux-react-library/commit/2fdbecf696e984b0b9460c9598ded460c94bf011))
- need to change ([6ca6c17](https://github.com/peddleon/ped-ux-react-library/commit/6ca6c178ddaebd860e56db06237542d956dcad9c))
- package lock update ([7960b5b](https://github.com/peddleon/ped-ux-react-library/commit/7960b5bafe165a1c621146772c4a05eb55659e2f))
- pagelayout sidebar and footer ([651723a](https://github.com/peddleon/ped-ux-react-library/commit/651723a0bb4d26010f7dacdcdb76953a95015129))
- path issue with windows devices for import ([080220d](https://github.com/peddleon/ped-ux-react-library/commit/080220d46814e71e0b0f9684622ba4add8de97c6))
- prop description within file only ([e5ffe29](https://github.com/peddleon/ped-ux-react-library/commit/e5ffe2988b3db15575032cfdae69c1eef65f5736))
- prop for fetching data ([173b8a2](https://github.com/peddleon/ped-ux-react-library/commit/173b8a297681acd6740fa28d0483f163f11529a2))
- props changes for navigation and style support ([e95fb26](https://github.com/peddleon/ped-ux-react-library/commit/e95fb26d45a3bd394888f5df1717b671ff0e8f04))
- props for the application ([af5e643](https://github.com/peddleon/ped-ux-react-library/commit/af5e643299545f6d4cad7af6199e1a2fafcafc17))
- provide documentation for the app layout ([5d32de1](https://github.com/peddleon/ped-ux-react-library/commit/5d32de1969f6cdbe02a3781de2134df1592f9c38))
- provided props details with table ([4742f7a](https://github.com/peddleon/ped-ux-react-library/commit/4742f7a7adefc8cc1e73952929a53e1b8845807e))
- provided props details with table ([a0e231a](https://github.com/peddleon/ped-ux-react-library/commit/a0e231a53e0306bf44f18691af94f5e74cc120f2))
- provided wrapper for server ([b43701d](https://github.com/peddleon/ped-ux-react-library/commit/b43701df931aedd96697ecca4b69a0e7b84cc527))
- provided wrapper for server ([0c1ae17](https://github.com/peddleon/ped-ux-react-library/commit/0c1ae1718ec5d037560e6372182f8c4c144257a1))
- readme md file configuration ([1ab7440](https://github.com/peddleon/ped-ux-react-library/commit/1ab744082da8dc95afe09c7333d3b653ed581278))
- readme md file update ([b816ef3](https://github.com/peddleon/ped-ux-react-library/commit/b816ef3123abff4a3c0edad8b845c2c6256c978b))
- remove close admin frame button ([6fc86e3](https://github.com/peddleon/ped-ux-react-library/commit/6fc86e3396441a86851dc8257d303bb82656bdfc))
- remove icons ([041eb4c](https://github.com/peddleon/ped-ux-react-library/commit/041eb4cb4e528978f5c1aa8818a3a0c6b3689533))
- revert code ([cc87f02](https://github.com/peddleon/ped-ux-react-library/commit/cc87f02a91d8825a6ef0fa066a3bbac1650787c2))
- rules update for arrow function ([1024ff4](https://github.com/peddleon/ped-ux-react-library/commit/1024ff49ef3ea799c029d7a639ea44fc65768f67))
- set data test id ([90514e8](https://github.com/peddleon/ped-ux-react-library/commit/90514e899b9b06f5c436fd1479bd65cb54e2130f))
- **Shimmer:** pascal case ([8a4ff3e](https://github.com/peddleon/ped-ux-react-library/commit/8a4ff3e3488fd38b74bbe9180f20bac28766a30e))
- spell mistake ([ba422ab](https://github.com/peddleon/ped-ux-react-library/commit/ba422ab43393b52d2a82b8536e4faf7a11a42f78))
- stories for typography and types for it ([8a423e6](https://github.com/peddleon/ped-ux-react-library/commit/8a423e630486dc6aea51539fe987083110727ba7))
- storybook file names and types for button ([c647423](https://github.com/peddleon/ped-ux-react-library/commit/c64742356826cb0e3aa1c5332bd5ed58dfacdffd))
- storybook for the table header ([d4bfa30](https://github.com/peddleon/ped-ux-react-library/commit/d4bfa30edb7e0efef53812cdd7415bf7d4ef3be8))
- storybook out directory changes ([06cfbac](https://github.com/peddleon/ped-ux-react-library/commit/06cfbacb93e841185ad8ac91979f2705c00b7a73))
- styling for the application ([cad9a3a](https://github.com/peddleon/ped-ux-react-library/commit/cad9a3a2bb1148498348ad718a1ccf6c1cb2a2e9))
- styling issue with application layout ([54daeff](https://github.com/peddleon/ped-ux-react-library/commit/54daefff81e538be8bb658ee5f664104447d9409))
- table component styles and application layout env label ([b945523](https://github.com/peddleon/ped-ux-react-library/commit/b9455239de619ae868e66fb63c960ebd7f9d59a2))
- table component styles and application layout env label ([f5f44ff](https://github.com/peddleon/ped-ux-react-library/commit/f5f44ff2c82aab960548b09825474e5381d37879))
- table row and header component ([e515e32](https://github.com/peddleon/ped-ux-react-library/commit/e515e326ee5d2cbce5f4304346ab30421250cf0b))
- **TablePagination:** gap styling with variables ([a7faeb9](https://github.com/peddleon/ped-ux-react-library/commit/a7faeb9a0b67bae676194ef1a60b8699973fc5bb))
- test cases and structure of navigation for mobile ([6c27904](https://github.com/peddleon/ped-ux-react-library/commit/6c27904b3a0eac1b5c0ac575807366f2240fd1d4))
- theme for inverted background ([a127a32](https://github.com/peddleon/ped-ux-react-library/commit/a127a323dac9fb96e43cc1ac78a7b1cb0ea97da2))
- theme for inverted background ([0dd458a](https://github.com/peddleon/ped-ux-react-library/commit/0dd458a81787a5aeb04b7f553bece77662ca1c0f))
- theme name update ([275a513](https://github.com/peddleon/ped-ux-react-library/commit/275a51323530456aaec762c3d35380c7e44c7d2a))
- update build storybook command ([051a091](https://github.com/peddleon/ped-ux-react-library/commit/051a091a3767e7345323dc7c8a0ff512dce6f670))
- update name for jest coverage ([ca6a179](https://github.com/peddleon/ped-ux-react-library/commit/ca6a17939a1fcb991a632292a9f2dcd6220d8ed7))
- update package lock ([7c684ef](https://github.com/peddleon/ped-ux-react-library/commit/7c684ef7ee9543964c5ffdbbf5bd8f64d1e7a083))
- update package.lock ([92832f8](https://github.com/peddleon/ped-ux-react-library/commit/92832f8cc9021b4b3fdbd983f1faf087b652586e))

## 1.0.0 (2024-04-22)
