import type { ReactNode } from 'react';
import classNames from '@utils/classNames';

import styles from './FluidRoot.module.css';

interface FluidRootProps {
  isFluid?: boolean;
  children: ReactNode;
}

/* FluidRoot sets CSS custom properties used by variables.module.css and
 * typography.module.css. It should be placed as high as possible in the
 * component hierarchy to set initial values, and can be set at arbitrary points
 * in the component hierarchy to switch fluid variants on or off.
 *
 * See FluidRoot.module.css for specifics.
 */
const FluidRoot = ({ isFluid = false, children }: FluidRootProps) => (
  <div className={classNames(isFluid ? styles.fluid : styles.static)}>
    {children}
  </div>
);

export default FluidRoot;
