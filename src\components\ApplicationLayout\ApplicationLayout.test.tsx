import React from 'react';
import { render } from '@testing-library/react';

import ApplicationLayout from './ApplicationLayout';

describe('ApplicationLayout', () => {
  beforeAll(() => {
    // Mock document.scrollingElement
    Object.defineProperty(document, 'scrollingElement', {
      value: document.createElement('div'),
      writable: true,
    });
  });

  it('renders with custom props', () => {
    const customProps = {
      backgroundColor: '#ffffff',
      mobileBackgroundColor: '#f0f0f0',
      isWideContainer: true,
      isFullContainer: true,
      isFluid: true,
      isAdminFrameShown: true,
      adminFrameLabel: 'Admin Frame',
      adminFrameRight: <div>Admin Frame Right</div>,
      adminFrameLeft: <div>Admin Frame Left</div>,
      environment: 'Production',
      isOverflowHidden: true,
      hasBackgroundTransition: false,
      children: <div>Main Content</div>,
      footer: <div>Footer Content</div>,
      isStickOnTop: true,
      navigationProps: {
        onNavigationClick: () => {},
        logo: '',
        logoUrl: '/',
        leftNavItems: [
          {
            label: 'Get Offer1',
          },
        ],
        rightNavItems: [
          {
            label: 'Get Offer',
          },
        ],
        mobileNavButton: [],
        mobileMenuButtons: null,
      },
    };

    const { getByText } = render(<ApplicationLayout {...customProps} />);

    expect(getByText('Get Offer1')).toBeInTheDocument();
    expect(getByText('Get Offer')).toBeInTheDocument();
  });
});
