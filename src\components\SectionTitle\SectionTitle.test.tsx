import { render } from '@testing-library/react';
import SectionTitle from '@components/SectionTitle';

describe('SectionTitle Component', () => {
  // Test case: Renders with heading text
  test('renders with heading text', () => {
    const { getByText } = render(
      <SectionTitle
        headingText="Testing Title 1"
        icon={null}
        actions={null}
        headingTag="h1"
      />,
    );

    // Expectation: Heading text should be in the document
    expect(getByText('Testing Title 1')).toBeInTheDocument();
    // Expectation: Heading text should be visible
    expect(getByText('Testing Title 1')).toBeVisible();
  });

  // Test case: Renders with icon
  test('renders with icon', () => {
    const { getByTestId } = render(
      <SectionTitle
        headingText="Test Heading"
        icon={<div data-testid="icon" />}
      />,
    );

    // Expectation: Icon should be in the document
    expect(getByTestId('icon')).toBeInTheDocument();
  });

  // Test case: Renders with actions at left of screen and right of heading
  test('renders with actions at left of screen and right of heading', () => {
    const { getByText } = render(
      <SectionTitle
        headingText="Test Heading"
        actions={[
          <button key="1" type="button">
            Action 1
          </button>,
          <button key="2" type="button">
            Action 2
          </button>,
        ]}
        actionDirection="left"
      />,
    );

    // Expectation: Action 1 should be in the document
    expect(getByText('Action 1')).toBeInTheDocument();
    // Expectation: Parent node of Action 2 should have class 'actionsLeft'
    expect(getByText('Action 2').parentNode.parentNode).toHaveClass(
      'actionsLeft',
    );
  });

  // Test case: Renders with heading tag h3
  test('renders with heading tag h3', () => {
    const { container } = render(
      <SectionTitle headingText="Test Heading" headingTag="h3" />,
    );

    // Expectation: Element with tag 'h3' should be in the document
    const headingElement = container.querySelector('h3');
    expect(headingElement).toBeInTheDocument();
  });
});
