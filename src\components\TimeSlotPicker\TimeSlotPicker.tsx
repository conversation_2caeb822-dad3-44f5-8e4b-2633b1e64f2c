import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import AnimatedLoader from '@components/AnimatedLoader';
import Scrims from '@components/Scrims';
import { SCHEDULE_TIMESLOT_RADIO_BUTTON } from '@constants/dataTestId';
import classNames from '@utils/classNames';
import { formatDate } from '@utils/date';

import TimeSlotPickerHeader from './TimeSlotPickerHeader/TimeSlotPickerHeader';
import TimeSlotPickerRow from './TimeSlotPickerRow/TimeSlotPickerRow';
import styles from './TimeSlotPicker.module.css';
import { TimeSlotPickerPropTypes } from './TimeSlotPicker.types';

/**
 * The TimeSlotPicker component is a React component used for selecting time slots from a list of available options.
 * @param {Object} props - The props for the TimeSlotPicker component.
 * @param {TimeSlotPickerRowPropTypes[]} props.rowItems - An array of objects representing time slot rows.
 * @param {string} props.timeZone - The time zone string for displaying time slots.
 * @param {string} props.value - The selected time slot value.
 * @param {Function} props.onSelect - The handler function for time slot selection.
 * @param {boolean} [props.isLoading=false] - Optional. Specifies whether the component is in loading state.
 * @param {boolean} [props.isDisabled=false] - Optional. Specifies whether the component is disabled.
 * @param {boolean} [props.hasDateCarouselHeader=true] - Optional. Specifies whether the component has a date carousel header.
 * @param {Function} [props.onChatWithAnAgentClick] - Optional. The Click handler function to take action.
 * @returns {JSX.Element} - The rendered TimeSlotPicker component.
 */
const TimeSlotPicker: FC<TimeSlotPickerPropTypes> = ({
  rowItems,
  timeZone = undefined,
  value,
  onSelect,
  isLoading,
  isDisabled,
  hasDateCarouselHeader = true,
  renderEmptyState,
}) => {
  const listRef = useRef<HTMLUListElement>(null);
  const rowsRef = useRef(new Array(rowItems.length));
  const [activeDate, setActiveDate] = useState(null);

  const scrollToRow = index => {
    const $row = rowsRef.current[index];
    if ($row) {
      $row.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  };

  const handleClick = useCallback(
    ({ event, date, index }) => {
      event.preventDefault();

      setActiveDate(date);
      scrollToRow(index);
    },
    [setActiveDate],
  );

  const handleSelect = useCallback(
    ({ id, startAt, endAt, timeStartAt, timeEndAt }) => {
      setActiveDate(startAt);
      onSelect({ id, startAt, endAt, timeStartAt, timeEndAt });
    },
    [setActiveDate, onSelect],
  );

  useEffect(() => {
    if (rowItems.length > 0) {
      if (value) {
        const valueDate = new Date(value);
        const valueDateISO = valueDate.toISOString();
        if (valueDate.getTime() !== new Date(activeDate).getTime()) {
          setActiveDate(valueDate);
        }
        const valueIndex = rowItems.findIndex(item => {
          const itemDateISO = new Date(item.date).toISOString();
          return valueDateISO.split('T')[0] === itemDateISO.split('T')[0];
        });

        if (valueIndex) {
          scrollToRow(valueIndex);
        }
      } else {
        setActiveDate(rowItems[0].date);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value, rowItems]);

  const headerItems = useMemo(() => rowItems, [rowItems]);

  const renderLoadingState = isLoading && (
    <div className={styles.loader}>
      <AnimatedLoader />
    </div>
  );

  const renderEmptyOrLoadingState = renderLoadingState || renderEmptyState;

  return (
    <div className={styles.picker}>
      {renderEmptyOrLoadingState || (
        <>
          {hasDateCarouselHeader && (
            <div className={styles.headerWrapper}>
              <TimeSlotPickerHeader
                items={headerItems}
                activeDate={activeDate}
                onClick={handleClick}
              />
            </div>
          )}

          <div className={styles.container}>
            <ul
              className={styles.list}
              ref={$el => {
                listRef.current = $el;
              }}
            >
              {rowItems.map(({ date, items: slotItems }, index) => (
                <div
                  key={date.toString()}
                  className={classNames(
                    styles.rowWrapper,
                    formatDate(date) === formatDate(activeDate) &&
                      styles.active,
                  )}
                  data-id={SCHEDULE_TIMESLOT_RADIO_BUTTON}
                >
                  <TimeSlotPickerRow
                    dataTestId={`${SCHEDULE_TIMESLOT_RADIO_BUTTON}${index}`}
                    value={value}
                    date={date}
                    timeZone={timeZone}
                    items={slotItems}
                    ref={$el => {
                      rowsRef.current[index] = $el;
                    }}
                    onSelect={handleSelect}
                    isDisabled={isDisabled}
                  />
                </div>
              ))}
            </ul>
            <Scrims overflowRef={listRef} />
          </div>
        </>
      )}
    </div>
  );
};

export default TimeSlotPicker;
