/* eslint-disable react-hooks/exhaustive-deps */ // This comment is here to disable the exhaustive-deps rule for the entire file due to multiple issues with adding dependency
import React, { useEffect, useState } from 'react';
import Button from '@components/Button';

import styles from './TablePagination.module.css';

interface PaginationRangeButtonsProps {
  pagination: number[];
  paginationBtn: (
    item: number,
  ) => 'darkInverted' | 'grey' | 'greyInverted' | 'transparent' | 'dark';
  table: {
    getState: () => {
      pagination: {
        pageIndex: number;
        pageSize: number;
      };
    };
    setPageIndex: (index: number) => void;
    getRowCount: () => number;
  };
}

const PaginationRangeButtons: React.FC<PaginationRangeButtonsProps> = ({
  pagination,
  paginationBtn,
  table,
}) => {
  const [paginationRange, setPaginationRange] = useState<number[]>([]);
  const currentPage = table.getState().pagination.pageIndex + 1;
  const rowCount = table.getRowCount();
  const { pageSize } = table.getState().pagination;

  useEffect(() => {
    if (paginationRange.includes(currentPage)) return;

    const hasPlusOneCurrentPage = paginationRange.includes(currentPage - 1);
    const hasMinusOneCurrentPage = paginationRange.includes(currentPage + 1);

    if (hasPlusOneCurrentPage) {
      setPaginationRange(paginationRange.map(item => item + 1));
      return;
    }

    if (hasMinusOneCurrentPage) {
      setPaginationRange(paginationRange.map(item => item - 1));
      return;
    }

    setPaginationRange(pagination);
  }, [currentPage]);

  useEffect(() => {
    setPaginationRange(pagination);
  }, [rowCount, pageSize]);

  return (
    <>
      {paginationRange.map(item => (
        <Button
          size="small"
          key={item} // Added key prop to fix a React warning about unique keys
          label={`${item}`}
          theme={paginationBtn(item)}
          onClick={() => {
            table.setPageIndex(item - 1);
          }}
          className={styles.paginationButton}
        />
      ))}
    </>
  );
};

export default PaginationRangeButtons;
