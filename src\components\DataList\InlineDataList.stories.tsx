import React from 'react';
import { Meta } from '@storybook/react';
import { InlineDataList, InlineDataListItem } from '@components/DataList';
import variableStyles from '@styles/variables.module.css';

export default {
  title: 'Components/Lists/Inline Data List',
  component: InlineDataList,
} as Meta;

const Template = ({ width, ...props }) => (
  <div
    style={{
      padding: '1rem',
      backgroundColor: props.isInverted
        ? variableStyles.black
        : variableStyles.white,
    }}
  >
    <div style={{ width }}>
      <InlineDataList {...props}>
        <InlineDataListItem term="Year" details="2007" isEditable />
        <InlineDataListItem term="Make" details="Chevrolet" isEditable />
        <InlineDataListItem term="Model" details="Malibu" isEditable />
        <InlineDataListItem term="Trim" details="LS w/1FL" isEditable />
        <InlineDataListItem term="Empty state" isEditable />
      </InlineDataList>
    </div>
  </div>
);

export const InlineDataListStory = Template.bind({});
InlineDataListStory.args = {
  width: 240,
  isInverted: false,
};

InlineDataListStory.argTypes = {
  width: {
    control: 'number',
  },
};
