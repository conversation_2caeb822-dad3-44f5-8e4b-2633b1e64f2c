// import React from 'react';
// import { Edit2Icon } from '@peddleon/ped-ux-react-icons';
// import classNames from '@utils/classNames';

// import styles from './InlineDataList.module.css';

// interface InlineDataListItemProps {
//   term: string;
//   details?: React.ReactNode;
//   isEditable?: boolean;
//   onEdit?: () => void;
//   className?: string;
// }

// const InlineDataListItem: React.FC<InlineDataListItemProps> = ({
//   term,
//   details,
//   isEditable = false,
//   onEdit,
//   className,
// }) => {
//   const inner = (
//     <>
//       <dt className={styles.dt}>{term}</dt>
//       <dd className={styles.dd}>
//         {details || <span className={styles.empty}>&mdash;</span>}
//         {isEditable && (
//           <span className={styles.editItemButton}>
//             <Edit2Icon height="16px" width="16px" />
//           </span>
//         )}
//       </dd>
//     </>
//   );

//   if (isEditable) {
//     return (
//       <button
//         className={classNames(styles.inlineDataListItem, styles.editable)}
//         type="button"
//         onClick={onEdit}
//       >
//         {inner}
//       </button>
//     );
//   }

//   return (
//     <div className={classNames(styles.inlineDataListItem, className)}>
//       {inner}
//     </div>
//   );
// };

// export default InlineDataListItem;
