@value variables: "../../../styles/variables.module.css";
@value oneSpace, threeSpace, primary, neutralDarkest, neutralDarker from variables;
@value typography: "../../../styles/typography.module.css";
@value bodyLarge, bodyBase from typography;

.list {
  composes: bodyLarge;
  font-weight: 500;
  color: neutralDarkest;
  list-style: none;
  padding: oneSpace;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;

  &:active {
    color: primary;
  }
}

.isSubList {
  margin-left: threeSpace;
}

.isActive {
  color: primary;
}

.icon {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.left {
  width: 100%;
  display: flex;
  align-items: center;
}

.leftIcon {
  margin-right: oneSpace;
}

.backBtn {
  margin-left: oneSpace;
  composes: bodyBase;
  font-weight: 700;
  color: neutralDarker;
}
