import { InputPropType, SelectItemType } from '@libtypes/common';

export interface SelectProps extends Omit<InputPropType, 'onChange'> {
  items: Array<SelectItemType>;
  value?: SelectItemType['value'];
  isMenuAlignedRight?: boolean;
  isActive?: boolean;
  isActiveControlled?: boolean;
  isFocused?: boolean;
  isMobileMenuModal?: boolean;
  caption?: string;
  onChange: (value: string) => void;
  onShouldClose?: () => void;
  onToggleButtonClick?: () => void;
  isMenuPositionTop?: boolean;
  isFixedMenu?: boolean;
  mobileModalPosition?: 'center' | 'bottom' | 'top';
  className?: string;
  hideErrorMessage?: boolean;
  title?: string;
}
