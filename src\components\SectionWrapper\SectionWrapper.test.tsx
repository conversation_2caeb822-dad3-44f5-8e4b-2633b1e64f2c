import { render } from '@testing-library/react';

import SectionWrapper from './SectionWrapper';

describe('SectionWrapper Component', () => {
  test('renders with heading and children', () => {
    const { getByText } = render(
      <SectionWrapper headingText="Test Heading">
        <div>Test Children</div>
      </SectionWrapper>,
    );

    expect(getByText('Test Heading')).toBeInTheDocument();
    expect(getByText('Test Children')).toBeInTheDocument();
  });

  test('renders with icon', () => {
    const { getByTestId } = render(
      <SectionWrapper
        headingText="Test Heading"
        icon={<div data-testid="icon" />}
      >
        <div>Test Children</div>
      </SectionWrapper>,
    );

    expect(getByTestId('icon')).toBeInTheDocument();
  });

  test('renders with actions', () => {
    const { getByText } = render(
      <SectionWrapper
        headingText="Test Heading"
        actions={[
          <button key="1" type="button">
            Action 1
          </button>,
        ]}
      >
        <div>Test Children</div>
      </SectionWrapper>,
    );

    expect(getByText('Action 1')).toBeInTheDocument();
  });

  test('renders divider when isDivider is true', () => {
    const { container } = render(
      <SectionWrapper headingText="Test Heading">
        <div>Test Children</div>
      </SectionWrapper>,
    );

    const dividerElement = container.querySelector('hr');

    expect(dividerElement).toBeInTheDocument();
  });

  test('renders divider with correct theme', () => {
    const { container } = render(
      <SectionWrapper
        headingText="Test Heading"
        options={{
          dividerTheme: 'dark',
          hasDevider: true,
        }}
      >
        <div>Test Children</div>
      </SectionWrapper>,
    );

    const dividerElement = container.querySelector('hr');

    expect(dividerElement).toHaveClass('darkTheme');
  });
});
