@value variables: "../../styles/variables.module.css";
@value twoSpace, fiveSpace, black50Opaque from variables;

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.errorBoundaryHeader {
  margin-top: fiveSpace;
}

.errorBoundaryBody {
  margin-top: twoSpace;
  color: black50Opaque;
  max-width: 45ch;
  margin-left: auto;
  margin-right: auto;
  white-space: pre-wrap;
  display: inline-block;
  width: 100%;
  word-wrap: break-word;
}
