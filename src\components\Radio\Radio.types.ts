import { ReactEventHandler } from 'react';

export interface RadioPropTypes {
  /**
   * Specifies whether the radio input is checked.
   */
  isChecked: boolean;
  /**
   * Specifies whether the radio input is disabled.
   */
  isDisabled?: boolean;
  /**
   * Specifies the tab index of the radio input.
   */
  tabIndex?: number;
  /**
   * Event handler for the onChange event of the radio input.
   */
  onChange?: ReactEventHandler<HTMLInputElement>;
  /**
   * Specifies the id attribute of the radio input.
   */
  id: string;
  /**
   * Specifies the label text associated with the radio input.
   */
  label?: string;
  /**
   * Specifies the name attribute of the radio input.
   */
  name?: string;
  /**
   * Specifies the value attribute of the radio input.
   */
  value?: string;
  /**
   * Additional class name(s) for custom styling.
   */
  className?: string;
  /**
   * Specifies whether the radio input should have a button-like appearance.
   */
  isInButtonShape?: boolean;
  /**
   * Can set a test ID to uniquely identify the component during testing.
   */
  dataTestId?: string;
}
