/* eslint-disable storybook/prefer-pascal-case */
import React from 'react';
import { Meta } from '@storybook/react';
import Button from '@components/Button';
import classNames from '@utils/classNames';

import { Typography } from '..';
import DetailedCTA from './DetailedCTA';
import styles from './DetailedCTA.module.css';
import { DetailedCTAPropTypes } from './DetailedCTA.types';
import Error404Illustration from './Error404Illustration';

/**
 * The DetailedCTA component displays a call-to-action with detailed information and an optional illustration.
 *
 * ## Overview
 *
 * The DetailedCTA component is used to present a call-to-action with additional details, such as a heading, body content, and an optional illustration. It provides flexibility in customization, allowing you to specify the size and position of the component.
 *
 * ## Usage
 *
 * To use the DetailedCTA component in your React application, import it from the appropriate directory and include it in your JSX.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { DetailedCTA } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the DetailedCTA component within your JSX:
 *
 * ```jsx
 * <DetailedCTA
 *   illustration={<Error404Illustration />}
 *   heading="No Offers"
 *   body="Once you start the process of selling a car through Peddle, your Offers will show up here."
 *   size="medium"
 *   action={<Button theme="warning" label="Get started" />}
 *   position="center"
 * />
 * ```
 *
 * This will render the DetailedCTA component with the specified details and customization options.
 */
const meta: Meta<typeof DetailedCTA> = {
  title: 'Components/DetailedCTA',
  tags: ['autodocs'],
  component: DetailedCTA,
};

export const detailedCTA = ({
  heading,
  body,
  size,
  action,
  position,
}: DetailedCTAPropTypes) => (
  <div style={{ height: '50rem', display: 'flex', width: '100%' }}>
    <DetailedCTA
      illustration={<Error404Illustration />}
      heading={heading}
      size={size}
      body={
        <Typography className={classNames(styles.body, styles[`${size}Body`])}>
          {body}
        </Typography>
      }
      action={action}
      position={position}
    />
  </div>
);

detailedCTA.args = {
  heading: 'No Offers',
  body: 'Once you start the process of selling a car through Peddle, your Offers will show up here.',
  size: 'medium',
  action: <Button theme="warning" label="Get started" />,
  position: 'center',
};
detailedCTA.argTypes = {
  size: {
    options: ['medium', 'small'],
    control: { type: 'select' },
  },
  position: {
    options: ['top', 'center'],
    control: { type: 'select' },
  },
};

export default meta;
