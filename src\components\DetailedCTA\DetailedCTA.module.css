@value variables: "../../styles/variables.module.css";
@value typography: "../../styles/typography.module.css";
@value h3Strong, h6Strong, bodyBase, bodySmall from typography;
@value tenSpace, black70, oneSpace, twoSpace, fiveSpace, black, black50, mediumWidth from variables;

.container {
  display: flex;
  flex-direction: column;
  text-align: center;
  width: 100%;
}

.illustrationWrapper {
  margin: 0 auto;
  max-width: 600px;
  width: 100%;
}

.heading {
  color: black;
  margin-top: fiveSpace;
}
.mediumHeading {
  composes: h3Strong;
}
.smallHeading {
  composes: h6Strong;
}

.body {
  color: black50;
  max-width: 37ch;
  margin-left: auto;
  margin-right: auto;
  white-space: pre-wrap;
  display: inline-block;
  width: 100%;
  word-wrap: break-word;
  @media (max-width: mediumWidth) {
    white-space: inherit;
  }
}
.mediumBody {
  composes: bodyBase;
  margin-top: twoSpace;
}
.smallBody {
  composes: bodySmall;
  margin-top: oneSpace;
}

.buttonWrapper {
  margin-top: fiveSpace;
}

.footer {
  composes: bodySmall;
  color: black70;
  margin-top: tenSpace;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.landScapeMode {
  padding: twoSpace 0px;
}
