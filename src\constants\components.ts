/** Dialog Modal */
// eslint-disable-next-line no-shadow
export enum ModalAction {
  VIEW = 'view',
  DISMISS = 'dismiss',
  ACCEPT = 'accept',
  DECLINE = 'decline',
}

/** Select Input */
export const SPACE_KEY_EVENT = 'Space';
export const ENTER_KEY_EVENT = 'Enter';
export const TAB_KEY_EVENT = 'Tab';
export const ARROW_DOWN_EVENT = 'ArrowDown';
export const ARROW_UP_EVENT = 'ArrowUp';
export const ESCAPE_EVENT = 'Escape';
export const ARROW_DOWN_KEYCODE_EVENT = 40;
export const ARROW_UP_KEYCODE_EVENT = 38;
export const SPACE_KEYCODE_EVENT = 32;
export const TAB_KEYCODE_EVENT = 9;
export const ENTER_KEYCODE_EVENT = 13;
export const ENTER_ESCAPE_EVENT = 27;

/** ModalPortal * */
export const GENERAL_MODAL_PORTAL_ID = 'modal-portal-root';
export const TOASTER_PORTAL_ID = 'toaster-modal-portal-root';

/** Table */
export const NO_RECORD_FOUND = 'No record found!';
export const TABLE_ASC_LABEL = 'Sort by ASC';
export const TABLE_ASC_LABEL_ID = 'sort_asc';
export const TABLE_DESC_LABEL = 'Sort by DESC';
export const TABLE_DESC_LABEL_ID = 'sort_desc';

export const TABLE_ASC_LABEL_STRING = 'Sort by A to Z';
export const TABLE_DESC_LABEL_STRING = 'Sort by Z to A';
export const TABLE_ASC_LABEL_NUMBER = 'Smallest to largest';
export const TABLE_DESC_LABEL_NUMBER = 'Largest to smallest';
export const TABLE_ASC_LABEL_DATE = 'Oldest first';
export const TABLE_DESC_LABEL_DATE = 'Newest first';

export const TABLE_NO_RECORD_DEFAULT_HEADER_TEXT = 'no results';
export const TABLE_NO_RECORD_DEFAULT_BODY_TEXT =
  'There are no results matching your search.';
