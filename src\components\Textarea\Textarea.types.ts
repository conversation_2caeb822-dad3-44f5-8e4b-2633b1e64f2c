import { ReactEventHandler } from 'react';
import { TextareaPropType } from '@libtypes/common';

export interface TextAreaInputProps extends TextareaPropType {
  minLength?: HTMLTextAreaElement['minLength'];

  maxLength?: HTMLTextAreaElement['maxLength'];

  isSquare?: boolean;

  isAutoFocused?: boolean;

  isFocused?: boolean;

  className?: string;

  onChange: ReactEventHandler<HTMLTextAreaElement>;

  inputMode?:
    | 'text'
    | 'email'
    | 'search'
    | 'tel'
    | 'url'
    | 'none'
    | 'numeric'
    | 'decimal';

  placeholder?: string;

  autoComplete?: string;

  hideRightIcon?: boolean;

  wrap?: 'hard' | 'soft' | 'off';

  resize?: 'none' | 'both' | 'horizontal' | 'vertical';

  rows?: number;

  isWrapped?: boolean;
}
