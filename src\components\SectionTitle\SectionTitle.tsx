import { FC } from 'react';
import Typography from '@components/Typography';
import classNames from '@utils/classNames';

import styles from './SectionTitle.module.css';
import { SectionTitleProps } from './SectionTitle.types';

const SectionTitle: FC<SectionTitleProps> = ({
  headingText,
  icon,
  actions,
  actionDirection,
  headingTag = 'h2',
  headingProps,
}) => (
  <div className={styles.header}>
    {icon && <div className={classNames(styles.icon)}>{icon}</div>}
    <div className={styles.headingWrapper}>
      <Typography tag={headingTag} {...headingProps}>
        {headingText}
      </Typography>
    </div>
    {actions && (
      <div
        className={classNames(
          styles.actions,
          actionDirection === 'left' && styles.actionsLeft,
        )}
      >
        {actions.map((action, index) => (
          // eslint-disable-next-line react/no-array-index-key
          <div key={`section-action-${index}`} className={styles.actions}>
            {action}
          </div>
        ))}
      </div>
    )}
  </div>
);

export default SectionTitle;
