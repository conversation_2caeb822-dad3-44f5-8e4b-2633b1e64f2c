@value variables: "../../../styles/variables.module.css";
@value smallWidth, white10, neutral, black5, twoSpace, black, white, black5, neutralLightest, neutralLightTable, oneSpace from variables;

.footerRoot {
  --footerColor: neutral;
  padding-left: oneSpace;
  cursor: pointer;
  align-items: center;
  background-color: neutral;
  color: white;
  transition: all 0.1s ease;

  &.isSecondary {
    background-color: neutralLightTable;
  }

  &.isLoading {
    pointer-events: none;
    background-color: white;
  }
  &.isFaded {
    opacity: 0.35;
  }

  &.rowClickable {
    cursor: pointer;
  }

  @media (max-width: smallWidth) {
    display: none;
  }
}

.footerText {
  padding: 16px;
}

.footerDevider {
  margin-top: 0;
  border-width: 1px;
  color: unset;

  &:hover {
    background-color: black;
  }
}

.tableFooterWrapper {
  display: flex;
  justify-content: space-between;
  min-height: 60px;
  align-items: center;
}

.tableFooterWrapperMobile {
  border: 1px solid black5;
  margin: 0;
  font-size: 16px;
  padding-left: 0;
}
