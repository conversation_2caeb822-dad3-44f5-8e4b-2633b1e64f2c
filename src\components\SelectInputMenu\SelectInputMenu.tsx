import type { ComponentPropsWithRef, ReactNode } from 'react';
import { ForwardedRef, forwardRef, useRef } from 'react';
import Scrims from '@components/Scrims';
import classNames from '@utils/classNames';

import styles from './SelectInputMenu.module.css';

interface SelectInputMenuProps {
  isOpen: boolean;
  isPoweredByGoogle?: boolean;
  isAlignedRight?: boolean;
  getMenuProps: ({
    ref,
  }: {
    ref: ForwardedRef<HTMLUListElement>;
  }) => ComponentPropsWithRef<'ul'>;
  children: ReactNode;
  className?: string;
  listClassName?: string;
  footer?: ReactNode;
  onBlur?: () => void;
  hasListRef?: boolean;
  isMenuPositionTop?: boolean;
}

const SelectInputMenu = forwardRef<HTMLDivElement, SelectInputMenuProps>(
  (
    {
      isOpen,
      getMenuProps,
      isAlignedRight = false,
      children,
      className = null,
      listClassName = null,
      footer = null,
      onBlur = () => {},
      hasListRef = false,
      isMenuPositionTop = false,
      isPoweredByGoogle = false,
    }: SelectInputMenuProps,
    ref,
  ) => {
    const listRef = useRef<HTMLUListElement>();
    let extraListProps = {};

    if (hasListRef) {
      extraListProps = { ref: listRef };
    }

    return (
      <div
        ref={ref}
        className={classNames(
          styles.menu,
          isOpen && styles.visible,
          isAlignedRight && styles.alignedRight,
          isMenuPositionTop && styles.menuPositionTop,
          className,
        )}
      >
        <div className={styles.container}>
          <ul
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            {...(getMenuProps as any)(
              { ref: listRef, onBlur },
              { suppressRefError: true },
            )}
            className={classNames(styles.list, listClassName)}
            {...extraListProps}
          >
            {isOpen && children}
          </ul>
          {isOpen && <Scrims overflowRef={listRef} />}
        </div>

        {isPoweredByGoogle && isOpen && children && (
          <div className={styles.googleFooter}>
            <img
              src="https://peddle-public-staging.imgix.net/images/powered-by-google.png"
              alt="Powered by Google"
            />
          </div>
        )}
        {footer}
      </div>
    );
  },
);

export default SelectInputMenu;
