import type { ReactEventHandler, ReactNode } from 'react';
import Tag from '@components/Tag';
import classNames from '@utils/classNames';

import styles from './Frame.module.css';

interface FrameProps {
  onClick?: ReactEventHandler;
  label: string;
  isBorderShown?: boolean;
  isCollapsed?: boolean;
  isOffset?: boolean;
  right?: ReactNode;
  left?: ReactNode;
}

const Frame = ({
  onClick,
  label,
  isBorderShown = false,
  isCollapsed = false,
  isOffset = false,
  right,
  left,
}: FrameProps) => (
  <>
    <div
      className={classNames(
        styles.frame,
        isBorderShown && styles.showBorder,
        isCollapsed && styles.collapsed,
        isOffset && styles.offset,
      )}
    >
      <div className={styles.containerMobile}>
        {left && <span className={styles.leftButton}>{left}</span>}
        <span data-testid="frame-label" className={styles.label}>
          {label}
        </span>
        {right && <span className={styles.rightButton}>{right}</span>}
      </div>
      <div className={styles.containerDesktop}>
        <Tag
          label={label}
          labelClassName={styles.adminFrameLabel}
          theme="warning"
          type={onClick ? 'button' : 'label'}
          right={right}
          left={left}
          onClick={onClick}
          className={styles.tag}
        />
      </div>
    </div>
    <div
      className={classNames(
        styles.frame,
        isBorderShown && styles.showBorder,
        isBorderShown && styles.zeroRadiusBorder,
      )}
    />
  </>
);

// TODO - this is duplicated in CSS, ideally remove that duplication somehow
export const MOBILE_FRAME_HEIGHT = 48;
export const MOBILE_FRAME_HEIGHT_COLLAPSED = 5;

export default Frame;
