import React, { useState } from 'react';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

import Toggle from './Toggle';

export default {
  title: 'Components/Inputs',
};
export const ToggleSheet = () => {
  const [state, setState] = useState({
    enabledOff: false,
    enabledOn: true,
    disabledOff: false,
    disabledOn: true,
  });

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <ComponentGrid countColumns={4}>
        <ComponentTile label="Enabled / Off">
          <Toggle
            id={1}
            isChecked={state.enabledOff}
            onChange={event =>
              setState({ ...state, enabledOff: event.target.checked })
            }
          />
        </ComponentTile>
        <ComponentTile label="Enabled / On">
          <Toggle
            id={2}
            isChecked={state.enabledOn}
            onChange={event =>
              setState({ ...state, enabledOn: event.target.checked })
            }
          />
        </ComponentTile>
        <ComponentTile label="Disabled / Off">
          <Toggle id={3} isDisabled isChecked={state.disabledOff} />
        </ComponentTile>
        <ComponentTile label="Disabled / On">
          <Toggle id={4} isDisabled isChecked={state.disabledOn} />
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
};
