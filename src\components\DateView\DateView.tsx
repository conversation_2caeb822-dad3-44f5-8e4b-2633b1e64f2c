import Typography from '@components/Typography';
import classNames from '@utils/classNames';
import { formatDay, formatMonth, formatWeekday } from '@utils/date';

import styles from './DateView.module.css';
import { DateViewProps } from './DateView.types';

const DataView = ({
  date,
  monthClassName,
  weekdayClassName,
}: DateViewProps) => (
  <>
    <Typography
      className={classNames(styles.month, monthClassName)}
      variant="captionStrong"
    >
      {formatMonth(date)}
    </Typography>
    <Typography variant="h5Strong">{formatDay(date)}</Typography>
    <Typography
      className={classNames(styles.weekday, weekdayClassName)}
      variant="overline"
    >
      {formatWeekday(date)}
    </Typography>
  </>
);

export default DataView;
