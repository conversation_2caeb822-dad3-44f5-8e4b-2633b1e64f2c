@value variables: "../../../styles/variables.module.css";
@value smallWidth, largeWidth, primary, black, white, twoSpace, oneSpace, mediumWidth from variables;

.root {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: twoSpace;
  flex-wrap: wrap;
  gap: twoSpace;

  @media (max-width: mediumWidth) {
    flex-direction: row-reverse;
  }
}

.pageRangeRoot {
  align-items: center;
  width: 425px;
  display: flex;
  gap: twoSpace;

  @media (max-width: mediumWidth) {
    display: flex;
    justify-content: space-between;
    width: 100%;
    align-items: center;
  }
}

.paginationDropdown {
  width: 425px;
  display: flex;
  gap: oneSpace;

  & .pageSize {
    width: 168px;
  }
  & .pageStatistics {
    width: 250px;
  }

  @media (max-width: smallWidth) {
    flex-wrap: wrap;
  }
  @media (max-width: mediumWidth) {
    width: 100%;

    & .pageSize {
      width: 100%;
    }
    & .pageStatistics {
      width: 100%;
    }
  }
}

.isInverted {
  color: white;
  background-color: black;
}

.dotes {
  font-size: 1.5rem;
}

.paginationRoot {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: oneSpace;

  @media (max-width: mediumWidth) {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: oneSpace;
    justify-content: center;
    width: 100%;
  }
}

.paginationButton {
  height: 44px;
  width: 44px;
  padding-right: unset !important;
  padding-left: unset !important;
  min-height: unset !important;
  justify-content: center;

  & > span {
    height: 24px;
    width: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  @media (max-width: mediumWidth) {
    min-width: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.pageRangeWithSelect {
  @media (max-width: mediumWidth) {
    display: none;
  }
}

.mobilePageRange {
  display: none;
  @media (max-width: mediumWidth) {
    width: 100%;
    justify-content: center;
    display: flex;
  }
}

.hideDesktop {
  @media (min-width: mediumWidth) {
    display: none;
  }
}
