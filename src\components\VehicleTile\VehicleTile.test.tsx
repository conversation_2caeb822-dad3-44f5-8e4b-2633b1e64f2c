import React from 'react';
import { render, screen } from '@testing-library/react';

import VehicleTile from './VehicleTile';

describe('VehicleTile', () => {
  it('renders vehicle details correctly', () => {
    render(
      <VehicleTile
        vehicle={{
          year: { label: '2007' },
          make: { label: 'Chevrolet' },
          model: { label: 'Malibu' },
          trim: { label: 'LS w/1FL' },
          VIN: 'JH4TB2H26CC000000',
        }}
        status={{
          id: 0,
          label: 'Assigned',
          value: 'assigned',
          theme: 'warning',
        }}
        location={{
          city: 'Madison',
          stateCode: 'WI',
        }}
      />,
    );

    expect(screen.getByText('2007')).toBeInTheDocument();
    expect(screen.getByText('Chevrolet Malibu')).toBeInTheDocument();
    expect(screen.getByText('LS w/1FL')).toBeInTheDocument();
    expect(screen.getByText('Madison, WI')).toBeInTheDocument();
    expect(screen.getByText('JH4TB2H26CC000000')).toBeInTheDocument();
  });
});
