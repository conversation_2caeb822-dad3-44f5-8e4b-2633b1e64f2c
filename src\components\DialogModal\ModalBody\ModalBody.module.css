@value variables: "../../../styles/variables.module.css";
@value black70, smallWidth, mediumWidth, twoSpace, threeSpace, bodySmallFontSize from variables;
@value typography: "../../../styles/typography.module.css";
@value bodyBase from typography;

.body {
  composes: bodyBase;
  color: black70;
  max-width: 44ch;
  min-width: 400px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: mediumWidth) {
    min-width: 300px;
  }

  @media (max-width: smallWidth) {
    font-size: bodySmallFontSize;
  }
}

.containerWrapper {
  position: relative;
  display: flex;
  justify-content: center;
  flex: 1;
  min-height: 1px;
  overflow: hidden;
}

.containerScroll {
  overflow: auto;
  width: 100%;
}

.hasNoScroll {
  overflow: visible;
}

.container {
  text-align: center;
  @media (max-width: smallWidth) {
    padding-right: threeSpace;
    padding-left: threeSpace;
    padding-bottom: twoSpace;
  }
}
