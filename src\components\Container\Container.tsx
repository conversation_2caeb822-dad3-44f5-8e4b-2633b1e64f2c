import type { ReactNode } from 'react';
import React, { forwardRef } from 'react';
import styles from '@components/Container/Container.module.css';
import classNames from '@utils/classNames';

interface ContainerProps {
  backgroundColor?: string;
  mobileBackgroundColor?: string;
  isFull?: boolean;
  isWide?: boolean;
  children?: ReactNode;
  className?: string;
  wrapperClassName?: string;
  id?: string;
  jumpHashId?: string;
}

const Container = forwardRef<HTMLDivElement, ContainerProps>(
  (
    {
      isFull = false,
      isWide = false,
      children = [],
      backgroundColor = 'transparent',
      mobileBackgroundColor = 'transparent',
      className = '',
      wrapperClassName = '',
      id = null,
      jumpHashId = null,
    }: ContainerProps,
    ref,
  ) => (
    <div
      ref={ref}
      className={classNames(
        styles.container,
        isWide && styles.wide,
        isFull && styles.full,
        className,
      )}
      style={{
        '--backgroundColor': backgroundColor,
        '--mobileBackgroundColor': mobileBackgroundColor,
      }}
      id={id}
      data-jump-hash={jumpHashId}
    >
      <div className={classNames(styles.containerWrapper, wrapperClassName)}>
        {children}
      </div>
    </div>
  ),
);

export default Container;
