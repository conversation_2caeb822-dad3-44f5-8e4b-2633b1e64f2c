import { ChangeEvent, useEffect, useState } from 'react';
import { Meta } from '@storybook/react';
import FieldLabel from '@components/FieldLabel';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import typographyStyles from '@styles/typography.module.css';
import variableStyles from '@styles/variables.module.css';

import Textarea from './Textarea';
import { TextAreaInputProps } from './Textarea.types';

/**
 * The Textarea component provides an input interface for users to enter multi-line text, with various customization options available.
 *
 * ## Overview
 *
 * The Textarea component allows developers to create multi-line input fields for text entry in their React applications. It supports different types of inputs, such as plain text, email, password, and more, and provides flexibility in styling and behavior.
 *
 * ## Usage
 *
 * To use the Textarea component in your React application, import it from the appropriate directory and render it with the desired props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { Textarea } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the Textarea component in your JSX:
 *
 * ```jsx
 * <Textarea
 *   label="Label"
 *   onChange={() => {}}
 *   placeholder="Placeholder"
 *   wrap="hard"
 *   resize="vertical"
 *   rows={4}
 *   value=""
 * />
 * ```
 *
 * This will render a multi-line input field with a label, placeholder text, and controlled value.
 *
 */

const meta: Meta<typeof Textarea> = {
  title: 'Components/Textarea',
  tags: ['autodocs'],
  component: Textarea,
};

export const TextareaStory = (args: TextAreaInputProps) => {
  const [state, setState] = useState(null);
  const { value: argValue } = args;
  useEffect(() => setState(argValue), [argValue]);
  return (
    <Textarea
      {...args}
      value={state}
      onChange={(event: ChangeEvent<HTMLTextAreaElement>) => {
        const { value } = event.target;
        setState(value);
      }}
    />
  );
};
TextareaStory.args = {
  id: 'example-id',
  label: 'Label',
  name: 'example-name',
  caption: 'Example Caption',
  value: '',
  placeholder: 'Placeholder',
  left: null,
  right: null,
  type: 'text',
  minLength: null,
  maxLength: null,
  hasSucceeded: false,
  isLoading: false,
  isDisabled: false,
  isBorderless: false,
  isSquare: false,
  isFocused: false,
  onChange: () => {},
  onFocus: () => {},
  onBlur: () => {},
  onInvalid: () => {},
  isAutoFocused: false,
  className: '',
  inputMode: undefined,
  pattern: '',
  required: false,
  onKeyDown: () => {},
  onClick: () => {},
  onPaste: () => {},
  onCut: () => {},
  autoComplete: '',
  hideRightIcon: false,
  wrap: 'hard',
  resize: 'vertical',
  rows: 4,
  isWrapped: true,
};
TextareaStory.argTypes = {
  id: {
    control: 'text',
    description: 'A unique identifier for the input element.',
  },
  label: {
    control: 'text',
    description: 'The label displayed alongside the input element.',
  },
  name: {
    control: 'text',
    description: 'The name attribute of the input element.',
  },
  error: {
    control: 'text',
    description: 'An error message to display when the input value is invalid.',
  },
  caption: {
    control: 'text',
    description:
      'A supplementary caption or additional information related to the input.',
  },
  value: {
    control: 'text',
    description: 'The current value of the input element.',
  },
  placeholder: {
    control: 'text',
    description:
      'The placeholder text displayed when the input field is empty.',
  },
  left: {
    control: 'none',
    description: 'An element to display on the left side of the input.',
  },
  right: {
    control: 'none',
    description: 'An element to display on the right side of the input.',
  },
  type: {
    control: 'select',
    options: ['text', 'email', 'password', 'number', 'search', 'tel', 'url'],
    description:
      'The type of input element to display (e.g., text, email, password).',
  },
  minLength: {
    control: 'number',
    description: 'The minimum length of the input value.',
  },
  maxLength: {
    control: 'number',
    description: 'The maximum length of the input value.',
  },
  hasSucceeded: {
    control: 'boolean',
    description:
      'Indicates whether the input has succeeded (e.g., successfully submitted).',
  },
  isLoading: {
    control: 'boolean',
    description:
      'Indicates whether data is being fetched or processed for the input.',
  },
  isDisabled: {
    control: 'boolean',
    description: 'Indicates whether the input element is disabled.',
  },
  isBorderless: {
    control: 'boolean',
    description: 'Indicates whether the input element has a borderless style.',
  },
  isSquare: {
    control: 'boolean',
    description: 'Indicates whether the input element has a square style.',
  },
  isFocused: {
    control: 'boolean',
    description: 'Indicates whether the input element is currently focused.',
  },
  onChange: {
    action: 'changed',
    description: 'Callback function invoked when the input value changes.',
  },
  onFocus: {
    action: 'focused',
    description:
      'Callback function invoked when the input element receives focus.',
  },
  onBlur: {
    action: 'blurred',
    description:
      'Callback function invoked when the input element loses focus.',
  },
  onInvalid: {
    action: 'invalid',
    description:
      'Callback function invoked when the input value is deemed invalid.',
  },
  isAutoFocused: {
    control: 'boolean',
    description:
      'Indicates whether the input element should be automatically focused.',
  },
  className: {
    control: 'text',
    description: 'Additional CSS class names to apply to the input element.',
  },
  inputMode: {
    control: 'text',
    description:
      'The input mode (e.g., text, email, search) for mobile devices.',
  },
  pattern: {
    control: 'text',
    description: 'A regular expression pattern for validating the input value.',
  },
  required: {
    control: 'boolean',
    description: 'Indicates whether the input is required to be filled.',
  },
  resize: {
    control: 'select',
    options: ['none', 'both', 'horizontal', 'vertical'],
    description: 'Specifies whether and how the input field can be resized.',
  },
  onKeyDown: {
    action: 'key down',
    description:
      'Callback function invoked when a key is pressed while the input element is focused.',
  },
  onClick: {
    action: 'clicked',
    description: 'Callback function invoked when the input element is clicked.',
  },
  onPaste: {
    action: 'pasted',
    description:
      'Callback function invoked when content is pasted into the input element.',
  },
  onCut: {
    action: 'cut',
    description:
      'Callback function invoked when content is cut from the input element.',
  },
  autoComplete: {
    control: 'text',
    description:
      'Specifies whether the input field should have autocomplete enabled.',
  },
  hideRightIcon: {
    control: 'boolean',
    description: 'Indicates whether the right icon should be hidden.',
  },
  wrap: {
    control: 'select',
    options: ['hard', 'soft', 'off'],
    description: `Specifies how the text in the textarea should be wrapped when it reaches the end of a line. Options include "hard" (line breaks inserted at the specified width), "soft" (line breaks are not inserted, but the text wraps when it reaches the container's edge), and "off" (no wrapping).`,
  },
  rows: {
    control: 'number',
    description:
      'Specifies the visible number of lines in the textarea. This helps define the initial size of the textarea, but it can still expand dynamically as the user enters more text beyond these initial rows.',
  },
};

export const TextareaSheet = ({
  label,
  fieldLabel,
  placeholder,
  error,
  caption,
  rightOverline,
}: {
  label: string;
  fieldLabel: string;
  placeholder: string;
  error: string;
  caption: string;
  rightOverline: string;
}) => {
  const [state, setState] = useState({
    standard: '',
    withLeft: '',
    withRight: '',
  });

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <ComponentGrid countColumns={2}>
        <ComponentTile label="Standard">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.standard}
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                standard: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Is focused">
          <Textarea
            isFocused
            label={label}
            placeholder={placeholder}
            value={state.standard}
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                standard: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Caption">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            caption={caption}
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Caption">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            caption={caption}
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Error">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            error={error}
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Error / Caption">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            error={error}
            caption={caption}
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Error">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            error={error}
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Error / No Message">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.standard}
            error={error}
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                standard: event.target.value,
              })
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Error / No Message">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            error={error}
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Right overline / Error / No Message">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            error={error}
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Success">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            hasSucceeded
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Success">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            hasSucceeded
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Loading">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            isLoading
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Loading">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            isLoading
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Disabled">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withLeft}
            isDisabled
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withLeft: event.target.value,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Disabled">
          <Textarea
            label={label}
            placeholder={placeholder}
            value={state.withRight}
            isDisabled
            onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
              setState({
                ...state,
                withRight: event.target.value,
              })
            }
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Field label">
          <div style={{ textAlign: 'left' }}>
            <FieldLabel label={fieldLabel} />
            <Textarea
              label={label}
              placeholder={placeholder}
              value={state.withLeft}
              onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
                setState({
                  ...state,
                  withLeft: event.target.value,
                })
              }
            />
          </div>
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Field label">
          <div style={{ textAlign: 'left' }}>
            <FieldLabel label={fieldLabel} />
            <Textarea
              label={label}
              placeholder={placeholder}
              value={state.withRight}
              onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
                setState({
                  ...state,
                  withRight: event.target.value,
                })
              }
              right={
                <span className={typographyStyles.overline}>
                  {rightOverline}
                </span>
              }
            />
          </div>
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
};

TextareaSheet.args = {
  label: 'Label',
  fieldLabel: 'Field label',
  placeholder: 'Placeholder',
  error: 'Input error message',
  caption: 'Input caption message',
  rightOverline: 'UNIT',
};

export default meta;
