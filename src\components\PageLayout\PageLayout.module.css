@value variables: "../../styles/variables.module.css";
@value smallWidth, ease-out-expo, twoSpace, black10, smallWidth, largeWidth, white, aboveZIndex, threeSpace, fiveSpace from variables;

.page {
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  align-items: stretch;
  width: 100%;
  height: 100%;
  position: relative;

  @media (max-width: smallWidth) {
    min-height: calc(100vh - var(--navigationHeight));
  }
}

.leftRightPadding {
  padding-left: twoSpace;
  padding-right: twoSpace;

  @media (max-width: smallWidth) {
    padding-left: 0;
    padding-right: 0;
  }
}

.sidebar {
  position: relative;
  min-width: 400px;
  max-width: 400px;
  width: 100%;
  overflow: auto;
  background: white;
  border-top-left-radius: 16px;
  z-index: aboveZIndex;
  border-right: solid 1px black10;

  @media (max-width: largeWidth) {
    border-top-left-radius: 0;
    width: 100%;
    min-width: smallWidth;
    max-width: smallWidth;
    position: absolute;
    bottom: var(--sellerLayoutFooterHeight);
    max-height: 52vh;
    overflow: hidden;
    background: none;
    border: none;
  }

  @media (max-width: smallWidth) {
    border-top-left-radius: 16px;
    position: fixed;
    width: 100%;
    max-width: auto;
    min-width: auto;
    left: 0;
    right: 0;
  }
}

.main {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: white;
  flex: 1;
  border-top-right-radius: 16px;
  border-top-left-radius: 16px;

  @media (max-width: smallWidth) {
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
  }

  @media (min-width: smallWidth) {
    border-top-left-radius: unset;
  }
}

.footerWrapper {
  position: relative;
  z-index: aboveZIndex;
  width: 100%;
  background: white;
  border-top: 1px solid black10;
  display: flex;
  flex-flow: row;
  flex: 0;
  justify-content: center;
  align-items: center;
  text-align: center;

  @media (max-width: smallWidth) {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100vw;
  }
}

.footerPortalRoot {
  padding: threeSpace fiveSpace;
  width: 100%;
  @media (max-width: smallWidth) {
    padding: twoSpace;
  }
}

.childrenWrapper {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  border-top-right-radius: 16px;
  border-top-left-radius: 16px;

  @media (max-width: smallWidth) {
    background: none;
  }
}

.sidebarContainerWrapper {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  @media (max-width: largeWidth) {
    max-height: 50vh;
    display: flex;
  }
}

.layout {
  --backgroundColor: white;
  --mobileBackgroundColor: white;

  height: 100%;
  width: 100%;
  display: flex;
  flex-flow: column;
  overflow-y: auto;
  background-color: var(--backgroundColor);

  @media (max-width: smallWidth) {
    background-color: var(--mobileBackgroundColor);
  }
  &.backgroundTransition {
    transition: background-color 0.5s ease-out-expo;
  }
}

.isOverflowHidden {
  overflow: hidden;
}
