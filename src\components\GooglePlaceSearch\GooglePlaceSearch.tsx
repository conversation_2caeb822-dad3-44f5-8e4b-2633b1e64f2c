// import React, { FC, useEffect, useRef, useState } from 'react';
// import { useCombobox, UseComboboxStateChange } from 'downshift';
// import { AlertTriangleIcon, CheckIcon } from '@peddleon/ped-ux-react-icons';
// import AnimatedLoader from '@components/AnimatedLoader';
// import SelectInputMenu from '@components/SelectInputMenu/SelectInputMenu';
// import SelectInputMenuItem from '@components/SelectInputMenu/SelectInputMenuItem';
// import { SelectItemType } from '@libtypes/common';
// import useDebouncedState from '@hooks/useDebouncedState';
// import classNames from '@utils/classNames';

// import styles from './GooglePlaceSearch.module.css';
// import { GooglePlaceSearchPropTypes } from './GooglePlaceSearch.types';
// import GooglePlacesScript from './GooglePlaceSearchScript';

// const GooglePlaceSearch: FC<GooglePlaceSearchPropTypes> = ({
//   id = null,
//   label = '',
//   error = '',
//   caption = '',
//   left = null,
//   right = null,
//   items,
//   inputValue,
//   value,
//   hasSucceeded = false,
//   isLoading = false,
//   isDisabled = false,
//   isBorderless = false,
//   onInputValueChange,
//   onChange,
//   onFocus = () => {},
//   onBlur = () => {},
//   onInvalid = () => {},
//   required = false,
//   maxLength = 50,
//   googlePlacesAPIKey = null,
//   name,
// }) => {
//   const selectedItem = value && items.find(item => item.value === value);
//   const [isFocused, setIsFocused] = useState(false);
//   const inputRef = useRef<HTMLInputElement>(null);
//   const {
//     isOpen: comboboxIsOpen,
//     getComboboxProps,
//     getInputProps,
//     getLabelProps,
//     getMenuProps,
//     highlightedIndex,
//     getItemProps,
//     setInputValue,
//   } = useCombobox({
//     id,
//     items,
//     itemToString: item => item?.label,
//     selectedItem,
//     onSelectedItemChange: ({ selectedItem: updatedSelectedItem }) => {
//       onChange(updatedSelectedItem);
//     },
//     onInputValueChange: (
//       event: UseComboboxStateChange<SelectItemType> & { name: string },
//     ) => {
//       /* eslint-disable */
//       event.name = name;
//       /* eslint-enable */
//       onInputValueChange(event);
//     },
//   });

//   const hasValue = inputValue?.length > 0;
//   const hasError = error.length > 0;
//   const hasCaption = caption.length > 0;
//   const isOpen = comboboxIsOpen && items.length > 0;

//   let statusRightIcon = null;
//   if (hasSucceeded) {
//     statusRightIcon = (
//       <span className={styles.successIconWrapper}>
//         <CheckIcon height={24} width={24} />
//       </span>
//     );
//   } else if (hasError) {
//     statusRightIcon = (
//       <span className={styles.errorIconWrapper}>
//         <AlertTriangleIcon width={24} height={24} />
//       </span>
//     );
//   } else if (isLoading) {
//     statusRightIcon = (
//       <span className={styles.fetchingIconWrapper}>
//         <AnimatedLoader />
//       </span>
//     );
//   }

//   const debouncedInput = useDebouncedState(inputValue, 500);

//   /* eslint-disable react-hooks/exhaustive-deps */
//   useEffect(() => {
//     setInputValue(debouncedInput);
//   }, [debouncedInput]);
//   /* eslint-enable react-hooks/exhaustive-deps */

//   return (
//     <>
//       {googlePlacesAPIKey && (
//         <GooglePlacesScript googlePlacesAPIKey={googlePlacesAPIKey} />
//       )}
//       <div
//         className={classNames(
//           styles.combobox,
//           isDisabled && styles.disabled,
//           isFocused && styles.focused,
//           isBorderless && styles.borderless,
//           isOpen && styles.active,
//           hasValue && styles.filled,
//           hasError && styles.errored,
//           hasCaption && styles.captioned,
//         )}
//       >
//         <div className={styles.container} {...getComboboxProps()}>
//           {left && <span className={styles.leftWrapper}>{left}</span>}

//           <div className={styles.inputWrapper}>
//             {label.length > 0 && (
//               // eslint-disable-next-line jsx-a11y/label-has-associated-control
//               <label {...getLabelProps()} className={styles.label}>
//                 {label}
//               </label>
//             )}

//             <input
//               {...getInputProps({ ref: inputRef, type: 'text' })}
//               name={name || id}
//               className={classNames(
//                 styles.input,
//                 label.length > 0 && styles.hasLabel,
//                 isDisabled && styles.disabled,
//               )}
//               onFocus={() => {
//                 setIsFocused(true);
//                 onFocus();
//               }}
//               onBlur={() => {
//                 setIsFocused(false);
//                 inputRef.current.checkValidity();
//                 onBlur();
//               }}
//               onInvalid={() => {
//                 if (onInvalid) {
//                   onInvalid({
//                     id,
//                     label,
//                     value,
//                     validity: inputRef.current.validity,
//                     name,
//                   });
//                 }
//               }}
//               required={required}
//               // Setting these seems to be the only way to remove the autofill dropdown on chrome for address fields
//               autoComplete={id}
//               disabled={isDisabled}
//               maxLength={maxLength}
//             />
//           </div>

//           {(right || hasError || hasSucceeded || isLoading) && (
//             <span className={styles.rightWrapper}>
//               {right}
//               {statusRightIcon}
//             </span>
//           )}
//         </div>

//         <div className={styles.menuWrapper}>
//           <SelectInputMenu
//             isOpen={isOpen}
//             getMenuProps={getMenuProps}
//             isPoweredByGoogle
//             className={styles.menuSelectInput}
//           >
//             {items.map((item, index) => (
//               <SelectInputMenuItem
//                 // eslint-disable-next-line react/no-array-index-key
//                 key={`${item.value}-${index}`} // Add index to make the key unique
//                 isHighlighted={highlightedIndex === index}
//                 secondaryLabel={item.secondaryLabel}
//                 {...getItemProps({ item, index })}
//               >
//                 {item.label}
//               </SelectInputMenuItem>
//             ))}
//           </SelectInputMenu>
//         </div>

//         {(hasError || hasCaption) && (
//           <div className={styles.footer}>
//             {hasError && <span className={styles.error}>{error}</span>}
//             {hasCaption && <span className={styles.caption}>{caption}</span>}
//           </div>
//         )}
//       </div>
//     </>
//   );
// };

// export default GooglePlaceSearch;
