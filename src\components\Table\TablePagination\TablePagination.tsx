// /* eslint-disable @typescript-eslint/ban-ts-comment */
// /* eslint-disable @typescript-eslint/no-explicit-any */
// import React, {
//   forwardRef,
//   Ref,
//   useCallback,
//   useEffect,
//   useMemo,
//   useState,
// } from 'react';
// import {
//   ChevronLeftIcon,
//   ChevronRightIcon,
//   ChevronsLeftIcon,
//   ChevronsRightIcon,
// } from '@peddleon/ped-ux-react-icons';
// import { Column, RowModel } from '@tanstack/react-table';
// import Button from '@components/Button';
// import Select from '@components/Select';
// import { MIN_PAGE_SIZE } from '@components/Table/Table';
// import Typography from '@components/Typography';
// import usePagination from '@hooks/usePagination';
// import classNames from '@utils/classNames';
// import { formatNumberWithCommas } from '@utils/common';
// import { formatCurrency, parseDollars } from '@utils/currency';

// import PaginationRangeButtons from './PaginationRangeButtons';
// import styles from './TablePagination.module.css';

// interface TablePaginationProps {
//   isInverted: boolean;
//   table: {
//     getRowCount: () => number;
//     getState: () => {
//       pagination: {
//         pageSize: number;
//         pageIndex: number;
//       };
//     };
//     setPageSize: (size: number) => void;
//     getCanPreviousPage: () => boolean;
//     getCanNextPage: () => boolean;
//     firstPage: () => void;
//     lastPage: () => void;
//     setPageIndex: (index: number) => void;
//     previousPage: () => void;
//     nextPage: () => void;
//     getRowModel?: () => RowModel<any>;
//     getAllColumns?: () => Column<any>[];
//   };
//   selectedRows?: any[];
// }

// const intialStatistics = {
//   statisticLabel: 'Statistics',
//   totalCount: 0,
//   grandTotal: undefined,
//   median: undefined,
//   average: undefined,
//   sum: undefined,
//   count: 0,
// };

// const TablePagination = forwardRef<HTMLDivElement, TablePaginationProps>(
//   ({ isInverted, table, selectedRows }, ref: Ref<HTMLDivElement>) => {
//     const [isLoading, setIsLoading] = useState(false);

//     const pagination = usePagination({
//       totalCount: table.getRowCount(),
//       pageSize: table.getState().pagination.pageSize,
//       currentPage: table.getState().pagination.pageIndex + 1,
//     });

//     const [statistics, setStatistics] = useState(intialStatistics);

//     const statisticsColumn = useMemo(
//       () =>
//         table
//           .getAllColumns()
//           // @ts-ignore
//           .find(column => column?.columnDef?.isUsedForStatistics),
//       // eslint-disable-next-line react-hooks/exhaustive-deps
//       [table.getAllColumns()],
//     );

//     const calculateMedian = useCallback((rows, accessorKey) => {
//       const sortedValues =
//         rows.map(row => parseDollars(row[accessorKey])).sort((a, b) => a - b) ||
//         [];
//       const middleIndex = Math.floor(sortedValues.length / 2);
//       if (sortedValues.length % 2 === 0) {
//         return (sortedValues[middleIndex - 1] + sortedValues[middleIndex]) / 2;
//       }
//       return sortedValues[middleIndex];
//     }, []);

//     const btnTheme = isInverted ? 'greyInverted' : 'transparent';
//     const primaryBtnTheme = isInverted ? 'darkInverted' : 'dark';

//     const startOfPageRange =
//       table.getState().pagination.pageIndex *
//         table.getState().pagination.pageSize +
//       1;

//     const endOfPageRange =
//       startOfPageRange + table.getState().pagination.pageSize - 1 >
//       table.getRowCount()
//         ? table.getRowCount()
//         : startOfPageRange + table.getState().pagination.pageSize - 1;

//     // hide table if records are less than 20
//     const isHidden = table.getRowCount() < MIN_PAGE_SIZE && !statisticsColumn;

//     const handlePreviousPage = useCallback(() => {
//       setIsLoading(true);

//       setTimeout(() => {
//         setIsLoading(false);
//         table.previousPage();
//       }, 100);
//       // eslint-disable-next-line react-hooks/exhaustive-deps
//     }, [table, table.getState().pagination.pageIndex]);

//     const handleNextPage = useCallback(
//       () => {
//         setIsLoading(true);

//         setTimeout(() => {
//           setIsLoading(false);
//           table.nextPage();
//         }, 100);
//       },
//       // eslint-disable-next-line react-hooks/exhaustive-deps
//       [table, table.getState().pagination.pageIndex],
//     );

//     const paginationTheme = item =>
//       table.getState().pagination.pageIndex + 1 === item
//         ? primaryBtnTheme
//         : btnTheme;

//     const paginationBtn = item =>
//       isLoading ? 'transparent' : paginationTheme(item);

//     const [selectedStastics, setSelectedStastics] = useState(undefined);

//     let statisticItems = [];

//     if (selectedRows?.length > 0) {
//       statisticItems = [
//         {
//           label: `Sum: ${statistics.sum}`,
//           value: 'sum',
//         },
//         {
//           label: `Average: ${statistics.average}`,
//           value: 'average',
//         },
//         {
//           label: `Median: ${statistics.median}`,
//           value: 'median',
//         },
//         {
//           label: `Count: ${statistics.count}`,
//           value: 'count',
//         },
//       ];
//     } else {
//       if (statistics.grandTotal) {
//         statisticItems = [
//           ...statisticItems,
//           {
//             label: `Grand total: ${statistics.grandTotal}`,
//             value: 'grandTotal',
//           },
//         ];
//       }
//       if (statistics.totalCount > 0) {
//         statisticItems = [
//           ...statisticItems,
//           {
//             label: `Total count: ${statistics.totalCount}`,
//             value: 'totalCount',
//           },
//         ];
//       }
//     }

//     useEffect(() => {
//       const tempSelected = ['grandTotal', 'totalCount'].includes(
//         selectedStastics,
//       );
//       if (selectedRows?.length > 0 && tempSelected) {
//         setSelectedStastics('sum');
//       } else if (selectedRows?.length === 0 && !tempSelected) {
//         setSelectedStastics(
//           // @ts-ignore
//           statisticsColumn?.columnDef?.grandTotal ? 'grandTotal' : 'totalCount',
//         );
//       }
//       // eslint-disable-next-line react-hooks/exhaustive-deps
//     }, [selectedRows?.length, selectedStastics, statisticsColumn]);

//     useEffect(() => {
//       if (statisticsColumn) {
//         let tempStatistics;
//         if (selectedRows?.length > 0) {
//           const count =
//             selectedRows?.length > 0 ? selectedRows?.length : undefined;
//           const median =
//             selectedRows?.length > 0
//               ? calculateMedian(
//                   selectedRows,
//                   // @ts-ignore
//                   statisticsColumn?.columnDef?.accessorKey,
//                 )
//               : undefined;
//           const sum = selectedRows.reduce(
//             (acc, row) =>
//               acc +
//               parseDollars(
//                 // @ts-ignore
//                 row[statisticsColumn?.columnDef?.accessorKey],
//               ),
//             0,
//           );
//           const average = sum / count;
//           tempStatistics = {
//             median: median ? formatCurrency(median) : 0,
//             average: average ? formatCurrency(average) : 0,
//             sum: sum ? formatCurrency(sum) : 0,
//             count,
//           };
//         } else {
//           tempStatistics = {
//             // @ts-ignore
//             grandTotal: statisticsColumn?.columnDef?.grandTotal,
//             totalCount: table.getRowCount(),
//           };
//         }

//         const statisticsLabel =
//           typeof statisticsColumn?.columnDef?.header === 'function'
//             ? // @ts-ignore
//               statisticsColumn?.columnDef?.header()
//             : statisticsColumn?.columnDef?.header ?? 'Statistics';
//         setStatistics(prev => ({
//           ...prev,
//           ...tempStatistics,

//           statisticLabel: statisticsLabel,
//         }));
//       }
//       // eslint-disable-next-line react-hooks/exhaustive-deps
//     }, [calculateMedian, selectedRows, statisticsColumn, table.getRowCount()]);

//     useEffect(() => {
//       setIsLoading(false);
//       // eslint-disable-next-line react-hooks/exhaustive-deps
//     }, [table.getState().pagination.pageIndex]);

//     return (
//       <div ref={ref}>
//         <div
//           className={classNames(
//             styles.root,
//             isInverted && styles.isInverted,
//             isHidden && styles.isHidden,
//           )}
//         >
//           <div className={styles.pageRangeRoot}>
//             <div
//               className={classNames(
//                 styles.pageRangeWithSelect,
//                 styles.hideDesktop,
//               )}
//             >
//               <Typography className="d-flex " variant="bodyBaseStrong">
//                 {/* write function for page range */}
//                 {startOfPageRange} - {endOfPageRange} of {table.getRowCount()}
//               </Typography>{' '}
//             </div>
//             <div className={styles.paginationDropdown}>
//               <div className={styles.pageSize}>
//                 <Select
//                   mobileModalPosition="bottom"
//                   label="Page size"
//                   value={table.getState().pagination.pageSize.toString()}
//                   items={[
//                     {
//                       label: '20',
//                       value: '20',
//                     },
//                     {
//                       label: '50',
//                       value: '50',
//                     },
//                     {
//                       label: '100',
//                       value: '100',
//                     },
//                     {
//                       label: '500',
//                       value: '500',
//                     },
//                   ]}
//                   onChange={value => {
//                     table.setPageSize(Number(value));
//                     table.firstPage();
//                   }}
//                   isFixedMenu
//                 />
//               </div>

//               {statisticsColumn && statistics.totalCount > 0 && (
//                 <div className={styles.pageStatistics}>
//                   <Select
//                     mobileModalPosition="bottom"
//                     isFixedMenu
//                     label={statistics.statisticLabel}
//                     value={selectedStastics || statisticItems[0]?.value}
//                     items={statisticItems}
//                     onChange={value => {
//                       setSelectedStastics(value);
//                     }}
//                   />
//                 </div>
//               )}
//             </div>
//           </div>
//           <div className={styles.paginationRoot}>
//             <div className={styles.pageRangeWithSelect}>
//               <Typography className="d-flex " variant="bodyBaseStrong">
//                 {/* write function for page range */}
//                 {startOfPageRange} - {endOfPageRange} of{' '}
//                 {formatNumberWithCommas(table.getRowCount())}
//               </Typography>{' '}
//             </div>
//             <Button
//               size="small"
//               theme={btnTheme}
//               onClick={() => {
//                 table.firstPage();
//               }}
//               className={styles.paginationButton}
//               isDisabled={!table.getCanPreviousPage()}
//             >
//               <ChevronsLeftIcon height={24} width={24} />
//             </Button>
//             <Button
//               size="small"
//               theme={btnTheme}
//               onClick={handlePreviousPage}
//               className={styles.paginationButton}
//               isDisabled={!table.getCanPreviousPage()}
//             >
//               <ChevronLeftIcon height={24} width={24} />
//             </Button>

//             <PaginationRangeButtons
//               pagination={pagination}
//               table={table}
//               paginationBtn={paginationBtn}
//             />
//             <Button
//               size="small"
//               theme={btnTheme}
//               onClick={handleNextPage}
//               isDisabled={!table.getCanNextPage()}
//               className={styles.paginationButton}
//             >
//               <ChevronRightIcon height={24} width={24} />
//             </Button>
//             <Button
//               size="small"
//               theme={btnTheme}
//               onClick={() => {
//                 table.lastPage();
//               }}
//               isDisabled={!table.getCanNextPage()}
//               className={styles.paginationButton}
//             >
//               <ChevronsRightIcon height={24} width={24} />
//             </Button>
//           </div>
//           <div className={styles.mobilePageRange}>
//             <div>
//               <Typography className="d-flex " variant="bodyBaseStrong">
//                 {/* write function for page range */}
//                 {startOfPageRange} - {endOfPageRange} of {table.getRowCount()}
//               </Typography>{' '}
//             </div>
//           </div>
//         </div>
//       </div>
//     );
//   },
// );

// export default TablePagination;
