import PhoneInput from '@components/PhoneInput';
import SelectInput from '@components/Select/Select';
import { TextInputProps } from '@components/TextInput/TextInput.types';
import ZIPInput from '@components/ZIPInput';

import {
  Checkbox,
  GooglePlaceSearch,
  Radio,
  RadioGroup,
  Select,
  TextInput,
} from '..';
import { useFormContext } from './Form';

type FormFieldProps =
  | ({
      type?:
        | 'checkbox'
        | 'select'
        | 'radio'
        | 'radioGroup'
        | 'input'
        | 'selectSearch'
        | 'phoneInput'
        | 'googlePlaceSearch'
        | 'zipInput';
    } & Record<string, unknown>)
  | TextInputProps;

const FormField = ({ type, ...rest }: FormFieldProps) => {
  const { register } = useFormContext();

  const registeredProps = register({ type, ...rest });

  switch (type) {
    case 'checkbox':
      return <Checkbox {...registeredProps} />;

    case 'radioGroup':
      return <RadioGroup {...registeredProps} />;

    case 'selectSearch':
      return <SelectInput {...registeredProps} />;

    case 'select':
      return <Select {...registeredProps} />;

    case 'radio':
      return <Radio {...registeredProps} />;

    case 'phoneInput':
      return <PhoneInput {...registeredProps} />;

    case 'googlePlaceSearch':
      return <GooglePlaceSearch {...registeredProps} />;

    case 'zipInput':
      return <ZIPInput {...registeredProps} />;

    default:
      return <TextInput {...registeredProps} />;
  }
};

export default FormField;
