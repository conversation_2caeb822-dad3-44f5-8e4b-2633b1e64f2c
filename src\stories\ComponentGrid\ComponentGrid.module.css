@value variables: "../../styles/variables.module.css";
@value smallWidth, mediumWidth from variables;

.grid {
  display: grid;
  /* autoprefixer: off */
  grid-template-rows: auto;
}

.columns1 {
  grid-template-columns: repeat(1, 1fr);
  /* autoprefixer: off */
  grid-gap: var(--fiveSpace);
}

.columns2 {
  grid-template-columns: repeat(2, 1fr);
  /* autoprefixer: off */
  grid-gap: var(--fiveSpace);

  @media (max-width: mediumWidth) {
    grid-template-columns: repeat(1, 1fr);
    /* autoprefixer: off */
    grid-gap: var(--twoSpace);
  }
}

.columns3 {
  grid-template-columns: repeat(3, 1fr);
  /* autoprefixer: off */
  grid-gap: var(--fiveSpace);

  @media (max-width: mediumWidth) {
    grid-template-columns: repeat(2, 1fr);
    /* autoprefixer: off */
    grid-gap: var(--twoSpace);
  }

  @media (max-width: smallWidth) {
    grid-template-columns: repeat(1, 1fr);
    /* autoprefixer: off */
    grid-gap: var(--fiveSpace);
  }
}

.columns4 {
  grid-template-columns: repeat(4, 1fr);
  /* autoprefixer: off */
  grid-gap: var(--fiveSpace);

  @media (max-width: mediumWidth) {
    grid-template-columns: repeat(2, 1fr);
    /* autoprefixer: off */
    grid-gap: var(--twoSpace);
  }

  @media (max-width: smallWidth) {
    grid-template-columns: repeat(1, 1fr);
    /* autoprefixer: off */
    grid-gap: var(--fiveSpace);
  }
}

.columns8 {
  grid-template-columns: repeat(8, 1fr);
  /* autoprefixer: off */
  grid-gap: var(--fiveSpace);

  @media (max-width: largeWidth) {
    grid-template-columns: repeat(6, 1fr);
    /* autoprefixer: off */
    grid-gap: var(--twoSpace);
  }

  @media (max-width: mediumWidth) {
    grid-template-columns: repeat(4, 1fr);
    /* autoprefixer: off */
    grid-gap: var(--twoSpace);
  }

  @media (max-width: smallWidth) {
    grid-template-columns: repeat(1, 1fr);
    /* autoprefixer: off */
    grid-gap: var(--fiveSpace);
  }
}
