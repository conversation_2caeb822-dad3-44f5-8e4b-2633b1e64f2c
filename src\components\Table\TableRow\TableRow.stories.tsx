// import { ArrowDownIcon, CheckIcon } from '@peddleon/ped-ux-react-icons';
import { Meta } from '@storybook/react';

import TableText from '../TableText';
import TextRow from './TextRow';

/**
 * TextRow component renders a row in a table or similar layout, supporting loading state and shimmers.
 * It allows customization of row rendering and provides various styling options.
 *
 * ## Usage
 *
 * To use the TextRow component in your React application, import it from the appropriate directory and render it with the desired props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { TextRow } from '@peddleon/ped-ux-react-library';
 * ```
 */

export default {
  title: 'Components/Table/TextRow',
  component: TextRow,
  tags: ['autodocs'],
  argTypes: {
    isRowLoading: { control: 'boolean' },
  },
} as Meta;

const rowDefaultProps = {
  row: {
    id: '1',
    getVisibleCells: () => [
      {
        id: 'test-1',
        flexRender: () => (
          <TableText
            indicatorTheme="warning"
            label="Assigned"
            onClick={() => {}}
            variant="statusIndicator"
          />
        ),
        width: '100px',
      },
      {
        id: 'test-2',
        flexRender: () => (
          <TableText
            label="copy clipboard"
            onClick={() => {}}
            variant="copyClipboard"
          />
        ),
        width: '100px',
      },
      {
        id: 'test-3',
        flexRender: () => (
          <TableText
            label="Sensitive Data"
            onClick={() => {}}
            variant="masked"
          />
        ),
        width: '100px',
      },
      {
        id: 'test-4',
        flexRender: () => (
          <TableText
            icons={
              [
                // {
                //   icon: <ArrowDownIcon height="20px" width="20px" />,
                //   id: 'arrowDown',
                // },
                // {
                //   icon: <CheckIcon stroke="green" height="20px" width="20px" />,
                //   id: 'eyeIcon',
                // },
              ]
            }
            onClick={() => {}}
            variant="icon"
          />
        ),
        width: '100px',
      },
    ],
  },
  column: [
    {
      Header: 'Make',
      accessor: 'make',
      minWidth: 200,
      shimmer: {
        minWidth: 30,
        maxWidth: 100,
      },
    },
    {
      Header: 'Model',
      accessor: 'model',
      width: 400,
      minWidth: 200,
      shimmer: {
        count: 2,
        width: '75%',
      },
    },
    {
      Header: 'Trim',
      accessor: 'trim',
      minWidth: 200,
      shimmer: {
        minWidth: 90,
        maxWidth: 180,
      },
    },
    {
      Header: 'Year',
      accessor: 'year',
      minWidth: 200,
      shimmer: {
        minWidth: 30,
        maxWidth: 30,
      },
      style: {
        width: 100,
      },
    },
    {
      Header: 'VIN',
      accessor: 'vin',
      minWidth: 200,
      shimmer: {
        minWidth: 145,
        maxWidth: 145,
      },
    },
    {
      Header: 'Location',
      accessor: 'location',
      minWidth: 200,
      shimmer: {
        minWidth: 50,
        maxWidth: 130,
      },
    },
  ],
};

const Template = args => (
  <div
    style={{
      margin: '16px',
    }}
  >
    <TextRow {...args} />
  </div>
);

/**
 * Default TableText story with regular styling.
 */
export const Default = Template.bind({});

Default.args = {
  ...rowDefaultProps,
};

/**
 * Default TableText story with regular styling.
 */
export const WithBG = Template.bind({});

WithBG.args = {
  ...rowDefaultProps,
  isSecondary: true,
  isRowLoading: false,
};

export const WithRowRenderer = Template.bind({});

WithRowRenderer.args = {
  ...rowDefaultProps,
  rowRender: component => component,
};

export const WithLoader = Template.bind({});

WithLoader.args = {
  ...rowDefaultProps,
  isRowLoading: true,
};
