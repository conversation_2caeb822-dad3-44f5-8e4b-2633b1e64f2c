import { FC } from 'react';
import classNames from '@utils/classNames';
import themeStyles from '@styles/themes.module.css';

import styles from './Divider.module.css';
import { DividerProps } from './Divider.types';

const Divider: FC<DividerProps> = ({ theme = 'grey', className }) => (
  <hr
    className={classNames(
      themeStyles[`${theme}Theme`],
      styles.divider,
      className,
    )}
  />
);

export default Divider;
