import type { FC, ReactNode } from 'react';
import React, { forwardRef } from 'react';
import LinkComponent from '@components/NavigationBar/LinkComponent';
import classNames from '@utils/classNames';

import styles from './MenuItem.module.css';

interface SelectInputMenuItemProps {
  /**  props.children - The content of the menu item. */
  children: ReactNode;
  /**  Whether the menu item is highlighted. */
  isHighlighted?: boolean;
  /**  Content to be displayed on the left side of the menu item. */
  left?: ReactNode;
  /** A secondary label to be displayed. */
  secondaryLabel?: string;
  /** tab index */
  tabIndex?: number;
  /** Handle click event */
  onClick?: (event: React.MouseEvent<HTMLElement>) => void;
  /** extraStyles : classnames for extra styles */
  extraStyles?: {
    root?: string;
    menuListStyles?: string;
  };
  /**  Content to be displayed on the right side of the menu item. */
  right?: ReactNode;
}

// IntrinsicAttributes & SelectInputMenuItemProps'
const SelectInputMenuItem: FC<SelectInputMenuItemProps> = forwardRef<
  HTMLLIElement,
  SelectInputMenuItemProps
>(
  (
    {
      children,
      isHighlighted = false,
      left = null,
      right = null,
      secondaryLabel = '',
      extraStyles = { root: '', menuListStyles: '' },
      ...otherProps
    },
    ref,
  ) => (
    <LinkComponent
      {...otherProps}
      ref={ref}
      className={classNames(
        styles.item,
        isHighlighted && styles.itemHighlighted,
        left && styles.hasLeftItem,
        secondaryLabel && styles.hasSecondaryLabel,
        extraStyles.root,
      )}
    >
      <li
        className={classNames(styles.menuListItem, extraStyles.menuListStyles)}
      >
        {left}
        {children}
        {secondaryLabel && <span>{secondaryLabel}</span>}
        {right}
      </li>
    </LinkComponent>
  ),
);

export default SelectInputMenuItem;
