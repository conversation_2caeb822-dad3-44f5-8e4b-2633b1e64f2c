import React from 'react';
import { fireEvent,render } from '@testing-library/react';

import NavItemMobile from './NavItemMobile';

describe('NavItemMobile', () => {
  test('renders navigation item with label', () => {
    const label = 'Home';
    const { getByText } = render(<NavItemMobile label={label} />);
    const navigationItem = getByText(label);
    expect(navigationItem).toBeInTheDocument();
  });

  test('renders active class when isActive is true', () => {
    const label = 'Home';
    const { getByText } = render(
      <NavItemMobile label={label} isActive />,
    );
    const navigationItem = getByText(label);
    expect(navigationItem).toBeInTheDocument();
  });

  test('calls onClick when navigation item is clicked', () => {
    const onClickMock = jest.fn();
    const label = 'Home';
    const { getByText } = render(
      <NavItemMobile label={label} onClick={onClickMock} />,
    );
    const navigationItem = getByText(label);
    fireEvent.click(navigationItem);
    expect(onClickMock).toHaveBeenCalled();
  });

  test('calls onLeftIconClick when left icon is clicked', () => {
    const onLeftIconClickMock = jest.fn();
    const leftIcon = <div>Left Icon</div>;
    const { getByText } = render(
      <NavItemMobile
        label="Home"
        leftIcon={leftIcon}
        onLeftIconClick={onLeftIconClickMock}
      />,
    );
    const leftIconElement = getByText('Left Icon');
    fireEvent.click(leftIconElement);
    expect(onLeftIconClickMock).toHaveBeenCalled();
  });

  test('renders link when link prop is provided', () => {
    const label = 'Home';
    const link = '/';
    const { getByText } = render(<NavItemMobile label={label} link={link} />);
    const navigationLink = getByText(label);
    expect(navigationLink).toHaveAttribute('href', link);
  });
});
