@value variables: "../../styles/variables.module.css";
@value smallWidth, black10, primary from variables;
@value typography: "../../styles/typography.module.css";
@value buttonLabelSmall from typography;

.list {
  list-style: none;
  height: 100%;
  overflow: auto;

  @media (max-width: smallWidth) {
    width: 100%;
  }
}

.picker {
  display: flex;
  flex-flow: column;
  justify-content: space-between;
  height: 100%;
}

.headerWrapper {
  flex: 0 0 auto;
}

.container {
  position: relative;
  flex: 1 1 auto;
  overflow: hidden;

  @media (max-width: smallWidth) {
    width: 100%;
  }
}

.rowWrapper {
  transition: border-color 0.5s ease-out-expo;

  &:not(:last-of-type) {
    border-bottom: solid 1px black10;
  }

  @media (max-width: smallWidth) {
    &:not(.active) {
      display: none;
    }
    &.active {
      border-bottom: solid 1px transparent;
    }
  }
}

.emptyStateFooterLink {
  composes: buttonLabelSmall;
  padding: 0;
  color: primary;
  &:hover {
    color: primary;
  }
}
