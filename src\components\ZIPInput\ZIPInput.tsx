import React from 'react';
import TextInput from '@components/TextInput';
import { TextInputProps } from '@components/TextInput/TextInput.types';

import { ZIPInputPropTypes } from './ZIPInput.types';

const ZIPInput = ({
  id,
  name = 'zipCode',
  value,
  onChange,
  onInvalid,
  onFocus = () => {},
  onBlur = () => {},
  label = 'Zipcode',
  left,
  isFetching,
  isBorderless = false,
  isSquare = false,
  className = null,
  hasSucceeded,
  error,
  shouldHideErrorMessage = false,
  required = false,
  caption,
  isDisabled = false,
  inputMode = 'numeric',
  maxLength = 5,
}: ZIPInputPropTypes) => {
  const handleChange: TextInputProps['onChange'] = event => {
    const formattedValue = (
      event.target as HTMLInputElement | HTMLTextAreaElement
    ).value.replace(/\D/g, '');

    onChange(formattedValue);
  };

  return (
    <TextInput
      id={id}
      maxLength={maxLength}
      inputMode={inputMode}
      pattern={`^\\d{5,${maxLength}}$`}
      label={label}
      name={name || id}
      value={value || ''}
      onChange={handleChange}
      onInvalid={onInvalid}
      isBorderless={isBorderless}
      isSquare={isSquare}
      left={left}
      className={className}
      isLoading={isFetching}
      hasSucceeded={hasSucceeded}
      error={error}
      shouldHideErrorMessage={shouldHideErrorMessage}
      required={required}
      caption={caption}
      onFocus={onFocus}
      onBlur={onBlur}
      isDisabled={isDisabled}
    />
  );
};

export default ZIPInput;
