import { FC } from 'react';
import StatusIndicator from '@components/StatusIndicator';
import Tag from '@components/Tag';

import { Typography } from '..';
import styles from './VehicleTile.module.css';
import { VehicleTilePropTypes } from './VehicleTile.types';

const VehicleTile: FC<VehicleTilePropTypes> = ({
  vehicle,
  status,
  location,
  offerIDDisplay,
}) => {
  const cityState = [location?.city, location?.stateCode]
    .filter(Boolean)
    .join(', ');

  const { id: statusId, label: statusLabel, theme: statusTheme } = status;

  return (
    <div className={styles.tile}>
      {status && (
        <div className={styles.statusTag}>
          {statusId !== -1 && (
            <Tag
              withHorizontalPadding={false}
              theme="transparent"
              type="label"
              label={statusLabel}
              left={<StatusIndicator theme={statusTheme} />}
              labelClassName={styles.statusLabel}
              className={styles.tag}
            />
          )}
        </div>
      )}
      <Typography variant="h6Strong" className={styles.year}>
        {vehicle.year?.label}
      </Typography>
      <Typography variant="h4Strong" className={styles.makeModel}>
        {vehicle.make?.label}
        &nbsp;
        {vehicle.model?.label}
      </Typography>
      <div
        className={
          vehicle?.VIN ? styles.detailsWithVIN : styles.detailsWithoutVIN
        }
      >
        {vehicle?.trim?.label && (
          <Typography className={styles.metaData}>
            {vehicle.trim.label}
          </Typography>
        )}
        {cityState && <p className={styles.metaData}>{cityState}</p>}
        {vehicle?.VIN && (
          <Typography className={styles.metaData}>{vehicle.VIN}</Typography>
        )}
      </div>
      <div className={styles.offerIdBtnWrapper}>{offerIDDisplay}</div>
    </div>
  );
};

export default VehicleTile;
