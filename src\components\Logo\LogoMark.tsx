interface LogoMarkProps {
  color?: string;
  width?: number | string;
  height?: number | string;
  isInverted?: boolean;
}

const LogoMark = ({
  color = 'currentColor',
  width = '40',
  height = '100%',
  isInverted = false,
}: LogoMarkProps) => {
  if (isInverted) {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 14 15"
        width={width}
        height={height}
      >
        <path
          d="M9.703.08C6.7-.43 3.538 1.57 2.431 4.557a.036.036 0 0 1-.064 0c-.32-.557-.524-1.014-.778-1.54a.14.14 0 0 0-.183-.06.14.14 0 0 0-.054.045A9.743 9.743 0 0 0 .034 5.175a.423.423 0 0 0 0 .33c.442.993.974 1.943 1.588 2.839a.26.26 0 0 1 .04.183l-1.005 6.11a.239.239 0 0 0 .202.285 7.082 7.082 0 0 0 2.091 0 .287.287 0 0 0 .237-.238l1.167-6.786c.593-3.64 2.452-5.56 4.607-5.183 1.793.329 2.419 2.062 1.873 3.829-.486 1.566-1.802 2.633-3.27 2.633a3.247 3.247 0 0 1-1.946-.669.147.147 0 0 0-.237.081 30.708 30.708 0 0 0-.57 2.636.148.148 0 0 0 .081.16 5 5 0 0 0 1.37.425c3.261.477 6.493-1.028 7.489-4.857.948-3.23-.899-6.34-4.048-6.874Z"
          fill={color}
        />
      </svg>
    );
  }
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 200 200"
      width={width}
      height={height}
      fill={color}
    >
      <g>
        <path
          fillRule="evenodd"
          d="M74.861 176.803c42.338 7.693 82.42-21.88 96.128-65.299 13.708-43.59-4.164-84.444-46.329-92.308-42.338-7.692-82.94 21.197-96.648 64.787-13.708 43.419 4.685 85.128 46.85 92.82zm-1.175-95.8c6.669-17.652 25.54-29.514 43.561-26.549 18.871 3.248 29.939 21.607 24.263 40.955-5.959 22.736-25.398 31.633-44.838 28.809-3.121-.565-5.533-1.271-8.23-2.542-.283-.283-.425-.565-.425-.989.284-2.118 1.419-8.05 3.405-15.675.284-.565.852-.848 1.42-.424 2.695 1.836 6.526 3.954 11.634 3.954 8.798 0 16.744-6.355 19.582-15.675 3.263-10.45-.426-20.76-11.21-22.737-12.912-2.26-23.98 9.18-27.669 30.786l-6.953 40.248a1.818 1.818 0 01-1.418 1.412 46.204 46.204 0 01-12.487 0 1.465 1.465 0 01-1.135-1.694l5.96-36.294c.141-.283 0-.706-.426-1.271-4.967-7.061-8.372-14.263-9.507-16.805-.284-.565-.284-1.272 0-1.978 1.986-4.377 4.54-8.614 7.946-12.85.425-.424 1.135-.424 1.419.14.394.786.771 1.545 1.143 2.295 1.1 2.215 2.161 4.353 3.54 6.885.141.141.425.141.425 0z"
          fill={color}
        />
      </g>
    </svg>
  );
};

export default LogoMark;
