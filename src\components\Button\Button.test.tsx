import { fireEvent, render } from '@testing-library/react';
import { ANIMATED_LOADER, BUTTON_TEST_ID } from '@constants/dataTestId';

import Button from './Button';

describe('Button component', () => {
  it('renders without crashing', () => {
    render(<Button />);
  });

  it('renders label when provided', () => {
    const { getByText } = render(<Button label="Submit" />);
    expect(getByText('Submit')).toBeInTheDocument();
  });

  it('should render with different component', () => {
    const { getByTestId } = render(
      <Button id="test" label="Submit" component="a" />,
    );
    expect(getByTestId(`test-${BUTTON_TEST_ID}`)).toBeInTheDocument();
  });

  it('calls onClick handler when clicked', () => {
    const onClickMock = jest.fn();
    const { getByText } = render(
      <Button label="Click me" onClick={onClickMock} />,
    );
    fireEvent.click(getByText('Click me'));
    expect(onClickMock).toHaveBeenCalledTimes(1);
  });

  it('should render the icon', () => {
    const { getByText } = render(
      <Button>
        <span>icon</span>
      </Button>,
    );

    expect(getByText('icon')).toBeInTheDocument();
  });

  it('should have the loading svg', () => {
    const { getByTestId } = render(<Button isLoading label="test" />);

    expect(getByTestId(ANIMATED_LOADER)).toBeInTheDocument();
  });

  it('renders label when with all the things', () => {
    const { getByText } = render(
      <Button
        left={<div>left</div>}
        right={<div>right</div>}
        isFullWidth
        isSquare
        label="Submit"
      />,
    );
    expect(getByText('left')).toBeInTheDocument();
    expect(getByText('right')).toBeInTheDocument();
  });

  it('onMouseDown is called when button is pressed', () => {
    const onClickMock = jest.fn();
    const { getByText } = render(
      <Button label="Click me" onMouseDown={onClickMock} />,
    );
    fireEvent.mouseDown(getByText('Click me'));
    expect(onClickMock).toHaveBeenCalledTimes(1);
  });

  test('onMouseUp is called when button is released', () => {
    const onMouseUpMock = jest.fn();
    const { getByText } = render(
      <Button label="Click me" onMouseUp={onMouseUpMock} dataId="test" />,
    );
    fireEvent.mouseUp(getByText('Click me'));
    expect(onMouseUpMock).toHaveBeenCalledTimes(1);
  });

  test('onTouchStart is called when button is touched', () => {
    const onTouchStartMock = jest.fn();
    const { getByText } = render(
      <Button label="Click me" onTouchStart={onTouchStartMock} dataId="test" />,
    );
    fireEvent.touchStart(getByText('Click me'));
    expect(onTouchStartMock).toHaveBeenCalledTimes(1);
  });

  test('onTouchEnd is called when touch ends on button', () => {
    const onTouchEndMock = jest.fn();
    const { getByText } = render(
      <Button label="Click me" onTouchEnd={onTouchEndMock} dataId="test" />,
    );
    fireEvent.touchEnd(getByText('Click me'));
    expect(onTouchEndMock).toHaveBeenCalledTimes(1);
  });

  // 59, 90-114, 120-124
});
