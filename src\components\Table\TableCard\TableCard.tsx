// import React, { useEffect, useRef, useState } from 'react';
// import { MoreHorizontalIcon } from '@peddleon/ped-ux-react-icons';
// import Checkbox from '@components/Checkbox';
// import Dropdown from '@components/Dropdown';
// import Shimmer from '@components/Shimmer';
// import classNames from '@utils/classNames';
// import variableStyles from '@styles/variables.module.css';

// import styles from './TableCard.module.css';
// import { TableCardPropTypes } from './TableCard.types';

// const Card: React.FC<TableCardPropTypes> = ({
//   id,
//   cardHeaderLeft = null,
//   bodyTitle,
//   bodySubTitle,
//   footerLeftContent,
//   footerRightContent,
//   actionItems,
//   onMenuItemClick = () => {},
//   cardHeaderRight = null,
//   cardBodyRight = null,
//   onClick = () => {},
//   extraStyles = {},
//   isLoading = false,
//   rowSelection = {},
//   index,
//   handleRowSelection,
//   hasRowSelection,
// }) => {
//   const cardRef = useRef(null);
//   const [cardHeight, setCardHeight] = useState(0);
//   const handleMoreMenuClick = event => {
//     event.preventDefault();
//     event.stopPropagation();
//   };

//   const handleMenuItemClick = (_event, dropdownId) => {
//     onMenuItemClick(dropdownId, id);
//   };

//   useEffect(() => {
//     if (isLoading) return;
//     const cardRefHeight = cardRef?.current?.clientHeight;
//     setCardHeight(cardRefHeight);
//   }, [isLoading]);

//   if (isLoading) {
//     return (
//       <div
//         style={{
//           height: cardHeight || 'auto',
//         }}
//         className="overflow-hidden mt-2"
//       >
//         <div className="d-flex">
//           <Shimmer className="" width="80%" height={20} />
//           <Shimmer width="20%" className="ml-2" height={20} />
//         </div>
//         <div className="m-2">
//           <Shimmer width="100%" height={50} />
//         </div>
//         <div className="d-flex">
//           <Shimmer width="30%" height={20} />
//           <Shimmer width="70%" className="ml-2" height={20} />
//         </div>
//       </div>
//     );
//   }

//   return (
//     // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
//     <div className={styles.card} onClick={onClick} ref={cardRef}>
//       {/* CARD HEADER */}
//       <div className={classNames(styles.cardHeader, extraStyles.cardHeader)}>
//         <div
//           className={classNames(hasRowSelection && 'd-flex align-items-center')}
//         >
//           {hasRowSelection && (
//             <div className={classNames(styles.checkbox, 'mr-2')}>
//               <Checkbox
//                 id={`tableCardCheckbox_${id}`}
//                 isChecked={rowSelection[index]}
//                 onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
//                   handleRowSelection(
//                     index,
//                     (event.target as HTMLInputElement).checked,
//                     event,
//                   );
//                 }}
//               />
//             </div>
//           )}
//           {cardHeaderLeft}
//         </div>
//         <div className={classNames(extraStyles?.cardHeaderRight)}>
//           {cardHeaderRight}
//         </div>

//         {!!actionItems?.lists.length && (
//           <button
//             onClick={handleMoreMenuClick}
//             type="button"
//             aria-label="more"
//             className={styles.moreOptions}
//           >
//             <Dropdown
//               id={`tableCardDropDown_${id}`}
//               item={actionItems}
//               onClick={handleMenuItemClick}
//               openOnClick
//               place="left"
//             >
//               <div id={`tableCardDropDown_${id}`} className={styles.moreIcon}>
//                 <MoreHorizontalIcon
//                   color={variableStyles.neutralDarkest}
//                   height={20}
//                   width={20}
//                 />
//               </div>
//             </Dropdown>
//           </button>
//         )}
//       </div>

//       {/* CARD BODY */}
//       <div className={classNames(styles.cardBody, extraStyles.cardBody)}>
//         <div
//           className={classNames(
//             styles.cardContent,
//             !cardBodyRight && styles.cardBodyFullContent,
//             extraStyles.cardContent,
//           )}
//         >
//           {bodyTitle && (
//             <div
//               className={classNames(
//                 styles.cardBodyTitle,
//                 extraStyles.cardBodyTitle,
//               )}
//             >
//               {bodyTitle}
//             </div>
//           )}
//           <div
//             className={classNames(
//               styles.cardBodySubTitle,
//               extraStyles.cardBodySubTitle,
//             )}
//           >
//             {bodySubTitle}
//           </div>
//         </div>
//         <div>{cardBodyRight}</div>
//       </div>

//       {/* CARD FOOTER */}
//       {(footerLeftContent || footerRightContent) && (
//         <div className={classNames(styles.cardFooter, extraStyles.cardFooter)}>
//           <div
//             className={classNames(
//               styles.cardFooterLeftContent,
//               extraStyles.cardFooterLeftContent,
//             )}
//           >
//             {footerLeftContent}
//           </div>
//           <div
//             className={classNames(
//               styles.cardFooterRightContent,
//               extraStyles.cardFooterRightContent,
//             )}
//           >
//             {footerRightContent}
//           </div>
//         </div>
//       )}
//     </div>
//   );
// };

// export default Card;
