import React from 'react';

interface LinkComponentProps {
  component?: React.ElementType;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any; // Accept any other prop
}

const LinkComponent: React.FC<LinkComponentProps> = ({
  // eslint-disable-next-line @typescript-eslint/naming-convention
  component: Component = 'a',
  ...props
}) => <Component href={props.link} {...props} />;

export default LinkComponent;
