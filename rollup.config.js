/* eslint-disable import/no-extraneous-dependencies */
import { resolve } from 'path';
import copy from 'rollup-plugin-copy';
import postcss from 'rollup-plugin-postcss';
import swc from 'rollup-plugin-swc';
import alias from '@rollup/plugin-alias';
import { nodeResolve } from '@rollup/plugin-node-resolve';
import typescript from '@rollup/plugin-typescript';

const input = 'src/index.ts';

// TODO - generate from package.json
const external = [
  'react',
  'react-dom',
  'react/jsx-runtime',
  'compute-scroll-into-view',
  'downshift',
  'prop-types',
  '@swc/helpers',
  '@storybook/theming',
  '@storybook/components',
  'telejson',
  '@storybook/channels',
  '@storybook/preview-api',
  '@storybook/docs-tools',
  '@storybook/blocks',
  'rc-tooltip',
  '@peddleon/ped-ux-react-icons',
  'qr-code-styling',
  'react-day-picker',
];

function getPlugins() {
  return [
    /* this plugin is currently here entirely to resolve jsx files on import
     * (i.e. it enables `import { Circle } from './Circle'` over `import { Circle } from './Circle.jsx')
     * this is super weird since this is a plugin for node_modules but apparently that's just the way it is in rollup-land
     * see https://github.com/rollup/rollup-plugin-babel/issues/44, https://github.com/rollup/rollup/issues/1052
     */
    nodeResolve({
      extensions: ['.mjs', '.js', '.json', '.node', '.jsx', '.ts', '.tsx'],
      preferBuiltins: true,
    }),
    alias({
      entries: [
        {
          find: '@components',
          replacement: resolve(__dirname, 'src/components'),
        },
        {
          find: '@libtypes',
          replacement: resolve(__dirname, 'src/types'),
        },
        {
          find: '@hooks',
          replacement: resolve(__dirname, 'src/hooks'),
        },
        {
          find: '@constants',
          replacement: resolve(__dirname, 'src/constants'),
        },
        {
          find: '@utils',
          replacement: resolve(__dirname, 'src/utils'),
        },
        {
          find: '@styles',
          replacement: resolve(__dirname, 'src/styles'),
        },
        {
          find: '@stories',
          replacement: resolve(__dirname, 'src/stories'),
        },
        {
          find: '@contexts',
          replacement: resolve(__dirname, 'src/contexts'),
        },
      ],
    }),
    swc({
      rollup: {
        exclude: [
          '**/*.css',
          '**/node_modules/**',
          '**/tests/**',
          '**/*.stories.tsx',
        ],
      },
      jsc: {
        externalHelpers: false,
        parser: {
          syntax: 'typescript',
          tsx: true,
          decorators: true,
          dynamicImport: true,
        },
        target: 'es2021',
        transform: {
          react: {
            runtime: 'automatic',
            importSource: 'react',
          },
        },
      },
      module: {
        type: 'es6',
      },
      minify: false,
    }),
    typescript({
      declarationDir: 'dist/types',
    }),
    postcss({
      modules: {
        generateScopedName: '[name]__[local]___[hash:base64:5]',
      },
      extract: true,
    }),
    copy({
      targets: [{ src: 'src/styles/', dest: 'dist/' }],
    }),
  ];
}

// we're splitting this into two jobs to resolve an issue with the babel runtime
// where the CJS output tries to use the ESM version (see plugins above). this
// makes the build take twice as long. we should try to find a solution that's
// more performant
export default [
  {
    input,
    output: [
      {
        dir: 'dist',
        format: 'esm',
        preserveModules: true,
        preserveModulesRoot: 'src',
      },
    ],
    external,
    plugins: getPlugins(),
  },
  {
    input,
    output: [
      {
        format: 'cjs',
        file: 'dist/index.cjs',
      },
    ],
    external,
    plugins: getPlugins(),
  },
];
