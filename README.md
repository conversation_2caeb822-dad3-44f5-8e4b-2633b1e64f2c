# ped-ux-react-library

React component library
storybook URL: https://peddle-ux-react-library-storybook.onrender.com/?path=/docs/welcome--docs

## Library Setup

Please note the node version required to run this application. Use
[nvm](https://github.com/nvm-sh/nvm) to use the right node version (20.11.1) by simply
running `nvm use` within this directory prior to running the below.

### Installl dependencies

```bash
npm install
```

## Eslint Configuration

### Environment Configuration

- **browser:** Enables browser global variables and browser-specific rules.
- **es2021:** Enables ES2021 syntax.

### Extends Configuration

- **eslint:recommended:** Core ESLint rules enforcing best practices.
- **plugin:@typescript-eslint/recommended:** Recommended rules from the @typescript-eslint/eslint-plugin.
- **plugin:react/recommended:** Recommended rules for React.
- **plugin:import/errors:** Rules for reporting import/export syntax errors.
- **plugin:import/warnings:** Rules for reporting import/export syntax warnings.
- **plugin:jsx-a11y/recommended:** Recommended rules for accessibility support in JSX.
- **plugin:react-hooks/recommended:** Recommended rules for React Hooks.
- **airbnb:** Extends Airbnb's JavaScript style guide.
- **prettier:** Integrates Prettier into ESLint.
- **plugin:storybook/recommended:** Recommended Storybook ESLint rules.

### Parser Configuration

- **@typescript-eslint/parser:** TypeScript parser for ESLint.

### Parser Options Configuration

- **ecmaVersion: "latest":** Sets ECMAScript version to the latest supported by ESLint.
- **sourceType: "module":** Sets source type to module for ES module syntax.

### Plugins Configuration

- **@typescript-eslint:** Integrates ESLint with TypeScript.
- **react:** Integrates ESLint with React.

### Rules Configuration

- **react/prefer-stateless-function:** Enforces using stateless functional components where possible.
- **react/jsx-filename-extension:** Restricts file extensions for `ts` files only.
- **react/jsx-pascal-case:** Enforces PascalCase for user-defined `ts` components.
- **react/jsx-max-depth:** Enforces a maximum depth for `ts` elements.
- **react/jsx-no-useless-fragment:** Disallows unnecessary fragments in JSX.
- **react/no-typos:** Prevents common typos in React component names.
- **react/prop-types:** Disables prop types validation.
- **react/no-array-index-key:** Enforces using a unique key when rendering arrays in JSX.
- **@typescript-eslint/naming-convention:** Enforces consistent naming conventions for variables, parameters, properties, and types in TypeScript code.
  - **_variable:_** CamelCase
  - **_component:_** PascalCase
  - **_parameters:_** CamelCase
  - **_properties:_** CamelCase
  - **_types:_** PascalCase
- **filename-rules/match:** Enforces a naming convention for filenames in `PascalCase` with `ts` entension.
- **sort-imports:** Enforces sorted import declarations.
- **react/function-component-definition:** Enforces the use of arrow function syntax for defining React components.
