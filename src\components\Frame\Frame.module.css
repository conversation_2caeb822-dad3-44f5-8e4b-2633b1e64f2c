@value variables: "../../styles/variables.module.css";
@value black, warning, neutralLighter, oneSpace, twoSpace, threeSpace, fiveSpace, largeWidth, topZIndex, ease-out-expo, fiveSpace from variables;
@value typography: "../../styles/typography.module.css";
@value overline from typography;

@value mobileContainerHeight: 48px;
@value mobileContainerCollapsedOffset: -43px;
@value borderWidth: 4px;

.frame {
  border-radius: 0.8rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: topZIndex;
  pointer-events: none;
  opacity: 1;

  &.zeroRadiusBorder {
    border-radius: 0;
  }
}

.showBorder {
  border: borderWidth solid warning;
}

.adminFrameLabel {
  padding: 0 fiveSpace;
}

.containerMobile {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: mobileContainerHeight;
  background: warning;
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  grid-template-areas: 'y label x';
  align-items: center;
  color: black;
  pointer-events: all;
  transition: all 1s ease-out-expo;
  border-bottom: solid neutralLighter 1px;

  @media (min-width: largeWidth) {
    display: none;
  }
}

.collapsed {
  & .containerMobile {
    transform: translateY(mobileContainerCollapsedOffset);
  }
}

.offset {
  & .containerDesktop {
    left: 70%;
  }
}

.label {
  grid-area: label;
  composes: overline;
  text-transform: uppercase;

  @media (min-width: largeWidth) {
    display: none;
  }
}

.tag {
  pointer-events: all !important;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border: solid neutralLighter 1px;
  border-top-style: none;
}

.rightButton {
  cursor: pointer;
  justify-self: end;
  margin-right: threeSpace;
  grid-area: x;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (min-width: largeWidth) {
    margin-right: 0;
    margin-left: 0;
  }
}

.leftButton {
  cursor: pointer;
  justify-self: start;
  margin-left: threeSpace;
  grid-area: y;
  display: flex;
  align-items: center;
  justify-content: center;

  @media (min-width: largeWidth) {
    margin-right: 0;
    margin-left: 0;
  }
}

.containerDesktop {
  display: none;
  @media (min-width: largeWidth) {
    display: flex;
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
  }
}
