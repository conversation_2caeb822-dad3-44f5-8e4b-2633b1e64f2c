@value variables: "./variables.module.css";
@value twoSpace, fourSpace, smallWidth, mediumWidth, largeWidth, black10 from variables;

.flexGapTwoSpace {
  margin: calc(-1 * var(--twoSpace, 16px)) 0 0 calc(-1 * var(--twoSpace, 16px));
  width: calc(100% + var(--twoSpace, 16px));

  & > * {
    padding: var(--twoSpace, 16px) 0 0 var(--twoSpace, 16px);
  }
}

.flexGapFourSpace {
  margin: calc(-1 * var(--fourSpace, 32px)) 0 0
    calc(-1 * var(--fourSpace, 32px));
  width: calc(100% + var(--fourSpace, 32px));

  & > * {
    padding: var(--fourSpace, 32px) 0 0 var(--fourSpace, 32px);
  }
}

.responsivePageGutters {
  padding-right: var(--tenSpace, 80px);
  padding-left: var(--tenSpace, 80px);

  @media (max-width: largeWidth) {
    padding-right: var(--fiveSpace, 40px);
    padding-left: var(--fiveSpace, 40px);
  }

  @media (max-width: smallWidth) {
    padding-right: var(--fourSpace, 32px);
    padding-left: var(--fourSpace, 32px);
  }
}

/* assumed to be used an inner container, where the
 * container already has a horizontal padding of twoSpace
 * */
.responsiveAdditivePageGutters {
  padding-right: var(--eightSpace, 64px);
  padding-left: var(--eightSpace, 64px);

  @media (max-width: largeWidth) {
    padding-right: var(--threeSpace, 24px);
    padding-left: var(--threeSpace, 24px);
  }

  @media (max-width: smallWidth) {
    padding-right: 0;
    padding-left: 0;
  }
}

/* Used by ApplicationLayout and Container
 * 
 * */
.responsiveContainer {
  composes: responsivePageGutters;
  flex: 1 0 auto;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  background-color: inherit;
  @media (max-width: smallWidth) {
    padding-left: 0;
    padding-right: 0;
  }
}

.responsiveContainerWide {
  composes: responsiveContainer;
  padding-left: var(--twoSpace, 16px);
  padding-right: var(--twoSpace, 16px);
  @media (max-width: smallWidth) {
    padding-left: 0;
    padding-right: 0;
  }
}

.responsiveContainerFull {
  composes: responsiveContainer;
  padding-left: 0;
  padding-right: 0;
}

.responsiveContainerNarrow {
  composes: responsiveContainer;
  max-width: 1144px;
}

/* Hides an element, but retains screenreader access
 * */
.visuallyHidden:not(:focus):not(:active) {
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

.visuallyHiddenContent {
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

.tileBorderRadius {
  border-radius: 20px;
  @media (max-width: mediumWidth) {
    border-radius: 16px;
  }
}

.tileBorder {
  composes: tileBorderRadius;
  box-shadow: 0px 0px 0px 1px var(--black10, rgba(33, 33, 33, 0.1));
}
