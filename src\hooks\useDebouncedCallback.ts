/* eslint-disable react-hooks/exhaustive-deps */
import { useMemo, useRef } from 'react';

// https://github.com/react-hookz/web/blob/master/src/useDebouncedCallback/useDebouncedCallback.ts
export default function useDebouncedCallback(
  callback,
  delay = 200,
  dependencies = []
) {
  const timeout = useRef<ReturnType<typeof setTimeout>>();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const callbackArgs = useRef<any>(null);

  const clear = () => {
    if (timeout.current) {
      clearTimeout(timeout.current);
      timeout.current = undefined;
    }
  };

  return useMemo(
    () => {
      const execute = () => {
        if (!callbackArgs.current) return;

        const context = callbackArgs.current;
        callbackArgs.current = undefined;

        callback(...context);

        clear();
      };

      // eslint-disable-next-line func-names
      const wrapped = function (...args) {
        clear();

        callbackArgs.current = args;
        timeout.current = setTimeout(execute, delay);
      };

      Object.defineProperties(wrapped, {
        length: { value: callback.length },
        name: { value: `${callback.name || 'anonymous'}__debounced__${delay}` },
      });

      return wrapped;
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [delay, ...dependencies]
  );
}
