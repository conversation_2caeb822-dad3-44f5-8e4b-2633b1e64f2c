import { ReactNode } from 'react';
import { StatusIndicatorTheme } from '@components/Button/Button.types';

export interface VehicleTilePropTypes {
  /**
   *  Information about the vehicle, including its year, make, model, trim, and VIN (optional).
   */
  vehicle: {
    year: {
      label: string;
      id?: string;
    };
    make: {
      label: string;
      id?: string;
    };
    model: {
      label: string;
      id?: string;
    };
    trim: {
      label: string;
      id?: string;
    };
    VIN?: string;
  };
  /**
   * The status of the vehicle, including an ID, label, value, and theme.
   */
  status: {
    id: number;
    label: string;
    value: string;
    theme: StatusIndicatorTheme;
  };
  /**
   * The location of the vehicle, including the city and state code.
   */
  location: {
    city: string;
    stateCode: string;
  };
  /**
   * Optional display for the offer ID associated with the vehicle.
   */
  offerIDDisplay?: ReactNode;
}
