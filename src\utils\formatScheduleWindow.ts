/* eslint-disable import/prefer-default-export */
import { formatDate } from '@utils/date';
// This function is for adding any empty state date to scheduling window come from API
function formatScheduleWindows(items, comparedItems) {
  const results = [];
  let fromAPIIndex = 0;
  let constructedIndex = 0;
  while (constructedIndex < comparedItems.length) {
    /* We use two trackers to track both arrays, and both start at index 0.
         -- If we find comparedItems array of current constructedIndex value has same date value as array from API current fromAPIIndex date value,
            Then we move both pointer one step and push fromAPIIndex value to results array.
         -- If we not find it, then we need to push empty state data to results array, and ONLY move constructedIndex pointer one step 
      */
    if (
      formatDate(comparedItems[constructedIndex]) ===
      formatDate(items[fromAPIIndex]?.date)
    ) {
      results.push(items[fromAPIIndex]);
      fromAPIIndex += 1;
      constructedIndex += 1;
    } else {
      results.push({
        date: comparedItems[constructedIndex],
        items: [],
        isDisabled: false,
      });
      constructedIndex += 1;
    }
  }
  return results;
}

export default formatScheduleWindows;
