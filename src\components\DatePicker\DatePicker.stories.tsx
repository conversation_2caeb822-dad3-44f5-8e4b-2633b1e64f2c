/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from 'react';
import { Meta } from '@storybook/react';
import { SelectItemType } from '@libtypes/common';
import { getDateRange } from '@utils/date';

import { SelectSearch } from '..';
import DatePicker from './DatePicker';
import { DatePickerPropTypes, DateType } from './DatePicker.types';

/**
 * The DatePicker component enables users to select a single date, multiple dates, or a range of dates.
 *
 * ## Overview
 *
 * The `DatePicker` provides a flexible and user-friendly way to handle date selection in your application.
 * It supports various modes, such as single date selection, range selection, and multiple date selection,
 * making it versatile for a wide range of use cases.
 *
 * ## Usage
 *
 * To use the `DatePicker` in your React application, import it from the component library and include it in your JSX.
 *
 * ### Import the component into your React application:
 *
 * ```jsx
 * import { DatePicker } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * ### Basic Usage
 *
 * ```jsx
 * <DatePicker pickerType="single" />
 * ```
 *
 * ### Range Selection
 *
 * ```jsx
 * <DatePicker
 *   pickerType="range"
 *   setTo="this week"
 *   onDateChange={(date) => console.log('Selected range:', date)}
 * />
 * ```
 *
 * ### Multiple Dates Selection
 *
 * ```jsx
 * <DatePicker
 *   pickerType="multiple"
 *   onDateChange={(dates) => console.log('Selected dates:', dates)}
 * />
 * ```
 *
 * ## Component Details
 *
 * The `DatePicker` internally uses `react-day-picker` to handle calendar rendering and interactions.
 * It also supports custom day components, such as `DayButton`, allowing further customization.
 *
 * ### Modes
 * - **Single**: Allows the selection of a single date.
 * - **Range**: Allows the selection of a date range (`from` and `to`).
 * - **Multiple**: Allows the selection of multiple dates.
 *
 * ## Props
 *
 * | Prop           | Type                                    | Required | Default    | Description                                                                                          |
 * |----------------|-----------------------------------------|----------|------------|------------------------------------------------------------------------------------------------------|
 * | `pickerType`   | `'single' | 'range' | 'multiple'`      | Yes      | `'single'` | Defines the mode of the date picker.                                                                |
 * | `defaultValue` | `DateType<T>`                          | No       | `undefined`| Initial value for the date or date range.                                                           |
 * | `onDateChange` | `(date: DateType<T>) => void`          | No       | `undefined`| Callback function triggered when the selected date(s) change.                                       |
 * | `disabledDates`| `Date[] | { before: Date; after: Date }`| No       | `undefined`| Dates that should be disabled in the picker.                                                        |
 * | `setTo`        | `SetToOptions<T>`                      | No       | `undefined`| Predefined range or date to initialize the picker (e.g., `'today'`, `'this week'`).                 |
 * | `timeZone`     | `string`                               | No       | `system timezone` | The time zone for the date calculations.                                                            |
 * | `isRequired`   | `boolean`                              | No       | `false`    | Whether the date selection is mandatory.                                                            |
 *
 * ## Notes
 *
 * - **Custom Components**: You can customize the `DayButton`, `NextMonthButton`, and `PreviousMonthButton` components using the `components` prop from `react-day-picker`.
 * - **Formatting Dates**: The `toUTC` function ensures all dates are normalized to UTC to prevent time zone discrepancies.
 * - **Date Validation**: Use the `disabledDates` prop to define rules for disabling specific dates or ranges.
 *
 * ## Integration
 *
 * The `DatePicker` can be integrated with form libraries such as `Formik` or `React Hook Form` for enhanced form handling. Make sure to wrap it in a controlled component for seamless interaction.
 *
 * ## Accessibility
 *
 * The `DatePicker` supports keyboard navigation and screen reader instructions, ensuring accessibility for users.
 */

const meta: Meta<typeof DatePicker> = {
  title: 'Components/DatePicker',
  tags: ['autodocs'],
  component: DatePicker,
};

export const DatePickerRange = (args: DatePickerPropTypes<'range'>) => {
  const [preRangeSelection, setPreRangeSelection] = useState<
    SelectItemType | undefined
  >(undefined);
  const [selectedDate, setSelectedDate] = useState<
    DateType<'range'> | undefined
  >();

  useEffect(() => {
    if (!preRangeSelection) return;
    const range = getDateRange(preRangeSelection?.value);
    setSelectedDate(range);
  }, [preRangeSelection]);

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        maxWidth: '320px',
        gap: '20px',
      }}
    >
      {args?.pickerType === 'range' && (
        <SelectSearch
          value={preRangeSelection}
          inputMode="text"
          noMatchFoundText="No matches"
          label="Select date range"
          onChange={(item: SelectItemType) => setPreRangeSelection(item)}
          isFixedMenu
          caption=" "
          isMenuAlignedRight={false}
          maxLength={50}
          placeholder="Placeholder"
          items={[
            {
              label: 'Year',
              options: [
                {
                  label: 'This year',
                  displayLabel: 'This year',
                  value: 'thisYear',
                },
                {
                  label: 'Last year',
                  displayLabel: 'Last year',
                  value: 'lastYear',
                },
              ],
            },
            {
              label: 'Month',
              options: [
                {
                  label: 'Last month',
                  displayLabel: 'Last month',
                  value: 'lastMonth',
                },
                {
                  label: 'This month',
                  displayLabel: 'This month',
                  value: 'thisMonth',
                },
                {
                  label: 'Last 3 month',
                  displayLabel: 'Last 3 month',
                  value: 'last3Month',
                },
                {
                  label: 'Last 6 month',
                  displayLabel: 'Last 6 month',
                  value: 'last6Month',
                },
                {
                  label: 'Last 12 month',
                  displayLabel: 'Last 12 month',
                  value: 'last12Month',
                },
              ],
            },
            {
              label: 'Week',
              options: [
                {
                  label: 'Last week',
                  displayLabel: 'Last week',
                  value: 'lastWeek',
                },
                {
                  label: 'This week',
                  displayLabel: 'This week',
                  value: 'thisWeek',
                },
              ],
            },
            {
              label: 'Day',
              options: [
                {
                  label: 'Yesterday',
                  displayLabel: 'Yesterday',
                  value: 'yesterday',
                },
                {
                  label: 'Today',
                  displayLabel: 'Today',
                  value: 'today',
                },
                {
                  label: 'Last 7 days',
                  displayLabel: 'Last 7 days',
                  value: 'last7Days',
                },
                {
                  label: 'Last 30 days',
                  displayLabel: 'Last 30 days',
                  value: 'last30Days',
                },
              ],
            },
          ]}
        />
      )}
      <DatePicker
        {...args}
        value={selectedDate}
        onDateChange={date => {
          if (args?.pickerType === 'range' && 'from' in date && 'to' in date) {
            setSelectedDate(date);
          } else {
            console.error('Invalid date type for range picker');
          }
        }}
        onDayClick={() => setPreRangeSelection(undefined)}
      />
    </div>
  );
};

DatePickerRange.args = {
  id: 'default-datepicker',
  pickerType: 'range',
};

DatePickerRange.argTypes = {
  id: { control: 'text' },
  pickerType: { control: 'select', options: ['single', 'range', 'multiple'] },
  setTo: { control: 'text' },
};

export const DefaultRangeDatePicker = (args: DatePickerPropTypes<'range'>) => (
  <DatePicker
    {...args}
    pickerType="single"
    onDateChange={date => console.log('Selected date range: ', date)}
  />
);

DefaultRangeDatePicker.args = {
  id: 'range-datepicker',
};

export const MultipleDatePicker = (args: DatePickerPropTypes<'multiple'>) => (
  <DatePicker
    {...args}
    pickerType="multiple"
    onDateChange={date => console.log('Selected dates: ', date)}
  />
);

MultipleDatePicker.args = {
  id: 'multiple-datepicker',
};

export default meta;
