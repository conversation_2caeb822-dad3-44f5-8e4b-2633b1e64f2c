import Alert from '@components/Alert';
import ModalPortal from '@components/DialogModal/ModalPortal';
import { useToastContext } from '@contexts/ToastContext';
import { ToastConfigType } from '@libtypes/common';
import { TOASTER_PORTAL_ID } from '@constants/components';
import classNames from '@utils/classNames';

import styles from './NotificationModal.module.css';

const NotificationModal = () => {
  const {
    setToastConfig,
    toastConfig: { body, isActive, theme, key },
    isCancellable,
    position,
  } = useToastContext();

  const onCloseHandler = () => {
    setToastConfig((prev: ToastConfigType) => ({ ...prev, isActive: false }));
  };

  if (!isActive) {
    return null;
  }

  return (
    <ModalPortal portalId={TOASTER_PORTAL_ID}>
      <div className={classNames(styles.notificationModal, styles[position])}>
        <div
          className={classNames(
            styles.notificationWrapper,
            !isCancellable && isActive && styles.defaultToastStyle,
          )}
          id="modal-sucess"
        >
          <Alert
            theme={theme}
            body={body}
            isCancellable={isCancellable}
            onCancelButtonClick={onCloseHandler}
            isAnimated={isActive}
            key={key}
          />
        </div>
      </div>
    </ModalPortal>
  );
};

export default NotificationModal;
