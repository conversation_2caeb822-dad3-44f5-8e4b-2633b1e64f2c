import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useMemo,
  useRef,
  useState,
} from 'react';
import { RegisterReturnBase } from '@hooks/useForm';

interface KeyValueState {
  [key: string]: string;
}

interface FormContextProps {
  formData?: { [key: string]: unknown };
  updateFormData?: (key: string, value: unknown) => void;
  formState?: KeyValueState[];
  errors?: KeyValueState;
  // TODO: refactor the types
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  register?: any;
  children?: ReactNode;
}

interface FormProps {
  children?: ReactNode;
  formState?: KeyValueState[];
  errors?: KeyValueState;
  register: RegisterReturnBase;
  id?: string;
  autoComplete?: 'on' | 'off';
}

const FormContext = createContext<FormContextProps | null>({
  errors: {},
  formData: {},
});

export function useFormContext() {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error('useFormContext must be used within a FormContextProvider');
  }
  return context;
}

const Form: React.FC<FormProps> = ({
  children,
  formState,
  errors,
  register,
  id = 'form-id',
  autoComplete = 'on',
}) => {
  const [formData, setFormData] = useState<{ [key: string]: unknown }>({});
  const formRef = useRef<HTMLFormElement>(null);

  const updateFormData = (key: string, value: unknown) => {
    setFormData(prevFormData => ({
      ...prevFormData,
      [key]: value,
    }));
  };

  const memoizedValue = useMemo(
    () => ({ formData, updateFormData, formState, errors, register }),
    [errors, formData, formState, register],
  );

  if (!register) {
    throw new Error(
      'Form must have the register props, as it will be responsible for managing state',
    );
  }

  const onSubmit = useCallback(e => {
    if (formRef.current) {
      formRef.current.dispatchEvent(
        new Event('submit', { cancelable: true, bubbles: true }),
      );
    }
    e?.preventDefault();
  }, []);

  return (
    <FormContext.Provider value={memoizedValue}>
      <form
        id={id}
        data-id={id}
        noValidate
        onSubmit={onSubmit}
        autoComplete={autoComplete}
        ref={formRef}
      >
        {children}
      </form>
    </FormContext.Provider>
  );
};

export default Form;
