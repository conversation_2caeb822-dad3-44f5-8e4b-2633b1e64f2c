import { useMemo } from 'react';

export const range = (start, end) => {
  const length = end - start + 1;
  return Array.from({ length }, (_, idx) => idx + start);
};

export default ({ totalCount, pageSize, currentPage }) => {
  const paginationRange = useMemo(() => {
    const totalPageCount = Math.ceil(totalCount / pageSize);

    // If there are less than or equal to 3 total pages, return all pages
    if (totalPageCount <= 3) {
      return range(1, totalPageCount);
    }

    // Case 1: If we're on the first page, show [1, 2, 3]
    if (currentPage === 1) {
      return range(1, 3);
    }

    // Case 2: If we're on the last page, show [totalPageCount - 2, totalPageCount - 1, totalPageCount]
    if (currentPage === totalPageCount) {
      return range(totalPageCount - 2, totalPageCount);
    }

    // Case 3: For middle pages, show [currentPage - 1, currentPage, currentPage + 1]
    return range(
      Math.max(currentPage - 1, 1),
      Math.min(currentPage + 1, totalPageCount),
    );
  }, [totalCount, pageSize, currentPage]);

  return paginationRange;
};
