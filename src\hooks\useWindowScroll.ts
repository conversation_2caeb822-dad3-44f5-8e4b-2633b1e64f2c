import {
  MutableRefObject,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import useWindowSize from '@hooks/useWindowSize';
import throttle from '@utils/throttle';

export const SCROLL_DIRECTION_DOWN = 'down';
export const SCROLL_DIRECTION_UP = 'up';

// dupe of useIsScrolling, because element that can have an event listener is
// different from the element that has a scrollTop
function useWindowScroll() {
  const [isScrolling, setIsScrolling] = useState(false);
  const [scrollDirection, setScrollDirection] = useState(SCROLL_DIRECTION_DOWN);
  const [windowScrollTop, setWindowScrollTop] = useState(0);
  const [windowScrollBottom, setWindowScrollBottom] = useState(0);
  const previousScrollTop = useRef(0);
  const timeoutRef: MutableRefObject<number> = useRef();

  const { width: windowWidth, height: windowHeight } = useWindowSize();

  const getScrollBottom = () => {
    const { scrollTop, scrollHeight, clientHeight } = document.scrollingElement;

    return scrollHeight - (scrollTop + clientHeight);
  };

  const handleScroll = throttle(() => {
    if (!isScrolling) {
      setIsScrolling(true);
    }

    window.clearTimeout(timeoutRef.current);

    const setState = () => {
      const { scrollTop } = document.scrollingElement;

      // We don't want to change anything if the values are equal
      if (scrollTop === previousScrollTop.current) return;

      if (scrollTop > previousScrollTop.current) {
        setScrollDirection(SCROLL_DIRECTION_DOWN);
      } else setScrollDirection(SCROLL_DIRECTION_UP);

      previousScrollTop.current = scrollTop;
      setWindowScrollTop(scrollTop);
      setWindowScrollBottom(getScrollBottom());
    };
    setState();

    timeoutRef.current = setTimeout(() => {
      setIsScrolling(false);

      // set state one last time after we're done tracking the scroll to ensure
      // we captured the "settled" scroll state
      requestAnimationFrame(setState);
    }, 166) as unknown as number;
  }, 100);

  useEffect(() => {
    document.addEventListener('scroll', handleScroll);

    return () => {
      if (document) {
        document.removeEventListener('scroll', handleScroll);
      }
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    setWindowScrollBottom(getScrollBottom());
  }, [windowWidth, windowHeight]); // eslint-disable-line react-hooks/exhaustive-deps

  const setScrollTop = useCallback(scrollTop => {
    document.scrollingElement.scrollTop = scrollTop;
  }, []);

  return {
    isScrolling,
    scrollDirection,
    windowScrollTop,
    windowScrollBottom,
    setScrollTop,
  };
}

export default useWindowScroll;
