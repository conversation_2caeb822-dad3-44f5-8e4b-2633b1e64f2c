name: Lint PR

on:
  pull_request_target:
    branches-ignore: []
    types:
      - opened
      - edited
      - synchronize

jobs:
  lint-pr:
    runs-on: ubuntu-latest
    steps:
      - uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            fix
            feat
            refactor
            chore
            build
            ci
            docs
            perf
            style
            revert
            test
