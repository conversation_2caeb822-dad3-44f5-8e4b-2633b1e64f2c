import {
  Controls,
  Description,
  Primary,
  Stories,
  Subtitle,
  Title,
} from '@storybook/blocks';
import '@styles/base.css';
import '@styles/typography.css';
import 'pure-react-carousel/dist/react-carousel.es.css';
import useDocumentFontLoad from '@hooks/useDocumentFontLoad';
import FluidRoot from '@components/FluidRoot';
import {
  TOASTER_PORTAL_ID,
  GENERAL_MODAL_PORTAL_ID,
} from '@constants/components';

/** @type { import('@storybook/react').Preview } */
const preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /(Date|At)$/,
      },
    },
    docs: {
      toc: true, // 👈 Enables the table of contents
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
          <Stories />
        </>
      ),
    },
  },
  decorators: [
    Story => {
      useDocumentFontLoad();

      return (
        <FluidRoot>
          <div id={GENERAL_MODAL_PORTAL_ID}></div>
          <div id={TOASTER_PORTAL_ID}></div>
          <Story />
        </FluidRoot>
      );
    },
  ],
};

export default preview;
