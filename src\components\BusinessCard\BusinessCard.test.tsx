import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import BusinessCard from './BusinessCard';
import { BusinessCardFormData } from './BusinessCard.types';

const mockFormData: BusinessCardFormData = {
  firstName: 'Lauren',
  lastName: '<PERSON><PERSON><PERSON>',
  title: 'Account Manager',
  organization: 'Buyer Network',
  email: '<EMAIL>',
  phoneNumber: '************',
};

describe('BusinessCard component', () => {
  it('should render all form fields', () => {
    render(<BusinessCard />);

    expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/organization/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/job title/i)).toBeInTheDocument();
  });

  it('should initialize with provided initial data', () => {
    render(<BusinessCard initialData={mockFormData} />);

    expect(screen.getByLabelText(/first name/i)).toHaveValue(
      mockFormData.firstName,
    );
    expect(screen.getByLabelText(/last name/i)).toHaveValue(
      mockFormData.lastName,
    );
    expect(screen.getByLabelText(/email/i)).toHaveValue(mockFormData.email);
    expect(screen.getByLabelText(/organization/i)).toHaveValue(
      mockFormData.organization,
    );
    expect(screen.getByLabelText(/job title/i)).toHaveValue(mockFormData.title);
  });

  it('should show validation errors for empty required fields', async () => {
    render(<BusinessCard />);

    const submitButton = screen.getByRole('button', {
      name: /download business card pdf/i,
    });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getAllByText(/this field is required/i)).toHaveLength(6);
    });
  });

  it('should show validation error for invalid email', async () => {
    render(<BusinessCard />);

    const emailInput = screen.getByLabelText(/email/i);
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });

    const submitButton = screen.getByRole('button', {
      name: /download business card pdf/i,
    });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(
        screen.getByText(/please enter a valid email address/i),
      ).toBeInTheDocument();
    });
  });

  it('should call onGenerate when form is valid and PDF is generated', async () => {
    const mockOnGenerate = jest.fn();
    render(
      <BusinessCard onGenerate={mockOnGenerate} initialData={mockFormData} />,
    );

    const submitButton = screen.getByRole('button', {
      name: /download business card pdf/i,
    });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnGenerate).toHaveBeenCalledWith(mockFormData);
    });
  });

  it('should call onError when PDF generation fails', async () => {
    const mockOnError = jest.fn();
    // Force an error by providing invalid SVG element
    const consoleSpy = jest
      .spyOn(console, 'error')
      .mockImplementation(() => {});

    render(<BusinessCard onError={mockOnError} initialData={mockFormData} />);

    const submitButton = screen.getByRole('button', {
      name: /download business card pdf/i,
    });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnError).toHaveBeenCalled();
    });

    consoleSpy.mockRestore();
  });
});
