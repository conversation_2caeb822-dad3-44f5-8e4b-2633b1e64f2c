@value variables: "../../styles/variables.module.css";

@value halfSpace, oneSpace, twoSpace, threeSpace, fourSpace, fiveSpace, sixSpace, eightSpace, tenSpace, fifteenSpace, twentyFiveSpace from variables;
@value halfSpaceFluid, oneSpace<PERSON>luid, twoSpace<PERSON><PERSON>id, threeSpace<PERSON><PERSON><PERSON>, fourSpaceFluid, fiveSpace<PERSON>luid, sixSpaceFluid, eightSpaceFluid, tenSpaceFluid, fifteenSpaceFluid, twentyFiveSpaceFluid from variables;
@value oneSpaceMin, oneSpaceMax, twoSpaceMin, twoSpaceMax, threeSpaceMin, threeSpaceMax, fourSpaceMin, fourSpaceMax, fiveSpaceMin, fiveSpaceMax, sixSpaceMin, sixSpaceMax, eightSpaceMin, eightSpaceMax, tenSpaceMin, tenSpaceMax, fifteenSpaceMin, fifteenSpaceMax, twentyFiveSpaceMin, twentyFiveSpaceMax from variables;
@value displayFontSize, h1FontSize, h2FontSize, h3FontSize, h4FontSize, h5FontSize, h6FontSize, bodyLarge<PERSON>ontSize, bodyBase<PERSON>ontSize, bodySmallFontSize, button<PERSON>abelXLargeFontSize, button<PERSON>abelLargeFontSize, buttonLabelSmallFontSize, captionFontSize, overlineFontSize from variables;
@value displayMobileFontSize, h1MobileFontSize, h2MobileFontSize, h3MobileFontSize, h4MobileFontSize, h5MobileFontSize, h6MobileFontSize from variables;
@value displayFluidFontSize, h1FluidFontSize, h2FluidFontSize, h3FluidFontSize, h4FluidFontSize, h5FluidFontSize, h6FluidFontSize, bodyLargeFluidFontSize, bodyBaseFluidFontSize, bodySmallFluidFontSize, buttonLabelXLargeFluidFontSize, buttonLabelLargeFluidFontSize, buttonLabelSmallFluidFontSize, captionFluidFontSize, overlineFluidFontSize from variables;
@value displayFluidFontSizeMin, displayFluidFontSizeMax, h1FluidFontSizeMin, h1FluidFontSizeMax, h2FluidFontSizeMin, h2FluidFontSizeMax, h3FluidFontSizeMin, h3FluidFontSizeMax, h4FluidFontSizeMin, h4FluidFontSizeMax, h5FluidFontSizeMin, h5FluidFontSizeMax, h6FluidFontSizeMin, h6FluidFontSizeMax, bodyLargeFluidFontSizeMin, bodyLargeFluidFontSizeMax, bodyBaseFluidFontSizeMin, bodyBaseFluidFontSizeMax, bodySmallFluidFontSizeMin, bodySmallFluidFontSizeMax, buttonLabelXLargeFluidFontSizeMin, buttonLabelXLargeFluidFontSizeMax, buttonLabelLargeFluidFontSizeMin, buttonLabelLargeFluidFontSizeMax, buttonLabelSmallFluidFontSizeMin, buttonLabelSmallFluidFontSizeMax, captionFluidFontSizeMin, captionFluidFontSizeMax, overlineFluidFontSizeMin, overlineFluidFontSizeMax from variables;
@value fluidFloor, fluidCeiling from variables;
@value smallWidth from variables;
@value white, black, primary, primaryFade, neutralDarkest from variables;

@value displayFluidLineHeight, h1FluidLineHeight, h2FluidLineHeight, h3FluidLineHeight, h4FluidLineHeight, h5FluidLineHeight, h6FluidLineHeight, captionFluidLineHeight from variables;

@value displayLineHeight, displayMobileLineHeight, displayHeaderLineHeight, h1LineHeight, h1MobileLineHeight, h2LineHeight, h2MobileLineHeight, h3LineHeight, h3MobileLineHeight, h4LineHeight, h4MobileLineHeight, h5LineHeight, h5MobileLineHeight, h6LineHeight, h6MobileLineHeight, captionLineHeight from variables;

.static {
  width: inherit;
  height: inherit;

  --halfSpace: halfSpace;
  --oneSpace: oneSpace;
  --twoSpace: twoSpace;
  --threeSpace: threeSpace;
  --fourSpace: fourSpace;
  --fiveSpace: fiveSpace;
  --sixSpace: sixSpace;
  --eightSpace: eightSpace;
  --tenSpace: tenSpace;
  --fifteenSpace: fifteenSpace;
  --twentyFiveSpace: twentyFiveSpace;

  --displayFontSize: displayFontSize;
  --displayMobileFontSize: displayMobileFontSize;
  --h1FontSize: h1FontSize;
  --h2FontSize: h2FontSize;
  --h3FontSize: h3FontSize;
  --h4FontSize: h4FontSize;
  --h5FontSize: h5FontSize;
  --h6FontSize: h6FontSize;
  --bodyLargeFontSize: bodyLargeFontSize;
  --bodyBaseFontSize: bodyBaseFontSize;
  --bodySmallFontSize: bodySmallFontSize;
  --buttonLabelXLargeFontSize: buttonLabelXLargeFontSize;
  --buttonLabelLargeFontSize: buttonLabelLargeFontSize;
  --buttonLabelSmallFontSize: buttonLabelSmallFontSize;
  --captionFontSize: captionFontSize;
  --overlineFontSize: overlineFontSize;

  --displayLineHeight: displayLineHeight;
  --displayMobileLineHeight: displayMobileLineHeight;
  --displayHeaderLineHeight: displayHeaderLineHeight;
  --h1LineHeight: h1LineHeight;
  --h1LineHeight: h1LineHeight;
  --h2LineHeight: h2LineHeight;
  --h3LineHeight: h3LineHeight;
  --h4LineHeight: h4LineHeight;
  --h5LineHeight: h5LineHeight;
  --h6LineHeight: h6LineHeight;
  --captionLineHeight: captionLineHeight;

  --white: white;
  --black: black;
  --primary: primary;
  --primaryFade: primaryFade;
  --neutralDarkest: neutralDarkest;

  @media (max-width: smallWidth) {
    --displayFontSize: displayMobileFontSize;
    --h1FontSize: h1MobileFontSize;
    --h2FontSize: h2MobileFontSize;
    --h3FontSize: h3MobileFontSize;
    --h4FontSize: h4MobileFontSize;
    --h5FontSize: h5MobileFontSize;
    --h6FontSize: h6MobileFontSize;

    --displayLineHeight: displayMobileLineHeight;
    --h1LineHeight: h1MobileLineHeight;
    --h2LineHeight: h2MobileLineHeight;
    --h3LineHeight: h3MobileLineHeight;
    --h4LineHeight: h4MobileLineHeight;
    --h5LineHeight: h5MobileLineHeight;
    --h6LineHeight: h6MobileLineHeight;
  }
}

.fluid {
  width: inherit;
  height: inherit;

  --halfSpace: halfSpaceFluid;
  --oneSpace: oneSpaceFluid;
  --twoSpace: twoSpaceFluid;
  --threeSpace: threeSpaceFluid;
  --fourSpace: fourSpaceFluid;
  --fiveSpace: fiveSpaceFluid;
  --sixSpace: sixSpaceFluid;
  --eightSpace: eightSpaceFluid;
  --tenSpace: tenSpaceFluid;
  --fifteenSpace: fifteenSpaceFluid;
  --twentyFiveSpace: twentyFiveSpaceFluid;

  --displayFontSize: displayFluidFontSize;
  --h1FontSize: h1FluidFontSize;
  --h2FontSize: h2FluidFontSize;
  --h3FontSize: h3FluidFontSize;
  --h4FontSize: h4FluidFontSize;
  --h5FontSize: h5FluidFontSize;
  --h6FontSize: h6FluidFontSize;
  --bodyLargeFontSize: bodyLargeFluidFontSize;
  --bodyBaseFontSize: bodyBaseFluidFontSize;
  --bodySmallFontSize: bodySmallFluidFontSize;
  --buttonLabelXLargeFontSize: buttonLabelXLargeFluidFontSize;
  --buttonLabelLargeFontSize: buttonLabelLargeFluidFontSize;
  --buttonLabelSmallFontSize: buttonLabelSmallFluidFontSize;
  --captionFontSize: captionFluidFontSize;
  --overlineFontSize: overlineFluidFontSize;

  --displayLineHeight: displayFluidLineHeight;
  --displayHeaderLineHeight: displayHeaderLineHeight;
  --h1LineHeight: h1FluidLineHeight;
  --h2LineHeight: h2FluidLineHeight;
  --h3LineHeight: h3FluidLineHeight;
  --h4LineHeight: h4FluidLineHeight;
  --h5LineHeight: h5FluidLineHeight;
  --h6LineHeight: h6FluidLineHeight;
  --captionLineHeight: captionFluidLineHeight;

  --white: white;
  --black: black;
  --primary: primary;
  --primaryFade: primaryFade;
  --neutralDarkest: neutralDarkest;

  @media (max-width: fluidFloor) {
    --oneSpace: oneSpaceMin;
    --twoSpace: twoSpaceMin;
    --threeSpace: threeSpaceMin;
    --fourSpace: fourSpaceMin;
    --fiveSpace: fiveSpaceMin;
    --sixSpace: sixSpaceMin;
    --eightSpace: eightSpaceMin;
    --tenSpace: tenSpaceMin;
    --fifteenSpace: fifteenSpaceMin;
    --twentyFiveSpace: twentyFiveSpaceMin;

    --displayFontSize: displayFluidFontSizeMin;
    --h1FontSize: h1FluidFontSizeMin;
    --h2FontSize: h2FluidFontSizeMin;
    --h3FontSize: h3FluidFontSizeMin;
    --h4FontSize: h4FluidFontSizeMin;
    --h5FontSize: h5FluidFontSizeMin;
    --h6FontSize: h6FluidFontSizeMin;
    --bodyLargeFontSize: bodyLargeFluidFontSizeMin;
    --bodyBaseFontSize: bodyBaseFluidFontSizeMin;
    --bodySmallFontSize: bodySmallFluidFontSizeMin;
    --buttonLabelXLargeFontSize: buttonLabelXLargeFluidFontSizeMin;
    --buttonLabelLargeFontSize: buttonLabelLargeFluidFontSizeMin;
    --buttonLabelSmallFontSize: buttonLabelSmallFluidFontSizeMin;
    --captionFontSize: captionFluidFontSizeMin;
    --overlineFontSize: overlineFluidFontSizeMin;
  }

  @media (min-width: fluidCeiling) {
    --oneSpace: oneSpaceMax;
    --twoSpace: twoSpaceMax;
    --threeSpace: threeSpaceMax;
    --fourSpace: fourSpaceMax;
    --fiveSpace: fiveSpaceMax;
    --sixSpace: sixSpaceMax;
    --eightSpace: eightSpaceMax;
    --tenSpace: tenSpaceMax;
    --fifteenSpace: fifteenSpaceMax;
    --twentyFiveSpace: twentyFiveSpaceMax;

    --displayFontSize: displayFluidFontSizeMax;
    --h1FontSize: h1FluidFontSizeMax;
    --h2FontSize: h2FluidFontSizeMax;
    --h3FontSize: h3FluidFontSizeMax;
    --h4FontSize: h4FluidFontSizeMax;
    --h5FontSize: h5FluidFontSizeMax;
    --h6FontSize: h6FluidFontSizeMax;
    --bodyLargeFontSize: bodyLargeFluidFontSizeMax;
    --bodyBaseFontSize: bodyBaseFluidFontSizeMax;
    --bodySmallFontSize: bodySmallFluidFontSizeMax;
    --buttonLabelXLargeFontSize: buttonLabelXLargeFluidFontSizeMax;
    --buttonLabelLargeFontSize: buttonLabelLargeFluidFontSizeMax;
    --buttonLabelSmallFontSize: buttonLabelSmallFluidFontSizeMax;
    --captionFontSize: captionFluidFontSizeMax;
    --overlineFontSize: overlineFluidFontSizeMax;
  }
}
