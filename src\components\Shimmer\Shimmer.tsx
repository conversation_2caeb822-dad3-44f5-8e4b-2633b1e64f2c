import classNames from '@utils/classNames';

import styles from './Shimmer.module.css';

interface ShimmerProps {
  width: number | string;
  height: number | string;
  className?: string;
}

const Shimmer = ({ width, height, className = null }: ShimmerProps) => (
  <div
    style={{
      width,
      height,
    }}
    className={classNames(styles.shimmer, className)}
  />
);

export default Shimmer;
