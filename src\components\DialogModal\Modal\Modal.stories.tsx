/* eslint-disable no-console */
import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta, StoryObj } from '@storybook/react';
import typographyStyles from '@styles/typography.module.css';
import variableStyles from '@styles/variables.module.css';

import Modal from './Modal';

/**
 *
 * <br/>

## Overview

The Modal component provides flexibility to set modal configuration with basic details.

## Usage

Import the component into your React application:

```jsx
import { Modal } from '@peddleon/ped-ux-react-library';```
 

 */
const meta: Meta<typeof Modal> = {
  component: Modal,
  title: 'Components/Modal',
  tags: ['autodocs'],
  parameters: {
    docs: {
      toc: true,
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
};

export const ModalStory: StoryObj<typeof Modal> = {
  render: ({
    hasCloseButton,
    hasDialogWrapperStyles,
    hasPadding,
    isShadowless,
    className,
  }) => (
    <div
      style={{
        padding: variableStyles.oneSpace,
      }}
    >
      <Modal
        isShadowless={isShadowless}
        onClose={() => console.log('On Close Action Triggered!!')}
        hasPadding={hasPadding}
        hasCloseButton={hasCloseButton}
        hasDialogWrapperStyles={hasDialogWrapperStyles}
        className={className}
      >
        <h1 className={typographyStyles.h2}>A flexible modal</h1>

        <p>
          The base treatment of a standard modal, intended to be used with
          custom content
        </p>
      </Modal>
    </div>
  ),
  argTypes: {
    hasCloseButton: {
      type: 'boolean',
    },
    hasPadding: {
      type: 'boolean',
    },
    isShadowless: {
      type: 'boolean',
    },
    hasDialogWrapperStyles: {
      type: 'boolean',
    },
    onClose: {
      type: 'function',
    },
    className: {
      type: 'string',
    },
  },
  args: {
    hasCloseButton: true,
    hasPadding: true,
    isShadowless: false,
    hasDialogWrapperStyles: false,
    onClose: () => {},
    className: '',
  },
};

export default meta;
