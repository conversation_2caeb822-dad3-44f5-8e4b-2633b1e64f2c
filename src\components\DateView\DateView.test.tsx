import React from 'react';
import { render } from '@testing-library/react';

import DateView from './DateView'; // Import the DateView

describe('DateView', () => {
  it('renders with default date format', () => {
    const { getByText } = render(<DateView date="04-10-2024" />);
    expect(getByText('Apr')).toBeInTheDocument();
    expect(getByText('10')).toBeInTheDocument();
    expect(getByText('Wed')).toBeInTheDocument();
  });

  it('renders with invalid date format', () => {
    const { queryAllByText } = render(<DateView date="invalid" />);
    expect(queryAllByText('Invalid Date').length).toBe(3);
  });
});
