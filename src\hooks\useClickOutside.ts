import { MutableRefObject, useEffect } from 'react';

function useClickOutside(
  refs: MutableRefObject<Node> | MutableRefObject<Node>[],
  handler: (event: MouseEvent) => void,
  options: { isEnabled: boolean } = { isEnabled: true }
) {
  useEffect(() => {
    const handleClick = (event: MouseEvent) => {
      const modalElement = document.getElementById('modal-sucess');

      if (modalElement) return true;
      if (Array.isArray(refs)) {
        const isContains = refs.some(ref => {
          if (ref.current && ref.current.contains(event.target as Node)) {
            return true;
          }
          return false;
        });
        if (isContains) return true;
      } else if (!refs.current || refs.current.contains(event.target as Node)) {
        return true;
      }

      return handler(event);
    };

    if (options?.isEnabled) {
      document.addEventListener('mousedown', handleClick);
      document.addEventListener('touchstart', handleClick);
    }

    return () => {
      if (options?.isEnabled) {
        document.removeEventListener('mousedown', handleClick);
        document.removeEventListener('touchstart', handleClick);
      }
    };
  }, [refs, handler, options?.isEnabled]);
}

export default useClickOutside;
