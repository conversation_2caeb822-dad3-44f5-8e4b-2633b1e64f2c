import { useEffect, useState } from 'react';
import { Meta } from '@storybook/react';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

import RadioGroup from './RadioGroup';

const getMockOptions = prefixId => [
  {
    id: `${prefixId}_this`,
    label: 'I would choose this one',
  },
  {
    id: `${prefixId}_or-this`,
    label: 'Or this one',
  },
  {
    id: `${prefixId}_maybe-this`,
    label: 'Maybe this one though',
  },
];

/**
 * The RadioGroup component creates a group of radio inputs with customizable options and layout.
 * It allows users to select only one option from the group at a time.
 *
 * ## Styles
 *
 * This component utilizes CSS modules for styling. It supports both column and row layouts,
 * along with left or right alignment options. Additionally, it can be configured to display
 * options in a button-like shape.
 *
 * ## Usage
 *
 * To use the RadioGroup component in your React application, import it from the appropriate directory
 * and include it in your JSX. You can customize its appearance and behavior using the provided props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { RadioGroup } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the RadioGroup component with your desired configuration:
 *
 * ```jsx
 * <RadioGroup
 *   options={[
 *     { id: 'option1', label: 'Option 1' },
 *     { id: 'option2', label: 'Option 2' },
 *     { id: 'option3', label: 'Option 3' },
 *   ]}
 *   checked="option1"
 *   onChange={({ checked }) => console.log(`Selected option: ${checked}`)}
 * />
 * ```
 *
 * This will render a group of radio inputs with the specified options. The `checked` prop
 * determines which option is selected by default, and the `onChange` prop allows you to handle
 * changes to the selected option.
 */

const meta: Meta<typeof RadioGroup> = {
  title: 'Components/RadioGroup',
  tags: ['autodocs'],
  component: RadioGroup,
};

export const RadioGroupStory = ({ checked, options, ...restProps }) => {
  const [isChecked, setIsChecked] = useState<string>(checked);
  useEffect(() => setIsChecked(checked), [checked]);

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <RadioGroup
        options={options}
        checked={isChecked}
        {...restProps}
        onChange={({ checked: updatedChecked }) => setIsChecked(updatedChecked)}
      />
    </div>
  );
};

RadioGroupStory.args = {
  checked: 'this',
  options: getMockOptions('story'),
  name: '',
  direction: 'column',
  alignment: 'left',
  isDisabled: false,
  isMobileInline: false,
  className: '',
  isInButtonShape: false,
};

RadioGroupStory.argTypes = {
  direction: {
    control: 'select',
    options: ['left', 'right'],
  },
  alignment: {
    control: 'select',
    options: ['column', 'row'],
  },
};

export const RadioGroupSheet = () => {
  const [checked, setChecked] = useState<string>(null);

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <div style={{ marginTop: variableStyles.fiveSpace }}>
        <ComponentTile label="Standard / Column / Left">
          <div style={{ textAlign: 'left' }}>
            <RadioGroup
              options={getMockOptions('sheet')}
              checked={checked}
              onChange={({ checked: updatedChecked }) =>
                setChecked(updatedChecked)
              }
            />
          </div>
        </ComponentTile>
      </div>

      <div style={{ marginTop: variableStyles.fiveSpace }}>
        <ComponentTile label="Standard / Column / Right">
          <RadioGroup
            options={getMockOptions('sheet')}
            checked={checked}
            onChange={({ checked: updatedChecked }) =>
              setChecked(updatedChecked)
            }
            alignment="right"
            direction="column"
          />
        </ComponentTile>
      </div>

      <div style={{ marginTop: variableStyles.fiveSpace }}>
        <ComponentTile label="Standard / Row / Left">
          <RadioGroup
            direction="row"
            alignment="left"
            options={getMockOptions('sheet')}
            checked={checked}
            onChange={({ checked: updatedChecked }) =>
              setChecked(updatedChecked)
            }
          />
        </ComponentTile>
      </div>

      <div style={{ marginTop: variableStyles.fiveSpace }}>
        <ComponentTile label="Standard / Row / Right">
          <div style={{ textAlign: 'right' }}>
            <RadioGroup
              direction="row"
              alignment="right"
              options={getMockOptions('sheet')}
              checked={checked}
              onChange={({ checked: updatedChecked }) =>
                setChecked(updatedChecked)
              }
            />
          </div>
        </ComponentTile>
      </div>

      <div style={{ marginTop: variableStyles.fiveSpace }}>
        <ComponentTile label="Standard / Row / Right / Disabled">
          <div style={{ textAlign: 'right' }}>
            <RadioGroup
              direction="row"
              alignment="right"
              options={getMockOptions('sheet')}
              checked={checked}
              onChange={() => {}}
              isDisabled
            />
          </div>
        </ComponentTile>
      </div>
    </div>
  );
};

export const RadioGroupInButtonShapeSheet = () => {
  const [checked, setChecked] = useState<string>(null);

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <div style={{ marginTop: variableStyles.fiveSpace }}>
        <ComponentTile label="Standard / Column / Left">
          <div style={{ textAlign: 'left' }}>
            <RadioGroup
              isInButtonShape
              options={getMockOptions('buttonsheet')}
              checked={checked}
              onChange={({ checked: updatedChecked }) =>
                setChecked(updatedChecked)
              }
            />
          </div>
        </ComponentTile>
      </div>

      <div style={{ marginTop: variableStyles.fiveSpace }}>
        <ComponentTile label="Standard / Column / Right">
          <RadioGroup
            isInButtonShape
            options={getMockOptions('buttonsheet')}
            checked={checked}
            onChange={({ checked: updatedChecked }) =>
              setChecked(updatedChecked)
            }
            alignment="right"
            direction="column"
          />
        </ComponentTile>
      </div>

      <div style={{ marginTop: variableStyles.fiveSpace }}>
        <ComponentTile label="Standard / Row / Left">
          <RadioGroup
            isInButtonShape
            direction="row"
            alignment="left"
            options={getMockOptions('buttonsheet')}
            checked={checked}
            onChange={({ checked: updatedChecked }) =>
              setChecked(updatedChecked)
            }
          />
        </ComponentTile>
      </div>

      <div style={{ marginTop: variableStyles.fiveSpace }}>
        <ComponentTile label="Standard / Row / Right">
          <div style={{ textAlign: 'right' }}>
            <RadioGroup
              isInButtonShape
              direction="row"
              alignment="right"
              options={getMockOptions('buttonsheet')}
              checked={checked}
              onChange={({ checked: updatedChecked }) =>
                setChecked(updatedChecked)
              }
            />
          </div>
        </ComponentTile>
      </div>

      <div style={{ marginTop: variableStyles.fiveSpace }}>
        <ComponentTile label="Standard / Row / Right / Disabled">
          <div style={{ textAlign: 'right' }}>
            <RadioGroup
              isInButtonShape
              direction="row"
              alignment="right"
              options={getMockOptions('buttonsheet')}
              checked={checked}
              onChange={() => {}}
              isDisabled
            />
          </div>
        </ComponentTile>
      </div>
    </div>
  );
};

export default meta;
