import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import { BUTTON_TEST_ID } from '@constants/dataTestId';

import Alert from './Alert'; // Import the Alert component

import '@testing-library/jest-dom'; // For better DOM assertions

describe('Alert Component', () => {
  it('renders with required props', () => {
    const { getByText } = render(
      <Alert body="This is an alert message" theme="primary" />,
    );
    expect(getByText('This is an alert message')).toBeInTheDocument();
  });

  it('renders with optional props', () => {
    const { getByText, getByTestId } = render(
      <Alert
        body="This is an alert message"
        theme="primary"
        left={<span data-testid="left-icon">Icon</span>}
        isCancellable
        onCancelButtonClick={() => {}}
        isAnimated
      />,
    );
    expect(getByText('This is an alert message')).toBeInTheDocument();
    expect(getByTestId('left-icon')).toBeInTheDocument();
  });

  it('triggers cancel action when cancel button is clicked', () => {
    const handleCancel = jest.fn();
    const { getByTestId } = render(
      <Alert
        body="This is an alert message"
        theme="primary"
        isCancellable
        onCancelButtonClick={handleCancel}
      />,
    );
    fireEvent.click(getByTestId(`cancel-button-${BUTTON_TEST_ID}`));
    expect(handleCancel).toHaveBeenCalledTimes(1);
  });
});
