import { FC, forwardRef, ReactNode } from 'react';
import classNames from '@utils/classNames';

export interface SectionWrapperProps {
  /**
   * Children can be placed within the children placeholder in the JSX of the WrapperSection.
   */
  children: ReactNode;
}

const SectionWrapper: FC<SectionWrapperProps> = forwardRef<
  HTMLDivElement,
  SectionWrapperProps
>(({ children }, ref) => (
  <div ref={ref} className={classNames('multi-section-wrapper')}>
    {children}
  </div>
));

export default SectionWrapper;
