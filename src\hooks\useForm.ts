/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useMemo, useRef, useState } from 'react';
import { TYPE_MISMATCH_MESSAGE } from '@constants/common';

export interface CommonProps {
  type?:
    | 'radio'
    | 'radioGroup'
    | 'select'
    | 'checkbox'
    | 'textArea'
    | 'selectSearch'
    | 'phoneInput'
    | 'zipinput'
    | 'googlePlaceSearch';
  name: string;
  isDisabled?: boolean;
  error?: string;
  onFocus?: (e: FocusEvent) => void;
  onChange?: (e: any) => void;
  onInputValueChange?: ({ inputValue }) => void;
  onInvalid?: (e: any) => void;
  // Add any other common props here
}

export interface RegisterReturnBase {
  name: string;
  isDisabled?: boolean;
  error?: string;
  onFocus?: (e: FocusEvent) => void;
  onChange?: (e: any) => void;
  onInputValueChange?: ({ inputValue }) => void;
  isChecked?: boolean;
  checked?: boolean;
  value?: any;
  isTouched?: boolean;
  onInvalid?: (args: {
    validity?: any;
    errorMessage?: any;
    name: any;
    id: any;
  }) => void;
}

const updateStateFromKey = (key, value, currentState) => {
  if (!key) {
    throw Error('No key found, Pass the name or id for the field');
  }

  const newState = { ...currentState };
  const path = key.split('.');
  const finalProp = path.pop();
  let pointer = newState;
  path.forEach(el => {
    pointer[el] = { ...pointer[el] };
    pointer = pointer[el];
  });
  pointer[finalProp] = value;

  return newState;
};

const getValueFromKey = (key, currentState) => {
  const splitted = key.split('.');
  const obj = currentState;
  return splitted.reduce((prev, curr) => prev && prev[curr], obj);
};

const getFlattenKeysFromState = currentState => {
  const flatState = [];
  (function recurse(obj, current) {
    Object.keys(obj).forEach(key => {
      const value = obj[key];
      const newKey = current ? `${current}.${key}` : key;
      if (value && !Array.isArray(value) && typeof value === 'object') {
        recurse(value, newKey);
      } else {
        flatState.push(newKey);
      }
    });
  })(currentState);

  return flatState;
};

// TODO: need to write the types
const useForm = ({
  fields,
  onSubmit,
  errorMessages = {},
  isSubmitting,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
}: any) => {
  const initialState = useMemo(
    () => ({
      ...fields,
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }),
    [],
  );

  const [state, setState] = useState({ ...initialState });
  const [touchedFields, setTouchedFields] = useState({});
  const keys = useMemo(() => getFlattenKeysFromState(state), [state]);

  const errorsRef = useRef(
    /* eslint-disable no-param-reassign */
    keys.reduce((result, key) => {
      const value = getValueFromKey(key, state);
      if (!value || (Array.isArray(value) && !value[0])) {
        result = updateStateFromKey(key, '', result);
      }
      return result;
    }, {}),
    /* eslint-enable no-param-reassign */
  );

  const [errors, setErrors] = useState({ ...errorsRef.current });
  const initialErrorState = useMemo(
    () => keys.reduce((result, key) => ({ ...result, [key]: '' }), {}),
    [keys],
  );

  const getErrorMessageFromValidity = useCallback(
    (validity, inputKey) => {
      let invalidType;

      /* eslint-disable no-restricted-syntax */
      for (const key in validity) {
        if (validity[key]) {
          invalidType = key;
        }
      }

      const errorKeyMapping = {
        valueMissing: 'required',
        rangeOverflow: 'maxValue',
        rangeUnderflow: 'minValue',
        stepMismatch: 'stepMismatch',
        tooLong: 'maxLength',
        tooShort: 'minLength',
        typeMismatch: 'typeError',
        patternMismatch: 'pattern',
      };

      const inputKeyMapping = errorKeyMapping[invalidType];

      return (
        (errorMessages?.[inputKey]?.[invalidType] ||
          errorMessages?.[inputKey]?.[inputKeyMapping]) ??
        TYPE_MISMATCH_MESSAGE
      );
    },
    [errorMessages],
  );

  const onInvalid = useCallback(
    ({ validity = undefined, errorMessage = undefined, name: key, id }) => {
      const message =
        errorMessage || getErrorMessageFromValidity(validity, key || id);

      if (message) {
        errorsRef.current = updateStateFromKey(key, message, errorsRef.current);
        setErrors({ ...errorsRef.current });
      }
    },
    [getErrorMessageFromValidity],
  );

  const setIsValid = useCallback(key => {
    errorsRef.current = updateStateFromKey(key, '', errorsRef.current);
    setErrors({ ...errorsRef.current });
  }, []);

  const validateAllFields = useCallback(
    (fieldsToValidate: string[] = []) => {
      const fieldskeys = fieldsToValidate.length
        ? fieldsToValidate
        : getFlattenKeysFromState(state);

      let eachFieldValid = true;

      fieldskeys.forEach(key => {
        const fieldElement = document.getElementsByName(
          key,
        )[0] as HTMLInputElement;
        if (fieldElement) {
          const isValid = fieldElement.checkValidity();

          if (!isValid) {
            const { validity } = fieldElement;

            // Find the first invalid type (already handled in getErrorMessageFromValidity)
            const errorMessage =
              getErrorMessageFromValidity(validity, key) ||
              fieldElement.validationMessage ||
              TYPE_MISMATCH_MESSAGE;

            errorsRef.current = updateStateFromKey(
              key,
              errorMessage,
              errorsRef.current,
            );
            eachFieldValid = false;
          } else {
            setIsValid(key);
          }
        }
      });

      setErrors({ ...errorsRef.current });
      return eachFieldValid;
    },
    [state, setIsValid, getErrorMessageFromValidity],
  );

  const handleOnSubmit = useCallback(
    e => {
      e?.preventDefault();
      validateAllFields();
      const isValid = e ? e.currentTarget.checkValidity() : true;
      onSubmit({ data: state, isValid });
    },
    [onSubmit, state, validateAllFields],
  );

  const handleOnCancel = useCallback(() => {
    errorsRef.current = initialErrorState;
    setErrors(initialErrorState);
  }, [initialErrorState]);

  const reset = useCallback(
    ({ resetToState = null } = {}) => {
      const tempInitialState = resetToState || initialState;
      setErrors({ ...errorsRef.current });
      setState(prevState => ({
        ...prevState,
        ...tempInitialState,
      }));
      handleOnCancel();
    },
    [handleOnCancel, initialState],
  );

  const onValueChange = useCallback(
    ({ key }) =>
      value => {
        setIsValid(key);
        setState(currentState => updateStateFromKey(key, value, currentState));
      },
    [setIsValid],
  );

  const onInputValueChange = useCallback(
    ({ key }) =>
      ({ inputValue }) => {
        setIsValid(key);
        setState(currentState =>
          updateStateFromKey(key, inputValue, currentState),
        );
      },
    [setIsValid],
  );

  const onSelectChange = useCallback(
    ({ key }) =>
      value => {
        setIsValid(key);
        setState(currentState => updateStateFromKey(key, value, currentState));
      },
    [setIsValid],
  );

  const onTextChange = useCallback(
    event => {
      let value = event;

      if (typeof event === 'object') {
        value = event.target?.value || event.inputValue;
      }

      const name = event.target?.name || event.name;
      setIsValid(name);
      setState(currentState => updateStateFromKey(name, value, currentState));
    },
    [setIsValid],
  );

  const onCheckChange = useCallback(
    ({ key }) =>
      event => {
        const value = event?.target?.checked ?? event?.checked;
        setState(currentState => updateStateFromKey(key, value, currentState));
      },
    [],
  );

  const register = useCallback(
    <T extends CommonProps>(props: T): T & RegisterReturnBase => {
      const data: T & RegisterReturnBase = {
        ...props,
        onFocus: (e: FocusEvent) => {
          setTouchedFields(prev => ({
            ...prev,
            [props.name]: true,
          }));
          if (props.onFocus) props.onFocus(e);
        },
        Touched: touchedFields[props.name],
        onInvalid: args => {
          onInvalid(args);
          if (props.onInvalid) props.onInvalid(args);
        },
      };

      data.isDisabled = data.isDisabled || isSubmitting;
      data.error = data.error || errors[props.name];

      const fieldMapping = {
        radio: {
          onChange: onCheckChange({ key: props.name }),
          isChecked: state[props.name],
        },
        radioGroup: {
          onChange: onValueChange({ key: props.name }),
          checked: state[props.name]?.checked,
        },
        select: {
          onChange: onSelectChange({ key: props.name }),
          value: state[props.name],
        },
        checkbox: {
          onChange: onCheckChange({ key: props.name }),
          isChecked: state[props.name],
        },
        textArea: {
          onChange: onValueChange({ key: props.name }),
          value: state[props.name],
        },
        selectSearch: {
          onChange: onSelectChange({ key: props.name }),
          value: state[props.name],
        },
        phoneInput: {
          onChange: onValueChange({ key: props.name }),
          value: state[props.name],
        },
        googlePlaceSearch: {
          onInputValueChange: onInputValueChange({ key: props.name }),
          value: state[props.name],
        },
        zipInput: {
          onChange: onValueChange({ key: props.name }),
          value: state[props.name],
        },
      };

      if (props.type && fieldMapping[props.type]) {
        const mapping = fieldMapping[props.type];
        data.onChange = data.onChange || mapping.onChange;
        if ('isChecked' in mapping) {
          data.isChecked = data.isChecked || mapping.isChecked;
        }
        if ('checked' in mapping) {
          data.checked = data.checked || mapping.checked;
        }
        if ('value' in mapping) {
          data.value = data.value || mapping.value;
        }
        if ('onInputValueChange' in mapping) {
          data.onInputValueChange = (e: any) => {
            onTextChange(e);
            if (props.onInputValueChange) props.onInputValueChange(e);
          };
        }
      } else {
        data.onChange = (e: any) => {
          onTextChange(e);
          if (props.onChange) props.onChange(e);
        };
        data.value = data.value || state[props.name];
      }

      return data;
    },
    [
      errors,
      isSubmitting,
      onInputValueChange,
      onInvalid,
      onSelectChange,
      onTextChange,
      onValueChange,
      state,
      touchedFields,
      onCheckChange,
    ],
  );

  const setErrorForcefully = useCallback((key, errorMessage) => {
    errorsRef.current = updateStateFromKey(
      key,
      errorMessage,
      errorsRef.current,
    );
    setErrors({ ...errorsRef.current });
  }, []);

  return {
    state,
    setState,
    errors,
    onValueChange,
    onTextChange,
    onSelectChange,
    onCheckChange,
    onInvalid,
    setIsValid,
    onSubmit: handleOnSubmit,
    onCancel: handleOnCancel,
    register,
    onChange: onValueChange,
    reset,
    validateAllFields,
    setErrorForcefully,
  };
};

export default useForm;
