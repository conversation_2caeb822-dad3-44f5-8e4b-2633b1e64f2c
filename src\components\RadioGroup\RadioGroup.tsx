import { useCallback } from 'react';
import Radio from '@components/Radio';
import classNames from '@utils/classNames';

import styles from './RadioGroup.module.css';
import { OptionItem, RadioGroupPropTypes } from './RadioGroup.types';

/**
 * The RadioGroup component provides a group of radio inputs with customizable options.
 * It supports features such as rendering multiple options, default checked state,
 * onChange event handling, custom CSS classes, rendering options in a row,
 * disabling options, and specifying direction.
 *
 * @param {Object} props - The props for the RadioGroup component.
 * @param {Array<Object>} props.options - Required. An array of options for the radio group.
 * @param {string} props.options[].id - Required. The ID for the option.
 * @param {string} props.options[].label - Required. The label text for the option.
 * @param {string} props.checked - Required. The value of the checked option.
 * @param {Function} props.onChange - Required. The onChange event handler for the radio group.
 * @param {string} [props.className=''] - Optional. Additional CSS classes for styling purposes.
 * @param {string} [props.direction='column'] - Optional. Specifies the direction of the radio group ('row' or 'column').
 * @param {string} [props.alignment='left'] - Optional. The horizontal alignment of the radio inputs (left or right).
 * @param {string} [props.isMobileInline='false'] - Optional. Specifies whether the layout should behave like block view on mobile devices.
 * @param {boolean} [props.isDisabled=false] - Optional. Specifies whether the radio group is disabled.
 * @returns {JSX.Element} - The rendered RadioGroup component.
 */
const RadioGroup = ({
  name,
  options,
  checked,
  onChange,
  direction = 'column',
  alignment = 'left',
  isDisabled = false,
  isMobileInline = false,
  className = null,
  isInButtonShape = false,
}: RadioGroupPropTypes) => {
  const handleChange = useCallback(
    ({ option }: { option: OptionItem }) => {
      onChange({
        checked: option.value || option.id,
      });
    },
    [onChange],
  );
  return (
    <ul
      className={classNames(
        styles.radioGroup,
        styles[direction],
        styles[alignment],
        !isMobileInline && styles.mobileBlock,
        className,
      )}
    >
      {options.map(option => (
        <li key={option.id} className={styles.radioWrapper}>
          <Radio
            name={name}
            id={option.id}
            label={option.label}
            value={option.value}
            isChecked={
              (option.value && checked === option.value) ||
              (option.id && checked === option.id)
            }
            onChange={() => {
              handleChange({ option });
            }}
            isDisabled={isDisabled}
            isInButtonShape={isInButtonShape}
          />
          {option.right && <div className={styles.right}>{option.right}</div>}
        </li>
      ))}
    </ul>
  );
};

export default RadioGroup;
