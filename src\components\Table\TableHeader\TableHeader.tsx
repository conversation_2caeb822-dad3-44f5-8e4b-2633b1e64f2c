// import React, { forwardRef } from 'react';
// import {
//   ArrowDownIcon,
//   ArrowUpIcon,
//   FilterIcon,
//   MoreVerticalIcon,
// } from '@peddleon/ped-ux-react-icons';
// import {
//   Column,
//   flexRender,
//   HeaderGroup,
//   SortingState,
// } from '@tanstack/react-table';
// import Button from '@components/Button';
// import Dropdown from '@components/Dropdown';
// import { TABLE_ASC_LABEL_ID, TABLE_DESC_LABEL_ID } from '@constants/components';
// import classNames from '@utils/classNames';
// import { ascOrder, descOrder } from '@utils/tableSortinglabel';

// import styles from './TableHeader.module.css';

// interface TableColumn {
//   header: string;
//   isDesc?: boolean;
//   accessor: string;
//   sorted?: boolean;
//   sortingEnabled?: boolean;
//   minWidth?: number;
//   width?: number;
// }

// interface TableHeaderProps {
//   /** array of table columns */
//   columns: HeaderGroup<TableColumn>[];
//   isInverted?: boolean;
//   hasFirstColumnPinned?: boolean;
//   onColumnOptionClick?: (column: string, option: string) => void;
//   setSorting: (updater: SortingState) => void;
// }

// const TableHeader: React.ForwardRefRenderFunction<
//   HTMLTableSectionElement,
//   TableHeaderProps
// > = (
//   {
//     columns,
//     isInverted = false,
//     hasFirstColumnPinned,
//     setSorting,
//     onColumnOptionClick,
//   },
//   ref,
// ) => {
//   // define types for Column<TData, TValue>;
//   const handleDropdownClick =
//     (data: Column<TableColumn>) =>
//     (event: React.SyntheticEvent, id: string) => {
//       event.stopPropagation();
//       const [dropdownId, answeredSort] = id.split('_');

//       if (dropdownId === 'sort')
//         if (setSorting)
//           setSorting([{ id: data.id, desc: answeredSort === 'desc' }]);
//       onColumnOptionClick(data.columnDef.id, id);
//     };
//   return (
//     <thead className={isInverted ? styles.isInverted : undefined} ref={ref}>
//       {columns.map(headerGroup => (
//         <tr
//           key={headerGroup.id}
//           className={classNames(
//             styles.headerGroup,
//             isInverted && styles.isInverted,
//           )}
//         >
//           {headerGroup.headers.map((header, index) => {
//             const hasDisableSorting =
//               typeof header.column.columnDef?.enableSorting !== 'undefined' &&
//               !header.column.columnDef.enableSorting;

//             const hasFilter =
//               typeof header.column.columnDef?.enableColumnFilter !==
//                 'undefined' && header.column.columnDef.enableColumnFilter;

//             const defaultOptions = [
//               {
//                 label: ascOrder(header.column.columnDef.type),
//                 id: TABLE_ASC_LABEL_ID,
//                 left: <ArrowUpIcon height="20px" width="20px" />,
//               },
//               {
//                 label: descOrder(header.column.columnDef.type),
//                 id: TABLE_DESC_LABEL_ID,
//                 left: <ArrowDownIcon height="20px" width="20px" />,
//               },
//             ];

//             let updatedOptions = [...defaultOptions];

//             if (header.column.getIsSorted()) {
//               updatedOptions = updatedOptions.filter(option => {
//                 const isDesc = header.column.getIsSorted() === 'desc';
//                 return isDesc
//                   ? option.id !== TABLE_DESC_LABEL_ID
//                   : option.id !== TABLE_ASC_LABEL_ID;
//               });
//             }

//             if (hasFilter) {
//               updatedOptions.push({
//                 label: 'Filter',
//                 id: 'filter',
//                 left: <FilterIcon height="20px" width="20px" />,
//               });
//             }

//             return (
//               <th
//                 // To provide custom style with max , min width of width
//                 style={{
//                   width: header.column.columnDef?.size,
//                   maxWidth: header.column.columnDef.maxSize,
//                 }}
//                 key={header.id}
//                 onClick={header.column.getToggleSortingHandler()}
//                 className={
//                   hasFirstColumnPinned && !index && styles.hasFirstColumnPinned
//                 }
//               >
//                 <div
//                   tabIndex={0}
//                   role="button"
//                   className={classNames(
//                     styles.header,
//                     header.column.getIsSorted() ? styles.headerSorted : '',
//                     hasDisableSorting ? styles.disableSorting : '',
//                   )}
//                 >
//                   <div role="cell" className={styles.headerRoot}>
//                     {flexRender(
//                       header.column.columnDef.header,
//                       header.getContext(),
//                     )}
//                     {{
//                       asc: (
//                         <div
//                           className={classNames(
//                             styles.arrowIconWrapper,
//                             styles.ascending,
//                           )}
//                         >
//                           <ArrowDownIcon height="20px" width="20px" />
//                         </div>
//                       ),
//                       desc: (
//                         <div
//                           className={classNames(
//                             styles.arrowIconWrapper,
//                             styles.descending,
//                           )}
//                         >
//                           <ArrowDownIcon height="20px" width="20px" />
//                         </div>
//                       ),
//                     }[header.column.getIsSorted() as string] ?? null}
//                     {/*  */}
//                     {!hasDisableSorting && (
//                       <Dropdown
//                         id="dropdown"
//                         item={{
//                           lists: updatedOptions,
//                         }}
//                         onClick={handleDropdownClick(header.column)}
//                       >
//                         <Button
//                           className={styles.columnDropdown}
//                           id="dropdown"
//                           theme="transparent"
//                         >
//                           <MoreVerticalIcon
//                             id="dropdown"
//                             stroke="currentColor"
//                             height={20}
//                             width={20}
//                           />
//                         </Button>
//                       </Dropdown>
//                     )}
//                   </div>
//                 </div>
//               </th>
//             );
//           })}
//         </tr>
//       ))}
//     </thead>
//   );
// };

// export default forwardRef(TableHeader);
