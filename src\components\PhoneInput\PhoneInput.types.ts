import { TextInputProps } from '@components/TextInput/TextInput.types';

export interface PhoneInputPropTypes {
  /**
   * Unique identifier for the input field.
   */
  id?: TextInputProps['id'];
  /**
   * Label text to display for the phone input field.
   */
  label?: TextInputProps['label'];
  /**
   * Caption or helper text displayed below the input field.
   */
  caption?: TextInputProps['caption'];
  /**
   * Current value of the phone input field.
   */
  value: string;
  /**
   * Error message to display if the input value is invalid.
   */
  error?: TextInputProps['error'];
  /**
   * If true, the input field will be disabled and not editable.
   */
  isDisabled?: TextInputProps['isDisabled'];
  /**
   * Callback function triggered when the value of the input changes.
   */
  onChange: (value: string) => void;
  /**
   * If true, the input field will be marked as required.
   */
  required?: TextInputProps['required'];
  /**
   * Callback function triggered when the input is invalid.
   */
  onInvalid?: TextInputProps['onInvalid'];
  /**
   * Callback function triggered when the input field gains focus.
   */
  onFocus?: () => void;
  /**
   * Callback function triggered when the input field loses focus.
   */
  onBlur?: () => void;
  /**
   * Name attribute for the input field, used for form submissions.
   */
  name?: string;
  /**
   * Regular expression pattern to validate the input value.
   */
  pattern?: string;
}
