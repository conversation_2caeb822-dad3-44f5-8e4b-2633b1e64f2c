import React from 'react';
import { render } from '@testing-library/react';

import Divider from './Divider';

describe('Divider Component', () => {
  test('renders with default theme correctly', () => {
    const { container } = render(<Divider />);
    const dividerElement = container.querySelector('hr');

    expect(dividerElement).toBeInTheDocument();
    expect(dividerElement).toHaveClass('greyTheme');
  });
});
