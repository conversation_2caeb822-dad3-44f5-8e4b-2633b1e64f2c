@value variables: "../../styles/variables.module.css";
@value typography: "../../styles/typography.module.css";
@value black5, black35 from variables;
@value white5, white35 from variables;
@value smallWidth,mediumWidth,largeWidth,oneSpace, twoSpace, threeSpace, fiveSpace, ease-out-expo from variables;
@value buttonLabelXLarge, buttonLabelLarge, buttonLabelSmall from typography;

.button {
  position: relative;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  border-radius: 28px;
  transition:
    background-color 0.5s ease-out-expo,
    border-color 0.5s ease-out-expo,
    color 0.5s ease-out-expo;

  &:disabled {
    pointer-events: none;
    background-color: black5;
    color: black35;
    border-color: black5;
  }

  &.disabledInverted:disabled {
    background-color: white5;
    color: white35;
    border-color: white5;
  }

  &:focus {
    outline: none;
  }
}

.wrapper {
  line-height: 0;
  transition: opacity 0.5s ease-out-expo;
}

.leftWrapper {
  composes: wrapper;
  margin-right: oneSpace;
  pointer-events: none;
}

.rightWrapper {
  composes: wrapper;
  pointer-events: none;
}

.fetchingIconWrapper {
  composes: wrapper;
  position: absolute;
  top: 50%;
  left: 50%;
  opacity: 0;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.label {
  pointer-events: none;
}

/* variants */
.fullWidth {
  width: 100%;
  justify-content: center;
}

.square {
  border-radius: 0;
}

.loading {
  pointer-events: none;

  & .leftWrapper,
  & .rightWrapper,
  & .label {
    opacity: 0;
  }

  & .fetchingIconWrapper {
    opacity: 1;
  }
}

.xLargeSize {
  composes: buttonLabelXLarge;
  padding-right: fiveSpace;
  padding-left: fiveSpace;
  min-height: var(--tenSpace);
  &:not(.square) {
    border-radius: var(--fiveSpace);
  }
}

.largeSize {
  composes: buttonLabelLarge;
  padding-right: threeSpace;
  padding-left: threeSpace;
  min-height: 56px;
}

.smallSize {
  composes: buttonLabelSmall;
  padding-right: twoSpace;
  padding-left: twoSpace;
  min-height: 44px;
}

.xSmallSize {
  composes: buttonLabelSmall;
  padding-right: twoSpace;
  padding-left: twoSpace;
  min-height: 36px;
}

.iconWrapper {
  composes: buttonLabelSmall;
  padding: calc(oneSpace + 2px) calc(oneSpace + 4px);
}

.rightWithLabel {
  margin-left: oneSpace;
}
.rightWithoutLabel {
  margin-left: 0px;
  @media (max-width: smallWidth) {
    padding-right: 0px;
  }
}

.rightWithoutLabel svg {
  @media (max-width: smallWidth) {
    width: 20px;
  }
}

.iconButton {
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 50%;
  transition:
    background-color 0.5s ease-out-expo,
    border-color 0.5s ease-out-expo,
    color 0.5s ease-out-expo;

  &:disabled {
    pointer-events: none;
    background-color: black5;
    color: black35;
    border-color: black5;
  }

  &.disabledInverted:disabled {
    background-color: white5;
    color: white35;
    border-color: white5;
  }
}

.childrenWrapper {
  composes: wrapper;
  pointer-events: none;
}

.fetching {
  pointer-events: none;

  & .childrenWrapper {
    opacity: 0;
  }

  & .fetchingIconWrapper {
    opacity: 1;
  }
}

.largeSizeIcon {
  height: 56px;
  width: 56px;
}

.smallSizeIcon {
  height: 44px;
  width: 44px;
}

.xSmallSizeIcon {
  height: 20px;
  width: 44px;
}
