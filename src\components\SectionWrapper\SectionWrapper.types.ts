import { ReactNode } from 'react';
import { DeviderTheme } from '@components/Divider/Divider.types';
import { ActionDirection } from '@components/SectionTitle/SectionTitle.types';
import { TagsTypes } from '@components/Typography/Typography.types';

interface WrapperOptions {
  hasDevider?: boolean;
  dividerTheme?: DeviderTheme;
  actionDirection?: ActionDirection;
  headingTag?: TagsTypes;
  headingProps?: Record<string, unknown>;
}

export interface SectionWrapperProps {
  /**
   * Children can be placed within the children placeholder in the JSX of the WrapperSection.
   */
  children: ReactNode;
  /**
   * Heading text of the section.
   */
  headingText?: string;
  /**
   * Icon to display alongside the heading.
   */
  icon?: ReactNode;
  /**
   * An array of Action Button objects representing action buttons within the section.
   */
  actions?: ReactNode[];
  /**
   * Options provide additional configuration for functionality such as divider, action position, etc.
   *
   * `hasDevider` is used to set a divider at the bottom of the wrapper.
   *
   * `dividerTheme` is used to set a predefined theme.
   *
   * `actionDirection` is used to align all actions to the right or left.
   *
   * `headingTag` is used to change the native behavior of the heading, for example, h1, h2, h3, p, etc.
   */
  options?: WrapperOptions;
  /**
   * Additional class name to be added to the wrapper.
   */
  wrapperClassName?: string;
}
