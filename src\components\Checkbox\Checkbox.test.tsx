import { render, screen } from '@testing-library/react';

import Checkbox from './Checkbox';

// Rendering Test
test('renders checkbox with default props', () => {
  render(<Checkbox isChecked={false} />);
  const checkbox = screen.getByRole('checkbox');
  expect(checkbox).toBeInTheDocument();
});

// Check Label Rendering
test('renders label when provided', () => {
  render(<Checkbox isChecked={false} label="Example Label" />);
  const label = screen.getByText('Example Label');
  expect(label).toBeInTheDocument();
});

// Checking Checkbox State
test('checkbox state changes on click', () => {
  render(<Checkbox isChecked />);
  const checkbox = screen.getByRole('checkbox');
  expect(checkbox).toBeChecked();
});

// Disabled State Test
test('renders disabled checkbox', () => {
  render(<Checkbox isChecked={false} isDisabled />);
  const checkbox = screen.getByRole('checkbox');
  expect(checkbox).toBeDisabled();
});

// Accessibility Test
test('checkbox is accessible', () => {
  render(<Checkbox isChecked={false} label="Accessible Checkbox" />);
  const checkbox = screen.getByRole('checkbox');
  expect(checkbox).toHaveAccessibleName();
});

// Custom Theme Test
test('renders checkbox with custom theme', () => {
  render(<Checkbox isChecked={false} theme="secondary" />);
  const checkbox = screen.getByRole('checkbox');
  expect(checkbox.parentElement).toHaveClass('secondary');
});
