import React from 'react';
import AnimatedLoader from '@components/AnimatedLoader';
import classNames from '@utils/classNames';

import styles from './ComponentLoader.module.css';

interface ComponentLoaderProps {
  isLoading: boolean;
  children: React.ReactNode;
  size?: number | string;
}

const ComponentLoader: React.FC<ComponentLoaderProps> = ({
  isLoading,
  children,
  size = 24,
}) => (
  <div className={styles.componentLoaderWrapper}>
    {isLoading && (
      <div className={styles.loaderOverlay}>
        <AnimatedLoader size={size} />
      </div>
    )}
    <div
      className={classNames(
        styles.componentContent,
        isLoading && styles.blurred,
      )}
    >
      {children}
    </div>
  </div>
);

export default ComponentLoader;
