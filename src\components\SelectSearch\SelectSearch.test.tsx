import { useState } from 'react';
import { render, screen } from '@testing-library/react';
// eslint-disable-next-line import/no-extraneous-dependencies
import userEvent from '@testing-library/user-event';
import { TEXT_INPUT_DATA_TEST_ID } from '@constants/dataTestId';

import SelectInput from './SelectSearch';

// Mocking the scrollIntoView function
window.HTMLElement.prototype.scrollIntoView = function mock () {};

// Mocking the useDebouncedCallback hook
jest.mock('@hooks/useDebouncedCallback', () => callback => callback);

// Define option items for testing
const OPTION_ITEMS = [
  { value: 'value1', label: 'Label 1' },
  { value: 'test1', label: 'Test 1' },
  { value: 'value2', label: 'Label 2' },
  { value: 'value3', label: 'Label 3' },
];

// Component wrapper for testing the SelectInput component
const SelectInputWrapper = ({
  items = OPTION_ITEMS,
  value = null,
  placeholder = 'Placeholder',
}) => {
  const [state, setState] = useState(value);
  return (
    <SelectInput
      items={items}
      value={state}
      placeholder={placeholder}
      onChange={v => setState(v)}
    />
  );
};

describe('SelectInput Component', () => {
  it('should render the select input with a placeholder value of "Placeholder"', async () => {
    // Render the component with some props
    render(<SelectInputWrapper />);

    // Expect the input to be visible in the browser
    expect(screen.getByTestId(TEXT_INPUT_DATA_TEST_ID)).toBeVisible();
    
    // Expect the placeholder to be in the document
    expect(screen.getByPlaceholderText('Placeholder')).toBeInTheDocument();

  });

  it('renders with a preselected value', async () => {
    // Render the component with a preselected value
    render(<SelectInputWrapper value={{ label: 'Label 1', value: 'value1' }} />);

    // Click on the input
    await userEvent.click(screen.getByTestId(TEXT_INPUT_DATA_TEST_ID));

    // Expect 'Label 1' to be in the document
    expect(screen.getByText('Label 1')).toBeInTheDocument();

    // Check that 'Label 1' is highlighted based on the applied class name
    expect(screen.getByText('Label 1').parentElement.parentElement).toHaveClass(
      'itemHighlighted',
    );
  });

  it('should search, highlight, and set the value on the Enter key', async () => {
    // Render the component with a preselected value
    render(<SelectInputWrapper />);

    const input = screen.getByTestId(TEXT_INPUT_DATA_TEST_ID);
    await userEvent.click(input);

    // Type 'Test 1' and press Enter
    await userEvent.type(input, 'Test 1{Enter}');

    // The input should have the value 'Test 1' after the modal is closed
    expect(input).toHaveValue('Test 1');
  });

  it('should display no match found if not found while searching', async () => {
    // Render the component with a preselected value
    render(<SelectInputWrapper />);

    const input = screen.getByTestId(TEXT_INPUT_DATA_TEST_ID);
    await userEvent.click(input);

    // Type 'Test 1' and press Enter
    await userEvent.type(input, 'Test 123');

    // The input should have the value 'Test 1' after the modal is closed
    expect(screen.getByText('No matches')).toHaveClass('notMatchFound');
    expect(screen.getByText('No matches')).toBeInTheDocument();
  });
});
