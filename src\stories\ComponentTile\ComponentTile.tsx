import type { ReactNode } from 'react';
import styles from '@stories/ComponentTile/ComponentTile.module.css';
import typographyStyles from '@styles/typography.module.css';

const ComponentTile = ({
  label,
  children,
}: {
  label?: string;
  children: ReactNode;
}) => (
  <div className={styles.componentTile}>
    <div className={styles.container}>
      <div className={styles.childrenWrapper}>{children}</div>

      <h3 className={[styles.label, typographyStyles.caption].join(' ')}>
        {label}
      </h3>
    </div>
  </div>
);

export default ComponentTile;
