/* eslint-disable no-console */
import React, { useEffect } from 'react';

interface GooglePlacesScriptProps {
  googlePlacesAPIKey: string;
}

const GooglePlacesScript: React.FC<GooglePlacesScriptProps> = ({
  googlePlacesAPIKey,
}) => {
  useEffect(() => {
    // Check if the Google Maps JavaScript API script already exists
    const existingScript = document.getElementById('google-place-script');
    if (existingScript || window?.google) {
      return;
    }

    // This useEffect will run only once when the component mounts
    const googlePlacesScript = document.createElement('script');
    if (googlePlacesAPIKey) {
      googlePlacesScript.src = `https://maps.googleapis.com/maps/api/js?key=${googlePlacesAPIKey}&libraries=places`;
      googlePlacesScript.id = 'google-place-script';
      googlePlacesScript.async = true; // Add async attribute for better performance
      document.head.appendChild(googlePlacesScript);
    } else {
      console.error('Google Place API Key is required to inject script!!');
    }

    // Cleanup function to remove the script when the component unmounts
    // eslint-disable-next-line consistent-return
    return () => {
      document.head.removeChild(googlePlacesScript);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return null;
};

export default GooglePlacesScript;
