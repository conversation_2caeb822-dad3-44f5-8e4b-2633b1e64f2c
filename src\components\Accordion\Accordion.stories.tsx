import StatusIndicator from '@components/StatusIndicator';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';

import variableStyles from '../../styles/variables.module.css';
import { FluidRoot } from '..';
import Accordion from './Accordion';

export default {
  title: 'Components/Utility',
};

const primaryItems = [
  {
    label: 'Vehicle',
    content: '2003 Honda CR-V',
    left: (
      <div style={{ marginRight: variableStyles.twoSpace, lineHeight: 1 }}>
        <StatusIndicator theme="primary" isComplete />
      </div>
    ),
  },
  {
    label: 'Ownership',
    content: '<PERSON><PERSON><PERSON>',
    left: (
      <div style={{ marginRight: variableStyles.twoSpace, lineHeight: 1 }}>
        <StatusIndicator theme="warning" isComplete />
      </div>
    ),
    isActive: true,
  },
  {
    label: 'Condition',
    content: 'Perfect, with new a/c',
    left: (
      <div style={{ marginRight: variableStyles.twoSpace, lineHeight: 1 }}>
        <StatusIndicator theme="neutralDark" isComplete />
      </div>
    ),
    isDisabled: true,
  },
  {
    label: 'Offer',
    content: '$1500',
    left: (
      <div style={{ marginRight: variableStyles.twoSpace, lineHeight: 1 }}>
        <StatusIndicator theme="neutralDark" isComplete />
      </div>
    ),
    isDisabled: true,
  },
];

const secondaryItems = [
  {
    label: 'Vehicle',
    content: '2003 Honda CR-V',
  },
  {
    label: 'Ownership',
    content: 'Maritess McClain',
    isActive: true,
  },
  {
    label: 'Condition',
    content: 'Perfect, with new a/c',
    isDisabled: true,
  },
  {
    label: 'Offer',
    content: '$1500',
    isDisabled: true,
  },
];

const displayItems = [
  {
    label: 'Who the heck is Peddle?',
    content:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.',
  },
  {
    label: 'How do offers work?',
    content:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.',
  },
  {
    label: 'What cities does Peddle operate in?',
    content:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.',
  },
  {
    label: 'Who comes to pick up my car?',
    content:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.',
  },
  {
    label: 'What kind of cars does Peddle help sell?',
    content:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.',
  },
  {
    label: "What's with all the toy cars?",
    content:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.',
  },
];

export const AccordionSheet = () => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
    }}
  >
    <FluidRoot>
      <ComponentGrid countColumns={1}>
        <ComponentTile label="Accordion / Primary">
          <Accordion items={primaryItems} theme="primaryMain" />
        </ComponentTile>

        <ComponentTile label="Accordion / Secondary">
          <Accordion items={secondaryItems} theme="secondary" />
        </ComponentTile>

        <ComponentTile label="Accordion / Display V1">
          <Accordion items={displayItems} theme="displayV1" />
        </ComponentTile>

        <ComponentTile label="Accordion / Display">
          <Accordion items={displayItems} theme="display" />
        </ComponentTile>
      </ComponentGrid>
    </FluidRoot>
  </div>
);

AccordionSheet.storyName = 'Accordion';
