name: Create git and Github release

on:
  push:
    branches:
      - develop

permissions:
  contents: write
  pull-requests: write

jobs:
  release-please:
    runs-on: ubuntu-latest
    steps:
      - name: Set release date
        run: echo "RELEASE_DATE=$(date +'%Y-%m-%d')" >> $GITHUB_ENV

      - name: Create release
        uses: google-github-actions/release-please-action@v3
        with:
          release-type: node
          package-name: release-please-action
          pull-request-header: "Release created at ${{ env.RELEASE_DATE }}"
          default-branch: develop
          changelog-types: '[{"type":"feat","section":"Features","hidden":false},{"type":"fix","section":"Bug Fixes","hidden":false},{"type":"chore","section":"Miscellaneous","hidden":false},{"type":"build","section":"Miscellaneous","hidden":false},{"type":"ci","section":"Miscellaneous","hidden":false},{"type":"docs","section":"Miscellaneous","hidden":false},{"type":"perf","section":"Miscellaneous","hidden":false},{"type":"refactor","section":"Miscellaneous","hidden":false},{"type":"style","section":"Miscellaneous","hidden":false},{"type":"revert","section":"Miscellaneous","hidden":false},{"type":"test","section":"Miscellaneous","hidden":false},{"type":"feat!","section":"BREAKING CHANGES","hidden":false},{"type":"fix!","section":"BREAKING CHANGES","hidden":false},{"type":"chore!","section":"BREAKING CHANGES","hidden":false},{"type":"build!","section":"BREAKING CHANGES","hidden":false},{"type":"ci!","section":"BREAKING CHANGES","hidden":false},{"type":"docs!","section":"BREAKING CHANGES","hidden":false},{"type":"perf!","section":"BREAKING CHANGES","hidden":false},{"type":"refactor!","section":"BREAKING CHANGES","hidden":false},{"type":"style!","section":"BREAKING CHANGES","hidden":false},{"type":"revert!","section":"BREAKING CHANGES","hidden":false},{"type":"test!","section":"BREAKING CHANGES","hidden":false}]'
