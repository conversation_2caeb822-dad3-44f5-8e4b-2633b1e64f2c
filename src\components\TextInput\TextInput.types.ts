import { ReactEventHandler } from 'react';
import { InputPropType } from '@libtypes/common';

export interface TextInputProps extends InputPropType {
  type?: 'text' | 'email' | 'password' | 'number' | 'search' | 'tel' | 'url';

  minLength?: HTMLInputElement['minLength'];

  maxLength?: HTMLInputElement['maxLength'];

  isSquare?: boolean;

  shouldHideErrorMessage?: boolean;

  isAutoFocused?: boolean;

  isFocused?: boolean;

  className?: string;

  onChange: ReactEventHandler<HTMLInputElement | HTMLTextAreaElement>;

  inputMode?:
    | 'text'
    | 'email'
    | 'search'
    | 'tel'
    | 'url'
    | 'none'
    | 'numeric'
    | 'decimal';

  pattern?: string;

  resize?: 'none' | 'both' | 'horizontal' | 'vertical';

  placeholder?: string;

  autoComplete?: string;

  hideRightIcon?: boolean;

  readOnly?: boolean;

  hideErrorMessage?: boolean;

  title?: string;

  isNewVariant?: boolean;
}
