// Button.stories.js
// import {
//   DownloadIcon,
//   FilterIcon,
//   PlusIcon,
//   SearchIcon,
// } from '@peddleon/ped-ux-react-icons';
import type { Meta } from '@storybook/react';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

import Button from './Button'; // Assuming the file where Button component is exported is named Button.js

/**
The Button component provides a customizable button interface for various usage scenarios within the application.

## Overview

The Button component offers a wide range of customization options such as size, theme, and state. It is designed to be flexible and adaptable to different design requirements.

## Usage

To use the Button component in your React application, import it from the `Components/Button` directory and render it with appropriate props.

Import the component into your React application:

```jsx
import { Button } from '@peddleon/ped-ux-react-library';```
 
 */
const meta: Meta<typeof Button> = {
  title: 'Core/Button',
  // tags: ['autodocs'],
  component: Button,
  argTypes: {
    theme: {
      control: 'select',
      options: [
        'dark',
        'light',
        'darkInverted',
        'lightInverted',
        'grey',
        'greyInverted',
        'transparent',
        'transparentInverted',
        'primary',
        'primaryFaded',
        'primaryInverted',
        'danger',
        'dangerFaded',
        'dangerInverted',
        'success',
        'successInverted',
        'successFaded',
        'warning',
        'warningInverted',
        'warningFaded',
      ],
    },
  },
};

const Template = args => <Button {...args} />;

export const StoryButton = Template.bind({});
StoryButton.args = {
  label: 'Button',
  theme: 'warning',
  size: 'large',
  type: 'button',
  isFullWidth: false,
  isLoading: false,
  isDisabled: false,
  isSquare: false,
  tabIndex: 0,
};

export const Primary = () => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
      display: 'flex',
      gap: '10px',
    }}
  >
    <Button label="Submit" theme="warning" />
    {/* <Button
      label="Add"
      theme="warning"
      right={<PlusIcon width={24} height={24} />}
    />
    <Button
      label="Download"
      theme="warning"
      right={<DownloadIcon width={24} height={24} />}
    /> */}
  </div>
);

Primary.args = {};

export const Transparent = () => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
      display: 'flex',
    }}
  >
    <Button label="Cancel" theme="transparent" />
    <Button label="Save" theme="warning" />
  </div>
);

Transparent.args = {};

export const Light = () => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
      display: 'flex',
      gap: '10px',
    }}
  >
    {/* <Button theme="light">
      <SearchIcon width={24} height={24} />
    </Button>
    <Button theme="light">
      <FilterIcon width={24} height={24} />
    </Button> */}
  </div>
);
Light.args = {};

export const Small = () => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
      display: 'flex',
      gap: '10px',
    }}
  >
    <Button label="Edit" size="small" theme="warning" />
    <Button label="Delete" size="small" theme="danger" />
  </div>
);

Small.args = {};

export const Large = () => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
      display: 'flex',
      gap: '10px',
    }}
  >
    <Button label="Start now" size="large" theme="warning" />
  </div>
);
Large.args = {};

export const Disabled = Template.bind({});
Disabled.args = {
  label: 'Disabled Button',
  isDisabled: true,
  theme: 'warning',
};

export const Loading = Template.bind({});
Loading.args = {
  label: 'Loading Button',
  isLoading: true,
  theme: 'warning',
};

export const Square = Template.bind({});
Square.args = {
  label: 'Square Button',
  isSquare: true,
  theme: 'warning',
};

export const ButtonSheet = ({ label }) => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
    }}
  >
    <ComponentGrid countColumns={3}>
      <ComponentTile label="Dark / Large">
        <Button label={label} theme="dark" />
      </ComponentTile>
      <ComponentTile label="Dark / Large / Loading">
        <Button label={label} theme="dark" isLoading />
      </ComponentTile>
      <ComponentTile label="Dark / Large / Disabled">
        <Button label={label} theme="dark" isDisabled />
      </ComponentTile>

      <ComponentTile label="Dark / Small">
        <Button label={label} theme="dark" size="small" />
      </ComponentTile>
      <ComponentTile label="Dark / Small / Loading">
        <Button label={label} theme="dark" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Dark / Small / Disabled">
        <Button label={label} theme="dark" size="small" isDisabled />
      </ComponentTile>

      <ComponentTile label="Light / Large">
        <Button label={label} theme="light" />
      </ComponentTile>
      <ComponentTile label="Light / Large / Loading">
        <Button label={label} theme="light" isLoading />
      </ComponentTile>
      <ComponentTile label="Light / Large / Disabled">
        <Button label={label} theme="light" isDisabled />
      </ComponentTile>

      <ComponentTile label="Light / Small">
        <Button label={label} theme="light" size="small" />
      </ComponentTile>
      <ComponentTile label="Light / Small / Loading">
        <Button label={label} theme="light" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Light / Small / Disabled">
        <Button label={label} theme="light" size="small" isDisabled />
      </ComponentTile>

      <ComponentTile label="Grey / Large">
        <Button label={label} theme="grey" />
      </ComponentTile>
      <ComponentTile label="Grey / Large / Loading">
        <Button label={label} theme="grey" isLoading />
      </ComponentTile>
      <ComponentTile label="Grey / Large / Disabled">
        <Button label={label} theme="grey" isDisabled />
      </ComponentTile>

      <ComponentTile label="Grey / Small">
        <Button label={label} theme="grey" size="small" />
      </ComponentTile>
      <ComponentTile label="Grey / Small / Loading">
        <Button label={label} theme="grey" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Grey / Small / Disabled">
        <Button label={label} theme="grey" size="small" isDisabled />
      </ComponentTile>

      <ComponentTile label="Transparent / Large">
        <Button label={label} theme="transparent" />
      </ComponentTile>
      <ComponentTile label="Transparent / Large / Loading">
        <Button label={label} theme="transparent" isLoading />
      </ComponentTile>
      <ComponentTile label="Transparent / Large / Disabled">
        <Button label={label} theme="transparent" isDisabled />
      </ComponentTile>

      <ComponentTile label="Transparent / Small">
        <Button label={label} theme="transparent" size="small" />
      </ComponentTile>
      <ComponentTile label="Transparent / Small / Loading">
        <Button label={label} theme="transparent" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Transparent / Small / Disabled">
        <Button label={label} theme="transparent" size="small" isDisabled />
      </ComponentTile>

      <ComponentTile label="Primary / Large">
        <Button label={label} theme="primary" />
      </ComponentTile>
      <ComponentTile label="Primary / Large / Loading">
        <Button label={label} theme="primary" isLoading />
      </ComponentTile>
      <ComponentTile label="Primary / Large / Disabled">
        <Button label={label} theme="primary" isDisabled />
      </ComponentTile>

      <ComponentTile label="Primary / Small">
        <Button label={label} theme="primary" size="small" />
      </ComponentTile>
      <ComponentTile label="Primary / Small / Loading">
        <Button label={label} theme="primary" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Primary / Small / Disabled">
        <Button label={label} theme="primary" size="small" isDisabled />
      </ComponentTile>

      <ComponentTile label="Primary Faded / Large">
        <Button label={label} theme="primaryFaded" />
      </ComponentTile>
      <ComponentTile label="Primary Faded / Large / Loading">
        <Button label={label} theme="primaryFaded" isLoading />
      </ComponentTile>
      <ComponentTile label="Primary Faded / Large / Disabled">
        <Button label={label} theme="primaryFaded" isDisabled />
      </ComponentTile>

      <ComponentTile label="Primary Faded / Small">
        <Button label={label} theme="primaryFaded" size="small" />
      </ComponentTile>
      <ComponentTile label="Primary Faded / Small / Loading">
        <Button label={label} theme="primaryFaded" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Primary Faded / Small / Disabled">
        <Button label={label} theme="primaryFaded" size="small" isDisabled />
      </ComponentTile>

      <ComponentTile label="Danger / Large">
        <Button label={label} theme="danger" />
      </ComponentTile>
      <ComponentTile label="Danger / Large / Loading">
        <Button label={label} theme="danger" isLoading />
      </ComponentTile>
      <ComponentTile label="Danger / Large / Disabled">
        <Button label={label} theme="danger" isDisabled />
      </ComponentTile>

      <ComponentTile label="Danger / Small">
        <Button label={label} theme="danger" size="small" />
      </ComponentTile>
      <ComponentTile label="Danger / Small / Loading">
        <Button label={label} theme="danger" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Danger / Small / Disabled">
        <Button label={label} theme="danger" size="small" isDisabled />
      </ComponentTile>

      <ComponentTile label="Danger Faded / Large">
        <Button label={label} theme="dangerFaded" />
      </ComponentTile>
      <ComponentTile label="Danger Faded / Large / Loading">
        <Button label={label} theme="dangerFaded" isLoading />
      </ComponentTile>
      <ComponentTile label="Danger Faded / Large / Disabled">
        <Button label={label} theme="dangerFaded" isDisabled />
      </ComponentTile>

      <ComponentTile label="Danger Faded / Small">
        <Button label={label} theme="dangerFaded" size="small" />
      </ComponentTile>
      <ComponentTile label="Danger Faded / Small / Loading">
        <Button label={label} theme="dangerFaded" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Danger Faded / Small / Disabled">
        <Button label={label} theme="dangerFaded" size="small" isDisabled />
      </ComponentTile>

      <ComponentTile label="Warning / Large">
        <Button label={label} theme="warning" />
      </ComponentTile>
      <ComponentTile label="Warning / Large / Loading">
        <Button label={label} theme="warning" isLoading />
      </ComponentTile>
      <ComponentTile label="Warning / Large / Disabled">
        <Button label={label} theme="warning" isDisabled />
      </ComponentTile>

      <ComponentTile label="Warning / Small">
        <Button label={label} theme="warning" size="small" />
      </ComponentTile>
      <ComponentTile label="Warning / Small / Loading">
        <Button label={label} theme="warning" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Warning / Small / Disabled">
        <Button label={label} theme="warning" size="small" isDisabled />
      </ComponentTile>
    </ComponentGrid>
  </div>
);
ButtonSheet.args = {
  label: 'Button label',
};

export const ButtonInvertedSheet = ({ label }) => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
      backgroundColor: variableStyles.black,
      color: variableStyles.white,
    }}
  >
    <ComponentGrid countColumns={3}>
      <ComponentTile label="Dark Inverted / Large">
        <Button label={label} theme="darkInverted" />
      </ComponentTile>
      <ComponentTile label="Dark Inverted / Large / Loading">
        <Button label={label} theme="darkInverted" isLoading />
      </ComponentTile>
      <ComponentTile label="Dark Inverted / Large / Disabled">
        <Button label={label} theme="darkInverted" isDisabled />
      </ComponentTile>

      <ComponentTile label="Dark Inverted / Small">
        <Button label={label} theme="darkInverted" size="small" />
      </ComponentTile>
      <ComponentTile label="Dark Inverted / Small / Loading">
        <Button label={label} theme="darkInverted" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Dark Inverted / Small / Disabled">
        <Button label={label} theme="darkInverted" size="small" isDisabled />
      </ComponentTile>

      <ComponentTile label="Light Inverted / Large">
        <Button label={label} theme="lightInverted" />
      </ComponentTile>
      <ComponentTile label="Light Inverted / Large / Loading">
        <Button label={label} theme="lightInverted" isLoading />
      </ComponentTile>
      <ComponentTile label="Light Inverted / Large / Disabled">
        <Button label={label} theme="lightInverted" isDisabled />
      </ComponentTile>

      <ComponentTile label="Light Inverted / Small">
        <Button label={label} theme="lightInverted" size="small" />
      </ComponentTile>
      <ComponentTile label="Light Inverted / Small / Loading">
        <Button label={label} theme="lightInverted" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Light Inverted / Small / Disabled">
        <Button label={label} theme="lightInverted" size="small" isDisabled />
      </ComponentTile>

      <ComponentTile label="Grey Inverted / Large">
        <Button label={label} theme="greyInverted" />
      </ComponentTile>
      <ComponentTile label="Grey Inverted / Large / Loading">
        <Button label={label} theme="greyInverted" isLoading />
      </ComponentTile>
      <ComponentTile label="Grey Inverted / Large / Disabled">
        <Button label={label} theme="greyInverted" isDisabled />
      </ComponentTile>

      <ComponentTile label="Grey Inverted / Small">
        <Button label={label} theme="greyInverted" size="small" />
      </ComponentTile>
      <ComponentTile label="Grey Inverted / Small / Loading">
        <Button label={label} theme="greyInverted" size="small" isLoading />
      </ComponentTile>
      <ComponentTile label="Grey Inverted / Small / Disabled">
        <Button label={label} theme="greyInverted" size="small" isDisabled />
      </ComponentTile>

      <ComponentTile label="Transparent Inverted / Large">
        <Button label={label} theme="transparentInverted" />
      </ComponentTile>
      <ComponentTile label="Transparent Inverted / Large / Loading">
        <Button label={label} theme="transparentInverted" isLoading />
      </ComponentTile>
      <ComponentTile label="Transparent Inverted / Large / Disabled">
        <Button label={label} theme="transparentInverted" isDisabled />
      </ComponentTile>

      <ComponentTile label="Transparent Inverted / Small">
        <Button label={label} theme="transparentInverted" size="small" />
      </ComponentTile>
      <ComponentTile label="Transparent Inverted / Small / Loading">
        <Button
          label={label}
          theme="transparentInverted"
          size="small"
          isLoading
        />
      </ComponentTile>
      <ComponentTile label="Transparent Inverted / Small / Disabled">
        <Button
          label={label}
          theme="transparentInverted"
          size="small"
          isDisabled
        />
      </ComponentTile>
    </ComponentGrid>
  </div>
);
ButtonInvertedSheet.args = {
  label: 'Button label',
};

export default meta;
