import Typography from '@components/Typography';
import { formatDay, formatMonth, formatWeekday, isToday } from '@utils/date';

import styles from './TimeSlotDateView.module.css';

const TimeSlotDateView = ({ date }: { date: string }) => (
  <div className={styles.date}>
    {isToday(date) ? (
      <Typography variant="captionStrong" className={styles.month}>
        Today
      </Typography>
    ) : (
      <Typography variant="captionStrong" className={styles.month}>
        {formatMonth(date)}
      </Typography>
    )}
    <Typography variant="bodyBase">{formatWeekday(date)}</Typography>
    <Typography variant="h2">{formatDay(date)}</Typography>
  </div>
);

export default TimeSlotDateView;
