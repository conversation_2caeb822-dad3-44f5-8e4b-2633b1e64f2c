import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta } from '@storybook/react';
import typographyStyles from '@styles/typography.module.css';

import PageLayout from './PageLayout';
import { PageLayoutProps } from './PageLayout.types';

/**
 *
 * <br/>

## Overview

The Page Layout component provides a flexible structure for organizing and presenting content within a web page.
It offers options for customizing the background color, handling overflow content, and adding transition effects. 
To construct the entire page layout from an integration perspective, 
developers need to provide props as shown in the Storybook and as described in the props documentation.


## Usage

Import the component into your React application:

```jsx
import { PageLayout } from '@peddleon/ped-ux-react-library';```
 

 */

const meta: Meta<typeof PageLayout> = {
  component: PageLayout,
  title: 'Layouts/PageLayout',
  tags: ['autodocs'],
  parameters: {
    docs: {
      toc: true,
      page: () => (
        <>
          {' '}
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
};

export const PageLayoutStory = ({
  backgroundColor,
  isOverflowHidden,
  hasBackgroundTransition,
  sidebar,
  footer,
  hasLeftRightPadding,
}: PageLayoutProps) => (
  <PageLayout
    backgroundColor={backgroundColor}
    isOverflowHidden={isOverflowHidden}
    hasBackgroundTransition={hasBackgroundTransition}
    sidebar={sidebar}
    footer={footer}
    hasLeftRightPadding={hasLeftRightPadding}
  >
    <div
      style={{
        padding: 'var(--fifteenSpace)',
        marginTop: 'var(--navigationHeight)',
        backgroundColor: 'white',
        borderTopRightRadius: '16px',
        borderTopLeftRadius: '16px',
        height: '100vh',
        width: '100%',
      }}
    >
      <h1 className={typographyStyles.h1}>Main content</h1>
    </div>
  </PageLayout>
);

PageLayoutStory.args = {
  backgroundColor: 'black',
  isOverflowHidden: false,
  hasBackgroundTransition: false,
  sidebar: null,
  footer: null,
};

PageLayoutStory.argTypes = {
  backgroundColor: {
    type: 'string',
  },
  isOverflowHidden: {
    type: 'boolean',
  },
  hasBackgroundTransition: {
    type: 'boolean',
  },
  sidebar: {
    type: 'object',
  },
  footer: {
    type: 'object',
  },
  hasLeftRightPadding: {
    type: 'boolean',
  },
};
export default meta;
