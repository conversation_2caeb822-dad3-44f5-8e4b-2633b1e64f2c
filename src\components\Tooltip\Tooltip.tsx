import React from 'react';
import TooltipRC from 'rc-tooltip';

import styles from './Tooltip.module.css';
import { TooltipProps } from './Tooltip.types';

import 'rc-tooltip/assets/bootstrap.css';

const Tooltip: React.FC<TooltipProps> = ({
  text,
  children,
  placement = 'top',
}) => {
  const handleVisibleChange = (visible: boolean) => {
    setTimeout(() => {
      if (visible) {
        document.body.classList.add('tooltip-visible');
      } else {
        document.body.classList.remove('tooltip-visible');
      }
    }, 300);
  };

  return (
    <TooltipRC
      destroyTooltipOnHide
      motion={{
        motionName: 'rc-tooltip-zoom-custom',
        motionAppear: true,
      }}
      onVisibleChange={handleVisibleChange}
      placement={placement}
      overlay={<p className={styles.tooltip}>{text}</p>}
      showArrow={false}
      overlayClassName="ux-tooltip"
    >
      <div className={styles.tooltipWrapper}> {children}</div>
    </TooltipRC>
  );
};

export default Tooltip;
