@value variables: "../../styles/variables.module.css";
@value ease-out-expo, swingTo, smallWidth, twoSpace, aboveZIndex from variables;

.dialogModal {
  pointer-events: all;

  &.isMobileCentered {
    @media (max-width: smallWidth) {
      width: 90vw;
    }
  }
}

.dialogModal:not(.isMobileCentered) {
  @media (max-width: smallWidth) {
    display: flex;
    flex-flow: column;
    justify-content: flex-end;
    align-items: flex-end;
    width: 100%;
    height: 100%;
  }
  & .dialogWrapper {
    @media (max-width: smallWidth) {
      position: relative;
      width: 100vw;
      max-height: var(--baseDialogModalMaxHeightMobile);
      background-color: white;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
      overflow: auto;
      z-index: aboveZIndex;
    }
  }
}

.modal {
  &.isAnimated {
    & .dialogWrapper {
      transform: scale(0.8);
      opacity: 0;
      @media (max-width: smallWidth) {
        transform: translateY(50%);
        /* pads this so we don't see a gap on mobile when the panel swings in */
        padding-bottom: var(--fourSpace);
      }
    }
    & .overlay {
      opacity: 0;
    }
  }

  &.isEntering {
    & .dialogWrapper {
      transition:
        opacity 0.3s ease-out-expo,
        transform 0.5s swingTo;
      opacity: 1;
      transform: scale(1);
      @media (max-width: smallWidth) {
        transform: translateY(var(--fourSpace));
      }
    }
    & .overlay {
      transition: opacity 0.5s ease-out-expo;
      opacity: 1;
    }
  }

  &.isExiting {
    & .dialogWrapper {
      transition:
        opacity 0.3s ease-out-expo,
        transform 0.3s ease-out-expo;
      opacity: 0;
      transform: scale(0.8);
      @media (max-width: smallWidth) {
        transform: translateY(50%);
      }
    }
    & .overlay {
      transition: opacity 0.5s ease-out-expo;
      opacity: 0;
    }
  }
}
