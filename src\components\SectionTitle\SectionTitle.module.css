@value variables: "../../styles/variables.module.css";
@value smallWidth, oneSpace, twoSpace, fourSpace from variables;

.header {
  display: flex;
  align-items: center;
  margin-bottom: twoSpace;
  overflow-x: auto;
  white-space: nowrap;
  max-width: 100%;
}

.headingWrapper {
  min-width: max-content;
  padding-right: oneSpace;
  flex-grow: 1;
}

.icon {
  margin-right: fourSpace;
  display: flex;

  @media (max-width: smallWidth) {
    margin-right: twoSpace;
  }
}

.actions {
  margin-left: auto;
  display: flex;
  align-items: center;

  @media (max-width: smallWidth) {
    margin-left: oneSpace;
  }
}

.actions > div > *:not(:last-child) {
  margin-right: oneSpace;
}

.actionsLeft {
  margin-left: unset;
  margin-right: auto;
}
