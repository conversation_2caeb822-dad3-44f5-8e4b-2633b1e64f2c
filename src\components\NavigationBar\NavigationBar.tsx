import React, {
  ReactNode,
  SyntheticEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
// import { ChevronDownIcon, MenuIcon, XIcon } from '@peddleon/ped-ux-react-icons';
import { ButtonProps, ButtonTheme } from '@components/Button/Button.types';
import Logo from '@components/Logo';
import useClickOutside from '@hooks/useClickOutside';
import useMediaQueryState from '@hooks/useMediaQueryState';
import useWindowScroll from '@hooks/useWindowScroll';
import useWindowSize from '@hooks/useWindowSize';
import classNames from '@utils/classNames';
import variableStyles from '@styles/variables.module.css';

import { Button, Dropdown } from '..';
import LinkComponent from './LinkComponent';
import styles from './NavigationBar.module.css';
import NavigationMenu from './NavigationMenu';

/**
 * Represents an item in the navigation bar.
 */
interface NavItem {
  /**
   * The label of the navigation item.
   */
  label: string;
  /**
   * The optional icon to display alongside the label.
   */
  icon?: ReactNode;
  /**
   * Indicates whether the item is currently active.
   */
  isActive?: boolean;
  /**
   * The link associated with the item.
   */
  link?: string;
  /**
   * Sub-items for dropdown menus.
   */
  listItems?: NavItem[];
  /**
   * The theme of the button.
   */
  theme?: ButtonTheme;
  /**
   * The unique identifier of the item.
   */
  id?: string;
}

interface NavigationButton extends ButtonProps {
  isActive?: boolean;
}

/**
 * Represents the props for the NavigationBar component.
 */
export interface NavigationBarProps {
  /**
   * Array of objects representing left navigation items.
   */
  leftNavItems?: NavItem[];
  /**
   * Array of objects representing right navigation items.
   */
  rightNavItems?: NavItem[];
  /**
   * The URL for the logo displayed in the navigation bar.
   */
  logoUrl: string;
  /**
   * Indicates whether the navigation bar should stick to the top of the viewport when scrolled.
   */
  isStickOnTop?: boolean;
  /**
   * An array of props for mobile navigation buttons.
   */
  mobileNavButton: NavigationButton[];
  /**
   * The ReactNode representing the logo component.
   */
  logo?: ReactNode;
  /**
   * Additional buttons to be displayed in the mobile navigation menu.
   */
  mobileMenuButtons?: ReactNode;
  /**
   * Additional CSS classes for styling the navigation bar and its wrapper.
   */
  extraStyles?: {
    nav?: string;
    wrapper?: string;
  };
  /**
   * Function called when a navigation item is clicked.
   */
  onNavigationClick: (
    event: SyntheticEvent<Element, Event>,
    id?: string | number,
    item?: { label: string; id?: string; link?: string },
  ) => void;
  /**
   * Function called when the height of the navigation bar changes.
   */
  onNavigationHeightChange?: ({ height }: { height: number }) => void;
  /**
   * Indicates whether the navigation bar is sticky.
   */
  isNavigationSticky?: boolean;
  /**
   * Background color of the navigation bar.
   */
  backgroundColor?: string;
  /**
   * Indicates whether the admin panel is active.
   */
  isAdminPanelActive?: boolean;
  /**
   * Theme for the active navigation item.
   */
  activeItemTheme?: ButtonTheme;
  /**
   * renderMobileMenu
   */
  renderMobileMenu?: () => ReactNode;
  /**
   * hasCustomMenuMobile: show the menu for the phone
   */
  hasCustomMenuMobile?: boolean;
  /**
   * To shift mobile / tablet navigation bar to right
   */
  isNavigationRight?: boolean;
  /**
   * on clicking of the outside it will call
   */
  onClickOutSideNavigation?: () => void;
  /**
   * To show border-radius with mobile in mount
   */
  hasFlatLayout?: boolean;
}

const NavigationBar: React.FC<NavigationBarProps> = ({
  leftNavItems = [],
  rightNavItems = [],
  logoUrl,
  isStickOnTop,
  mobileNavButton,
  logo,
  mobileMenuButtons,
  extraStyles = {
    nav: '',
    wrapper: '',
  },
  onNavigationClick = () => {},
  onNavigationHeightChange = () => {},
  backgroundColor = variableStyles.black,
  isAdminPanelActive,
  activeItemTheme = 'darkInverted',
  hasCustomMenuMobile = false,
  renderMobileMenu = () => null,
  isNavigationRight = false,
  onClickOutSideNavigation = () => {},
  hasFlatLayout = false,
}) => {
  const [isNavigationMenuOpen, setIsNavigationMenuOpen] = useState(false);
  const { windowScrollTop, setScrollTop } = useWindowScroll();
  const previousScrollTopRef = useRef(null);
  const menuRef = useRef<HTMLDivElement>(null);
  const isMobile = useMediaQueryState({ query: '(max-width: 480px)' });
  const isDesktop = useMediaQueryState({
    query: `(min-width: ${variableStyles.mediumWidth})`,
  });

  const { width } = useWindowSize();

  // resets scrollTop because iOS doesn't know how to handle scroll position
  // reset when switching to/from overflow: hidden
  useEffect(() => {
    if ((isNavigationMenuOpen || isNavigationMenuOpen) && !isDesktop) {
      previousScrollTopRef.current = windowScrollTop;
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'initial';
      if (previousScrollTopRef.current) {
        setScrollTop(previousScrollTopRef.current);
        previousScrollTopRef.current = null;
      }
    }

    return () => {
      document.body.style.overflow = 'initial';
    };
  }, [
    isNavigationMenuOpen,
    isDesktop,
    windowScrollTop,
    previousScrollTopRef,
    setScrollTop,
    setIsNavigationMenuOpen,
  ]);

  useClickOutside(menuRef, () => {
    setIsNavigationMenuOpen(false);
    onClickOutSideNavigation();
  });

  const toggleMenu = useCallback(() => {
    setIsNavigationMenuOpen(prevState => !prevState);
  }, []);

  useEffect(() => {
    setIsNavigationMenuOpen(hasCustomMenuMobile);
  }, [hasCustomMenuMobile]);

  const handleClick = (
    event: SyntheticEvent<Element, Event>,
    id?: string | number,
    item?: { label: string; id?: string; link?: string },
  ) => {
    setTimeout(() => {
      setIsNavigationMenuOpen(false);
    }, 200);

    const target = event.target as HTMLElement;
    if (target && target instanceof HTMLAnchorElement && target.href) {
      // Now you can safely ignore below code, as it'll redirect to the page. To track we're just
      // adding callback to parent
      onNavigationClick(event, id, item);
      return;
    }

    onNavigationClick(event, id, item);
  };

  useEffect(() => {
    onNavigationHeightChange({
      height: menuRef.current.clientHeight,
    });
  }, [onNavigationHeightChange, width]);

  const logoColor =
    isNavigationMenuOpen && isMobile
      ? variableStyles.black
      : variableStyles.white;

  return (
    <>
      <div
        className={classNames(
          styles.overlay,
          isNavigationMenuOpen && styles.isMenuOpen,
        )}
      />
      <nav
        className={classNames(
          styles.nav,
          isStickOnTop && styles.isSticky,
          isNavigationMenuOpen && styles.isMenuOpen,
          extraStyles.nav,
          isAdminPanelActive && isStickOnTop && styles.isAdminPanelActive,
          hasFlatLayout && styles.hasFlatLayout,
        )}
        ref={menuRef}
        style={{
          backgroundColor,
        }}
      >
        <div
          className={classNames(
            styles.barWrapper,
            isNavigationMenuOpen && styles.isMenuOpen,
            extraStyles.wrapper,
          )}
        >
          <div className={styles.navBarLeft}>
            {!!leftNavItems?.length && (
              <Button
                size="small"
                onClick={toggleMenu}
                theme={
                  isNavigationMenuOpen && isMobile
                    ? 'transparent'
                    : 'transparentInverted'
                }
                className={styles.leftNavMenu}
                id="toggle"
              >
                {/* {isNavigationMenuOpen && !hasCustomMenuMobile ? (
                  <XIcon height="20px" width="20px" />
                ) : (
                  <MenuIcon height="20px" width="20px" />
                )} */}
              </Button>
            )}
            <a href={logoUrl} className={styles.logo} aria-label="test">
              {logo || <Logo color={logoColor} />}
            </a>
            <div className={styles.leftNavigation}>
              {leftNavItems.map((item, index) => {
                const { id, theme, listItems, isActive, ...itemProps } = item;
                const dropdownID = `${id || item.label || index}`;

                return (
                  <div className={styles.buttonContainer} key={item.label}>
                    <Dropdown
                      useNativeDropdown
                      item={{ lists: listItems }}
                      id={dropdownID}
                      key={dropdownID}
                      extraStyles={{ menuListItem: styles.menuListItem }}
                      data-testid={dropdownID}
                      onClick={handleClick}
                    >
                      <LinkComponent key={dropdownID} {...itemProps}>
                        <Button
                          label={item.label}
                          id={dropdownID}
                          className={styles.leftNavigationBtn}
                          // right={
                          //   listItems && (
                          //     <ChevronDownIcon height="1.25em" width="1.25em" />
                          //   )
                          // }
                          onClick={event => handleClick(event, item.id, item)}
                          theme={isActive ? activeItemTheme : theme}
                          {...itemProps}
                        >
                          {item.icon}
                        </Button>
                      </LinkComponent>
                    </Dropdown>
                  </div>
                );
              })}
            </div>
          </div>
          <div className={styles.navBarRight}>
            {rightNavItems.map((item, index) => {
              const { id, theme, listItems, isActive, ...itemProps } = item;

              const dropdownID = `${id || item.label || index}`;

              return (
                <div className={styles.buttonContainer} key={dropdownID}>
                  <Dropdown
                    useNativeDropdown
                    item={{ lists: listItems }}
                    id={dropdownID}
                    key={dropdownID}
                    extraStyles={{ menuListItem: styles.menuListItem }}
                    onClick={handleClick}
                  >
                    <LinkComponent key={dropdownID} {...itemProps}>
                      <Button
                        label={item.label}
                        className={styles.leftNavigationBtn}
                        right={item.icon}
                        onClick={event => handleClick(event, item.id, item)}
                        id={dropdownID}
                        theme={isActive ? activeItemTheme : theme}
                        {...itemProps}
                      >
                        {item.icon}
                      </Button>
                    </LinkComponent>
                  </Dropdown>
                </div>
              );
            })}
          </div>
          <div
            className={classNames(styles.navBarRight, styles.mobileNavRight)}
          >
            {mobileNavButton.map(item => {
              const btnTheme =
                isNavigationMenuOpen && isMobile
                  ? (item?.theme?.replace('Inverted', '') as ButtonTheme)
                  : item?.theme;

              const { isActive, ...itemProps } = item;

              return (
                <LinkComponent
                  {...item}
                  key={`mobile-${item.id}`}
                  style={{
                    display: 'inlineBlock',
                  }}
                >
                  <Button
                    id={item.id || item.label}
                    size="small"
                    className={classNames(styles.navbarIcon)}
                    left={item.icon}
                    label={item.label}
                    onClick={event => handleClick(event, item.id)}
                    {...itemProps}
                    theme={isActive ? activeItemTheme : btnTheme}
                  >
                    {item.icon}
                  </Button>
                </LinkComponent>
              );
            })}
          </div>
        </div>

        {isNavigationMenuOpen && (
          <NavigationMenu
            mobileMenuButtons={mobileMenuButtons}
            primaryLinks={leftNavItems}
            onClick={handleClick}
            hasCustomMenuMobile={hasCustomMenuMobile}
            renderMobileMenu={renderMobileMenu}
            isNavigationRight={!isMobile && isNavigationRight}
          />
        )}
      </nav>
    </>
  );
};

export default NavigationBar;
