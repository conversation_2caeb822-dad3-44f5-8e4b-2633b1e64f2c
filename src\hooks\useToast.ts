import { useCallback, useEffect, useRef } from 'react';
import { useToastContext } from '@contexts/ToastContext';
import { ToastConfigType } from '@libtypes/common';

export default function useToast() {
  const { setToastConfig, toastConfig, timer } = useToastContext();
  const timeoutRef = useRef(null);
  const lastToast = useRef(null);

  useEffect(() => {
    if (toastConfig.isActive) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = setTimeout(() => {
        setToastConfig(prev => ({
          ...prev,
          isActive: false,
        }));
        lastToast.current = null;
      }, timer);
    } else {
      lastToast.current = null;
    }
    return () => {
      clearTimeout(timeoutRef.current);
    };
  }, [setToastConfig, timer, toastConfig.isActive]);

  const onToastConfigUpdate = useCallback(
    (toastProps: ToastConfigType) => {
      const newToast = `${toastProps.body}-${toastProps.theme}`;
      if (newToast !== lastToast.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = setTimeout(() => {
          setToastConfig(prev => ({
            ...prev,
            isActive: false,
          }));
          lastToast.current = null;
        }, timer);

        setToastConfig(prev => ({
          ...prev,
          ...toastProps,
          key: Date.now(),
        }));
        lastToast.current = newToast;
      }
    },
    [setToastConfig, timer],
  );

  return (toastProps: ToastConfigType) => {
    onToastConfigUpdate(toastProps);
  };
}
