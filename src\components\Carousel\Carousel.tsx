// /* eslint-disable react/jsx-max-depth */
// import { useContext, useEffect, useState } from 'react';
// import {
//   ButtonBack,
//   ButtonNext,
//   CarouselContext,
//   CarouselProvider,
//   DotGroup,
//   Slide,
//   Slider,
// } from 'pure-react-carousel';
// import { ArrowLeftIcon, ArrowRightIcon } from '@peddleon/ped-ux-react-icons';
// import { CAROUSEL_SLIDER_DATA_TEST_ID } from '@constants/dataTestId';

// import styles from './Carousel.module.css';
// import { CarouselPropTypes } from './Carousel.types';

// export const MyComponentUsingContext = ({ onSlideChange }) => {
//   const carouselContext = useContext(CarouselContext);
//   const [currentSlide, setCurrentSlide] = useState(
//     carouselContext.state.currentSlide,
//   );
//   useEffect(() => {
//     function onChange() {
//       setCurrentSlide(carouselContext.state.currentSlide);
//     }
//     carouselContext.subscribe(onChange);
//     return () => carouselContext.unsubscribe(onChange);
//   }, [carouselContext]);

//   useEffect(() => {
//     onSlideChange(currentSlide);
//   }, [currentSlide, onSlideChange]);

//   return null;
// };

// /**
//  * The Carousel component provides a slideshow carousel with customizable options.
//  * It supports features such as rendering dynamic slides, controlling visible slides,
//  * autoplay, navigation buttons, and more.
//  *
//  * @template T - The type of items in the carousel.
//  *
//  * @param {CarouselPropTypes<T>} props - The props for the Carousel component.
//  * @param {T[]} props.items - The array of items to render as slides in the carousel.
//  * @param {(item: T) => React.ReactNode} props.renderSlide - The function to render each slide.
//  * @param {number} [props.visibleSlides=1] - Optional. The number of visible slides at a time.
//  * @param {number} [props.currentSlide=1] - Optional. The index of the currently active slide.
//  * @param {number} [props.step=1] - Optional. The number of slides to move when navigating.
//  * @param {boolean} [props.isIntrinsicHeight=true] - Optional. Specifies whether slides have intrinsic height.
//  * @param {boolean} [props.infinite=false] - Optional. Specifies whether the carousel should loop infinitely.
//  * @param {boolean} [props.autoScroll=false] - Optional. Specifies whether the carousel should autoplay.
//  * @param {'forward' | 'backward'} [props.playDirection='forward'] - Optional. The direction of autoplay.
//  * @param {number} [props.interval=1000] - Optional. The interval between autoplay transitions.
//  * @param {boolean} [props.hasDots=false] - Optional. Specifies whether to display navigation dots.
//  *
//  * @returns {JSX.Element} - The rendered Carousel component.
//  */
// const Carousel = <T,>({
//   items,
//   renderSlide,
//   visibleSlides = 1,
//   step = 1,
//   currentSlide = 1,
//   isIntrinsicHeight = true,
//   infinite = false,
//   interval = 1000,
//   autoScroll = false,
//   playDirection = 'forward',
//   hasDots = false,
//   onActiveIndexChange = () => {},
// }: CarouselPropTypes<T>) => {
//   const [activeSlide, setActiveSlide] = useState(currentSlide);

//   const handleActiveSlide = index => {
//     setActiveSlide(index);
//     onActiveIndexChange(index);
//   };

//   return (
//     <div className={styles.carouselWrapper}>
//       <CarouselProvider
//         totalSlides={items.length}
//         visibleSlides={visibleSlides}
//         step={step}
//         currentSlide={currentSlide}
//         isIntrinsicHeight={isIntrinsicHeight}
//         naturalSlideHeight={100}
//         naturalSlideWidth={100}
//         infinite={infinite}
//         isPlaying={autoScroll}
//         playDirection={playDirection}
//         interval={interval}
//       >
//         <div className={styles.container}>
//           <ButtonBack className={styles.buttonBack}>
//             <div className={styles.back}>
//               <ArrowLeftIcon height="1em" width="1em" />
//             </div>
//           </ButtonBack>

//           <Slider
//             className={styles.slider}
//             data-testid={CAROUSEL_SLIDER_DATA_TEST_ID}
//           >
//             {items.map((item, index) => (
//               <Slide
//                 // eslint-disable-next-line react/no-array-index-key
//                 key={`slide_${index}`}
//                 index={index}
//                 className={styles.slide}
//               >
//                 {renderSlide(item, index)}
//               </Slide>
//             ))}
//           </Slider>

//           <ButtonNext className={styles.buttonNext}>
//             <div className={styles.next}>
//               <ArrowRightIcon width="1em" height="1em" />
//             </div>
//           </ButtonNext>
//         </div>
//         {hasDots && <DotGroup className={styles.dotGroup} />}
//         <MyComponentUsingContext onSlideChange={handleActiveSlide} />
//       </CarouselProvider>
//       <div className={styles.footer}>{items[activeSlide]?.label}</div>
//     </div>
//   );
// };

// export default Carousel;
