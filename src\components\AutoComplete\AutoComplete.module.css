@value variables: "../../styles/variables.module.css";
@value topZIndex, h6FontSize, captionFontSize, halfSpace, h6FontSize, eightSpace, oneSpace, twoSpace, ease-out-expo, danger, white, primary, success, black, black5, black10, black35, black50 from variables;

.combobox {
  &:hover {
    & .container {
      border-color: black35;
    }
  }
}

.disabled {
  pointer-events: none;

  & .container {
    pointer-events: none;
    background-color: black5;
    color: black35;
  }

  & .input {
    color: black50;
  }
}

.borderless {
  & .container {
    border: none;
  }
}

.fixedListWrapper {
  min-width: unset !important;
  position: fixed !important;
}

.fixedListWrapper .group {
  margin-top: halfSpace;

  @media (max-width: smallWidth) {
    margin-top: halfSpace;

    &:first-child {
      margin-top: oneSpace;
    }
  }
}

.combobox.errored {
  & .container {
    border-color: danger;
  }

  &.focused {
    & .container {
      border-color: danger;
    }

    & .label {
      color: black;
    }

    & .leftWrapper {
      color: black;
    }

    & .rightWrapper {
      color: black;
    }
  }
}

.error {
  color: danger;
}

.combobox.focused {
  & .container {
    border-color: primary;
  }

  & .label {
    color: primary;
  }

  & .leftWrapper {
    color: primary;
  }

  & .rightWrapper {
    color: black;
  }
}

.focused,
.filled {
  & .label {
    top: 0;
    transform: translateY(oneSpace) scale(0.75);
    line-height: 20px;
  }

  & .input::placeholder {
    opacity: 1;
  }
}

.container {
  height: eightSpace;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
  background: white;
  border: solid 1px;
  border-color: black10;
  border-radius: oneSpace;
  transition: border-color 0.5s ease-out-expo;
  padding-left: twoSpace;
  padding-right: twoSpace;
}

.inputWrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

.wrapper {
  line-height: 0;
}

.leftWrapper {
  composes: wrapper;
  margin-right: oneSpace;
  color: black50;
  transition: color 0.5s ease-out-expo;
}

.rightWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  display: flex;
  flex-flow: row;
  align-items: center;
  color: black50;

  & > * {
    line-height: 0;
  }
}

.errorIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: danger;
}

.successIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: success;
}

.fetchingIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
}

.label {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  font-size: h6FontSize;
  line-height: 20px;
  letter-spacing: 0.01em;
  color: black50;
  transform-origin: left center;
  will-change: top, transform, color;
  transition:
    color 0.5s ease-out-expo,
    top 0.5s ease-out-expo,
    transform 0.5s ease-out-expo;
  pointer-events: none;
  user-select: none;
}

.input {
  width: 100%;
  height: 100%;
  font-size: h6FontSize;
  line-height: 1.5;
  letter-spacing: 0.01em;
  color: black;
  align-self: flex-end;

  &::placeholder {
    opacity: 0;
    color: black50;
    font-weight: 400;
    letter-spacing: 0.01em;
    transition: opacity 0.5s ease-out-expo;
  }

  &.hasLabel {
    padding-top: twoSpace;
  }
}

.selected {
  position: absolute;
  bottom: 12px;
  left: 0;
  font-size: h6FontSize;
  line-height: 1.5;
  letter-spacing: 0.01em;
  color: black;
}

.footer {
  text-align: left;
  padding: halfSpace twoSpace 0 0;
  font-size: captionFontSize;
  line-height: 20px;
}

.active .menuWrapper {
  display: block;
}

.menuWrapper {
  display: none;
  line-height: 0;
  position: relative;
}

.menuSelectInput {
  z-index: topZIndex !important;
}
