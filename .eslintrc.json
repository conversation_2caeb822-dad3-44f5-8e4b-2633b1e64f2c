{
  "env": {
    "browser": true, // Specifies that the code will run in a browser environment
    "es2021": true, // Specifies ES2021 as the target ECMAScript version
    "jest/globals": true // Specifies Jest global variables
  },
  "extends": [
    "eslint:recommended", // Extends eslint recommended rules
    "plugin:@typescript-eslint/recommended", // Extends TypeScript ESLint recommended rules
    "plugin:react/recommended", // Extends React ESLint recommended rules
    "plugin:jest/recommended", // Extends Jest ESLint recommended rules
    "plugin:import/errors", // Extends import plugin error rules
    "plugin:import/warnings", // Extends import plugin warning rules
    "plugin:jsx-a11y/recommended", // Extends JSX accessibility ESLint recommended rules
    "plugin:react-hooks/recommended", // Extends React Hooks ESLint recommended rules
    "airbnb", // Extends Airbnb JavaScript style guide
    "prettier", // Extends Prettier rules
    "plugin:storybook/recommended" // Extends Storybook ESLint recommended rules
  ],
  "parser": "@typescript-eslint/parser", // Specifies TypeScript parser
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true // Enables JSX parsing
    },
    "ecmaVersion": "latest", // Specifies the latest ECMAScript version
    "sourceType": "module" // Specifies module code
  },
  "globals": {
    "google": "readonly"
  },
  "settings": {
    "react": {
      "version": "detect" // Automatically detects React version
    },
    "import/resolver": {
      "alias": {
        // Specifies module aliases for import resolution
        "map": [
          ["@components", "./src/components"],
          ["@libtypes", "./src/types"],
          ["@hooks", "./src/hooks"],
          ["@constants", "./src/constants"],
          ["@utils", "./src/utils"],
          ["@styles", "./src/styles"],
          ["@stories", "./src/stories"],
          ["@contexts", "./src/contexts"],
          ["rc-tooltip", "./node_modules/rc-tooltip/lib/index"],
          ["react-day-picker", "./node_modules/react-day-picker/src/index"]
        ],
        "extensions": [".ts", ".tsx"] // Specifies file extensions for import resolution
      }
    },
    "typescript": {} // Loads tsconfig.json to ESLint
  },
  "plugins": [
    "@typescript-eslint",
    "react",
    "jest",
    "simple-import-sort",
    "css-modules"
  ], // Specifies ESLint plugins
  "rules": {
    "react/prefer-stateless-function": "error", // Enforces preferring stateless functional components in React
    "react/jsx-filename-extension": [
      // Enforces JSX file extension
      "error",
      {
        "extensions": [".tsx"]
      }
    ],
    "react/jsx-pascal-case": "error", // Enforces PascalCase for JSX components
    "react/jsx-max-depth": ["error", { "max": 5 }], // Enforces maximum JSX nesting depth
    "react/jsx-no-useless-fragment": "error", // Disallows useless JSX fragments
    "react/no-typos": "error", // Prevents common typos in React component names
    "react/no-array-index-key": "error", // Enforces providing unique keys for array elements
    "react/prop-types": "off", // Disables prop types validation
    "react/require-default-props": "off", // Disables requiring default props
    "react/jsx-uses-react": "off", // Disables requiring React import in JSX files
    "react/react-in-jsx-scope": "off", // Disables requiring React import in JSX files
    "react/jsx-props-no-spreading": "off", // Disables disallowing spreading props
    "no-restricted-exports": "off", // Disables restricting exports
    "import/no-extraneous-dependencies": ["error", { "devDependencies": true }], // Allows dev dependencies for imports
    "@typescript-eslint/naming-convention": [
      // Specifies naming conventions for TypeScript
      "error",
      {
        "selector": "default",
        "format": ["camelCase", "PascalCase"],
        "leadingUnderscore": "allow"
      },
      {
        "selector": "variable",
        "format": ["camelCase", "PascalCase", "UPPER_CASE"],
        "leadingUnderscore": "allow"
      },
      {
        "selector": "parameter",
        "format": ["camelCase"],
        "leadingUnderscore": "allow"
      },
      {
        "selector": "property",
        "format": null,
        "leadingUnderscore": "allow"
      },
      {
        "selector": "typeLike",
        "format": ["PascalCase"]
      }
    ],
    "simple-import-sort/imports": ["error"],
    "react/function-component-definition": [
      // Specifies function component definition style
      "error",
      {
        "namedComponents": "arrow-function",
        "unnamedComponents": "arrow-function"
      }
    ],
    "no-underscore-dangle": 0, // Allows underscores in identifiers
    "import/extensions": [
      // Specifies import extensions
      0,
      "never",
      {
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never"
      }
    ],

    "jest/expect-expect": [
      "error",
      {
        "assertFunctionNames": ["expect", "render"] // There should be 'expect' or 'render' methods in each test case
      }
    ],
    "no-console": "error"
  },
  "overrides": [
    {
      "files": ["**/*.{ts,tsx,js,jsx}"],
      "rules": {
        "simple-import-sort/imports": [
          "error",
          {
            "groups": [
              // Group third-party imports starting with @ except specific patterns
              [
                "^react",
                "^next",
                "^[a-z]",
                "^@(?!components|contexts|libtypes|hooks|constants|utils|stories|styles)",
                "^@components",
                "^@contexts",
                "^@libtypes",
                "^@constants",
                "^@hooks",
                "^@utils",
                "^@stories",
                "^@styles"
              ],
              // Imports starting with `../` , `./`
              [
                "^\\.\\.(?!/?$)",
                "^\\.\\./?$",
                "^\\./(?=.*/)(?!/?$)",
                "^\\.(?!/?$)",
                "^\\./?$"
              ]
            ]
          }
        ]
      }
    },
    {
      "files": ["tests/**/*"], // Overrides settings for files under tests directory
      "env": {
        "jest": true // Specifies Jest environment for test files
      }
    }
  ]
}
