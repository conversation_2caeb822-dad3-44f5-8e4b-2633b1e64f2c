@value variables: "../../styles/variables.module.css";
@value halfSpace, oneSpace, twoSpace, black, black5, threeSpace, fourSpace, fiveSpace, black10, black35, black50, black70 from variables;
@value danger, white, primary, success from variables;
@value ease-out-expo from variables;

.textareaWrapper {
  & .container {
    position: relative;
    height: auto;
    align-items: flex-start;
    padding-top: twoSpace;
    padding-right: oneSpace;
    padding-bottom: oneSpace;
  }
  &:hover {
    & .container {
      border-color: black35;
    }
  }

  & .label {
    top: 0;
    transform: translateY(0);
    line-height: 24px;
  }
}

.disabled {
  pointer-events: none;

  & .container {
    pointer-events: none;
    background-color: black5;
    color: black35;
  }

  & .textarea {
    color: black50;
  }
}

.borderless {
  & .container {
    border: none;
  }
}

.square {
  & .container {
    border-radius: 0;
  }
}

.textareaWrapper.errored {
  & .container {
    border-color: danger;
  }

  &:has(textarea:focus) {
    & .container {
      border-color: danger;
    }

    & .label {
      color: black;
    }

    & .leftWrapper {
      color: black;
    }

    & .rightWrapper {
      color: black;
    }
  }
}

.textareaWrapper:has(textarea:focus) {
  & .container {
    border-color: primary;
  }

  & .label {
    color: primary;
  }

  & .leftWrapper {
    color: primary;
  }

  & .rightWrapper {
    color: black;
  }
}

.textareaWrapper:has(textarea:focus),
.filled {
  & .label {
    top: 0;
    transform: translateY(-8px) scale(0.75);
  }

  & .textarea::placeholder {
    opacity: 1;
  }
}

.textareaWrapper:has(textarea:focus):not(.filled) {
  &.readOnly {
    & .label {
      top: 50%;
      transform: translateY(-50%);
      line-height: 20px;
    }
    & .textarea {
      &::placeholder {
        opacity: 0;
        color: black50;
        font-weight: 400;
        letter-spacing: 0.01em;
        transition: opacity 0.5s ease-out-expo;
      }
    }
  }
}

.container {
  height: 64px;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
  background: white;
  border: solid 1px;
  border-color: black10;
  border-radius: 8px;
  transition: border-color 0.5s ease-out-expo;
  padding-left: twoSpace;
  padding-right: twoSpace;
}

.inputWrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

.hasRight {
  padding-right: fiveSpace !important;
}

.wrapper {
  line-height: 0;
}

.leftWrapper {
  composes: wrapper;
  margin-right: oneSpace;
  color: black50;
  transition: color 0.5s ease-out-expo;
}

.rightWrapper {
  right: oneSpace;
  composes: wrapper;
  position: absolute;
  margin-right: twoSpace;
  display: flex;
  flex-flow: row;
  align-items: center;
  color: black50;

  & > * {
    line-height: 0;
  }
}

.errorIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: danger;
}

.successIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: success;
}

.fetchingIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
}

.label {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0.01em;
  color: black50;
  transform-origin: left center;
  will-change: top, transform, color;
  transition:
    color 0.5s ease-out-expo,
    top 0.5s ease-out-expo,
    transform 0.5s ease-out-expo;
  pointer-events: none;
  user-select: none;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.textarea {
  display: block;
  width: 100%;
  height: 100%;

  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.01em;
  color: black;
  align-self: flex-end;
  padding-right: oneSpace;

  &::placeholder {
    opacity: 0;
    color: black50;
    font-weight: 400;
    letter-spacing: 0.01em;
    transition: opacity 0.5s ease-out-expo;
  }

  &.hasLabel {
    margin-top: twoSpace;
  }
}

.wrapped {
  overflow-wrap: break-word;
}

.textarea[readonly] {
  cursor: pointer;
}

.footer {
  display: flex;
  flex-direction: column;
  text-align: left;
  padding: halfSpace twoSpace 0 0;
  font-size: 12px;
  line-height: 20px;
}

.error {
  color: danger;
}

.caption {
  color: black70;
}

.multiline {
}
