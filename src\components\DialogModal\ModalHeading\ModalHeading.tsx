import classNames from '@utils/classNames';

import styles from './ModalHeading.module.css';
import { ModalHeadingProps } from './ModalHeading.types';

const ModalHeading = ({
  children,
  wrapperClassName = null,
}: ModalHeadingProps) => (
  <div className={classNames(styles.modalHeadingWrapper, wrapperClassName)}>
    <h3 className={styles.heading}>{children}</h3>
  </div>
);

export default ModalHeading;
