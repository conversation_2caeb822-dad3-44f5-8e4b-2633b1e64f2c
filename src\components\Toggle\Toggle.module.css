@value variables: "../../styles/variables.module.css";
@value white, neutralDarker, primary, ease-out-expo from variables;

.toggleTrack {
  width: 36px;
  height: 16px;
  border-radius: 8px;
  position: relative;
  display: inline-block;
  border: 2px solid neutralDarker;
  background-color: white;
  cursor: pointer;
}

.toggleKnob {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: neutralDarker;
  position: absolute;
  top: -4px;
  left: -4px;
  right: 0;
  bottom: 0;
  transition: ease-out-expo 500ms;
  border: 2px solid transparent;
}

.toggleTrackOn {
  border-color: primary;
  background-color: primary;

  & .toggleKnob {
    border-color: primary;
    background-color: white;
    transform: translateX(20px);
  }
}

.toggleDisabled {
  pointer-events: none;
  opacity: 0.5;
}
