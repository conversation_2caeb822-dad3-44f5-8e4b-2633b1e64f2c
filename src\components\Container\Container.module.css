@value variables: "../../styles/variables.module.css";
@value utility: "../../styles/utility.module.css";
@value responsiveContainer, responsiveContainerWide, responsiveContainerFull, responsiveContainerNarrow from utility;
@value smallWidth from variables;

.container {
  composes: responsiveContainer;
}
.containerWrapper {
  width: 100%;
  height: 100%;
  background-color: var(--backgroundColor);
  transition: background-color 0.5s ease-out;

  @media (max-width: smallWidth) {
    background-color: var(--mobileBackgroundColor);
  }
}
.wide {
  composes: responsiveContainerWide;
}
.full {
  composes: responsiveContainerFull;
}
.narrow {
  composes: responsiveContainerNarrow;
}
