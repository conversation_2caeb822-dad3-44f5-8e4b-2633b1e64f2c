@value variables: "./variables.module.css";
@value black, white from variables;

/* normalize styling across browsers */
html {
  width: 100%;
  height: 100%;
}

html {
  box-sizing: border-box;
}

* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
figure,
ol,
ul {
  margin: 0;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6,
a {
  white-space: pre-line;
  font-family: inherit;
  color: inherit;
}

body {
  width: 100%;
  margin: 0;
  padding: 0 !important;
  font-family: 'Aventa', sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  position: relative;
}

#__next {
  width: 100%;
  height: 100%;
}

input,
textarea,
select {
  all: initial;
  box-sizing: border-box;
  font-family: 'Aventa', sans-serif;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

select::-ms-expand {
  display: none;
}

input[type='submit'],
input[type='reset'],
input[type='button'],
button {
  background: none;
  border: 0;
  color: inherit;
  font: inherit;
  line-height: normal;
  overflow: visible;
  padding: 0;
  margin: 0;
  -webkit-appearance: button; /* for input */
  -webkit-user-select: none; /* for button */
  -moz-user-select: none;
  -ms-user-select: none;
}

/* hide number input step buttons in webkit */
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* hide number input step buttons in firefox */
input[type='number'] {
  -moz-appearance: textfield;
}

/* clears the 'X' from Internet Explorer */
input[type='search']::-ms-clear,
input[type='search']::-ms-reveal {
  display: none;
  width: 0;
  height: 0;
}

/* clears the 'X' from Chrome/Safari */
input[type='search']::-webkit-search-decoration,
input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-results-button,
input[type='search']::-webkit-search-results-decoration {
  -webkit-appearance: none;
  display: none;
}

input::-moz-focus-inner,
button::-moz-focus-inner {
  border: 0;
  padding: 0;
}

button:focus,
a:focus {
  outline: none;
}

img {
  width: 100%;
  height: auto;
  border: 0;
}

img[height='1'] {
  display: none;
}

video {
  width: 100%;
}

a {
  display: inline-block;
  text-decoration: none;
  color: inherit;
}

p a,
h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a,
span a {
  letter-spacing: normal;
}

fieldset {
  border: none;
  margin: 0;
  padding: 0;
}

textarea {
  border: none;
  background-color: transparent;
  resize: none;
  outline: none;
}

strong {
  font-weight: 500;
}

.markdown-content strong {
  font-weight: 700;
}

hr {
  margin: 0;
  height: 1px;
  width: 100%;
  border: none;
}

.multi-section-wrapper .section-wrapper {
  margin: 0;
  margin-top: var(--fourSpace);
  padding: 0 var(--threeSpace);
}

.multi-section-wrapper .content {
  padding: 0;
}

.carousel__slider-tray-wrap--horizontal {
  margin: 0 -1px !important;
}

.rc-tooltip-inner {
  padding: 0 !important;
  background-color: white !important;
}

.rc-tooltip {
  opacity: var(--tooltipOpacity);
}

/* Utility Classes */
/* Spacing (Margin and Padding) */
:root {
  --g-halfSpace: 4px;
  --g-oneSpace: 8px;
  --g-twoSpace: 16px;
  --g-threeSpace: 24px;
  --g-fourSpace: 32px;
}

/* Margin */
.m-0 {
  margin: 0;
}
.mt-0 {
  margin-top: 0;
}
.mr-0 {
  margin-right: 0;
}
.mb-0 {
  margin-bottom: 0;
}
.ml-0 {
  margin-left: 0;
}
.mx-0 {
  margin-left: 0;
  margin-right: 0;
}
.my-0 {
  margin-top: 0;
  margin-bottom: 0;
}

.m-1 {
  margin: var(--g-halfSpace);
}
.mt-1 {
  margin-top: var(--g-halfSpace);
}
.mr-1 {
  margin-right: var(--g-halfSpace);
}
.mb-1 {
  margin-bottom: var(--g-halfSpace);
}
.ml-1 {
  margin-left: var(--g-halfSpace);
}
.mx-1 {
  margin-left: var(--g-halfSpace);
  margin-right: var(--g-halfSpace);
}
.my-1 {
  margin-top: var(--g-halfSpace);
  margin-bottom: var(--g-halfSpace);
}

.m-2 {
  margin: var(--g-oneSpace);
}
.mt-2 {
  margin-top: var(--g-oneSpace);
}
.mr-2 {
  margin-right: var(--g-oneSpace);
}
.mb-2 {
  margin-bottom: var(--g-oneSpace);
}
.ml-2 {
  margin-left: var(--g-oneSpace);
}
.mx-2 {
  margin-left: var(--g-oneSpace);
  margin-right: var(--g-oneSpace);
}
.my-2 {
  margin-top: var(--g-oneSpace);
  margin-bottom: var(--g-oneSpace);
}

.m-3 {
  margin: var(--g-twoSpace);
}
.mt-3 {
  margin-top: var(--g-twoSpace);
}
.mr-3 {
  margin-right: var(--g-twoSpace);
}
.mb-3 {
  margin-bottom: var(--g-twoSpace);
}
.ml-3 {
  margin-left: var(--g-twoSpace);
}
.mx-3 {
  margin-left: var(--g-twoSpace);
  margin-right: var(--g-twoSpace);
}
.my-3 {
  margin-top: var(--g-twoSpace);
  margin-bottom: var(--g-twoSpace);
}

.m-4 {
  margin: var(--g-threeSpace);
}
.mt-4 {
  margin-top: var(--g-threeSpace);
}
.mr-4 {
  margin-right: var(--g-threeSpace);
}
.mb-4 {
  margin-bottom: var(--g-threeSpace);
}
.ml-4 {
  margin-left: var(--g-threeSpace);
}
.mx-4 {
  margin-left: var(--g-threeSpace);
  margin-right: var(--g-threeSpace);
}
.my-4 {
  margin-top: var(--g-threeSpace);
  margin-bottom: var(--g-threeSpace);
}

.m-5 {
  margin: var(--g-fourSpace);
}
.mt-5 {
  margin-top: var(--g-fourSpace);
}
.mr-5 {
  margin-right: var(--g-fourSpace);
}
.mb-5 {
  margin-bottom: var(--g-fourSpace);
}
.ml-5 {
  margin-left: var(--g-fourSpace);
}
.mx-5 {
  margin-left: var(--g-fourSpace);
  margin-right: var(--g-fourSpace);
}
.my-5 {
  margin-top: var(--g-fourSpace);
  margin-bottom: var(--g-fourSpace);
}

/* Padding */
.p-0 {
  padding: 0;
}
.pt-0 {
  padding-top: 0;
}
.pr-0 {
  padding-right: 0;
}
.pb-0 {
  padding-bottom: 0;
}
.pl-0 {
  padding-left: 0;
}
.px-0 {
  padding-left: 0;
  padding-right: 0;
}
.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.p-1 {
  padding: var(--g-halfSpace);
}
.pt-1 {
  padding-top: var(--g-halfSpace);
}
.pr-1 {
  padding-right: var(--g-halfSpace);
}
.pb-1 {
  padding-bottom: var(--g-halfSpace);
}
.pl-1 {
  padding-left: var(--g-halfSpace);
}
.px-1 {
  padding-left: var(--g-halfSpace);
  padding-right: var(--g-halfSpace);
}
.py-1 {
  padding-top: var(--g-halfSpace);
  padding-bottom: var(--g-halfSpace);
}

.p-2 {
  padding: var(--g-oneSpace);
}
.pt-2 {
  padding-top: var(--g-oneSpace);
}
.pr-2 {
  padding-right: var(--g-oneSpace);
}
.pb-2 {
  padding-bottom: var(--g-oneSpace);
}
.pl-2 {
  padding-left: var(--g-oneSpace);
}
.px-2 {
  padding-left: var(--g-oneSpace);
  padding-right: var(--g-oneSpace);
}
.py-2 {
  padding-top: var(--g-oneSpace);
  padding-bottom: var(--g-oneSpace);
}

.p-3 {
  padding: var(--g-twoSpace);
}
.pt-3 {
  padding-top: var(--g-twoSpace);
}
.pr-3 {
  padding-right: var(--g-twoSpace);
}
.pb-3 {
  padding-bottom: var(--g-twoSpace);
}
.pl-3 {
  padding-left: var(--g-twoSpace);
}
.px-3 {
  padding-left: var(--g-twoSpace);
  padding-right: var(--g-twoSpace);
}
.py-3 {
  padding-top: var(--g-twoSpace);
  padding-bottom: var(--g-twoSpace);
}

.p-4 {
  padding: var(--g-threeSpace);
}
.pt-4 {
  padding-top: var(--g-threeSpace);
}
.pr-4 {
  padding-right: var(--g-threeSpace);
}
.pb-4 {
  padding-bottom: var(--g-threeSpace);
}
.pl-4 {
  padding-left: var(--g-threeSpace);
}
.px-4 {
  padding-left: var(--g-threeSpace);
  padding-right: var(--g-threeSpace);
}
.py-4 {
  padding-top: var(--g-threeSpace);
  padding-bottom: var(--g-threeSpace);
}

.p-5 {
  padding: var(--g-fourSpace);
}
.pt-5 {
  padding-top: var(--g-fourSpace);
}
.pr-5 {
  padding-right: var(--g-fourSpace);
}
.pb-5 {
  padding-bottom: var(--g-fourSpace);
}
.pl-5 {
  padding-left: var(--g-fourSpace);
}
.px-5 {
  padding-left: var(--g-fourSpace);
  padding-right: var(--g-fourSpace);
}
.py-5 {
  padding-top: var(--g-fourSpace);
  padding-bottom: var(--g-fourSpace);
}

/* Text Alignment */
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-justify {
  text-align: justify;
}

/* Display Utilities */
.d-block {
  display: block;
}
.d-inline-block {
  display: inline-block;
}
.d-inline {
  display: inline;
}
.d-flex {
  display: flex;
}
.d-inline-flex {
  display: inline-flex;
}
.d-none {
  display: none;
}

/* Flex Utilities */
.flex-row {
  flex-direction: row;
}
.flex-column {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.justify-content-start {
  justify-content: flex-start;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-end {
  justify-content: flex-end;
}
.justify-content-between {
  justify-content: space-between;
}
.justify-content-around {
  justify-content: space-around;
}
.align-items-start {
  align-items: flex-start;
}
.align-items-center {
  align-items: center;
}
.align-items-end {
  align-items: flex-end;
}

/* Position */
.position-static {
  position: static;
}
.position-relative {
  position: relative;
}
.position-absolute {
  position: absolute;
}
.position-fixed {
  position: fixed;
}
.position-sticky {
  position: sticky;
}

.w-fit-content {
  width: fit-content;
}

.top-0 {
  top: 0;
}
.top-50 {
  top: 50%;
}
.top-100 {
  top: 100%;
}
.bottom-0 {
  bottom: 0;
}
.bottom-50 {
  bottom: 50%;
}
.bottom-100 {
  bottom: 100%;
}
.left-0 {
  left: 0;
}
.left-50 {
  left: 50%;
}
.left-100 {
  left: 100%;
}
.right-0 {
  right: 0;
}
.right-50 {
  right: 50%;
}
.right-100 {
  right: 100%;
}

/* Overflow */
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-visible {
  overflow: visible;
}
.overflow-scroll {
  overflow: scroll;
}

/* Width and Height */
.w-100 {
  width: 100%;
}
.h-100 {
  height: 100%;
}
.min-w-100 {
  min-width: 100%;
}
.min-h-100 {
  min-height: 100%;
}
.max-w-100 {
  max-width: 100%;
}

.rc-test {
  background-color: red;
}

.rc-tooltip.rc-tooltip-zoom-custom-appear,
.rc-tooltip.rc-tooltip-zoom-custom-enter {
  opacity: 0;
}
.rc-tooltip.rc-tooltip-zoom-custom-enter,
.rc-tooltip.rc-tooltip-zoom-custom-leave {
  display: block;
}
.rc-tooltip-zoom-custom-enter,
.rc-tooltip-zoom-custom-appear {
  opacity: 0;
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  animation-play-state: paused;
}
.rc-tooltip-zoom-custom-leave {
  animation-duration: 0.3s;
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.19, 1, 0.22, 1);
  animation-play-state: paused;
}
.rc-tooltip-zoom-custom-enter.rc-tooltip-zoom-custom-enter-active,
.rc-tooltip-zoom-custom-appear.rc-tooltip-zoom-custom-appear-active {
  animation-name: rcCustomToolTipZoomIn !important;
  animation-play-state: running;
}
.rc-tooltip-zoom-custom-leave.rc-tooltip-zoom-custom-leave-active {
  animation-name: rcCustomToolTipZoomOut !important;
  animation-play-state: running;
}

@keyframes rcCustomToolTipZoomIn {
  0% {
    opacity: 0;
    transform-origin: 50% 50%;
    transform: scale(0, 0) !important;
  }
  100% {
    opacity: 1;
    transform-origin: 50% 50%;
    transform: scale(1, 1);
  }
}
@keyframes rcCustomToolTipZoomOut {
  0% {
    opacity: 1;
    transform: scale(0, 0) !important;
  }
  100% {
    opacity: 0;
    transform: scale(1, 1) !important;
  }
}

.footerWrapper > :last-child {
  border-bottom-left-radius: var(--g-twoSpace);
  border-bottom-right-radius: var(--g-twoSpace);
}

.footerWrapper > :first-child {
  border-top-left-radius: var(--g-twoSpace);
  border-top-right-radius: var(--g-twoSpace);
}

/* tooltip */

.ux-tooltip .rc-tooltip-inner {
  min-height: unset !important;
  padding: var(--g-oneSpace) var(--g-twoSpace) !important;
}

.tooltip-visible .rc-tooltip {
  opacity: 1 !important;
}

/* Date picker */
table td.rdp-range_middle {
  background-color: var(--primaryFade) !important;
}

.rdp-nav {
  right: unset;
  justify-content: space-between;
  width: 100%;
}

.rdp-month_caption {
  justify-content: center;
}

.rdp-caption_label {
  font-family: Aventa;
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  text-align: left;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  color: var(--black);
}

.rdp-chevron {
  fill: var(--neutralDarkest) !important;
}

.rdp-months {
  margin: auto;
}
