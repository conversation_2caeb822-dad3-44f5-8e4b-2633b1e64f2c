import type { ReactNode } from 'react';
import { forwardRef } from 'react';
import classNames from '@utils/classNames';

import styles from './SelectInputMenu.module.css';

interface SelectInputMenuItemProps {
  children: ReactNode;
  isHighlighted?: boolean;
  left?: ReactNode;
  secondaryLabel?: string;
  isGroupBy?: boolean;
  item?: {
    menuLabel?: string;
  };
  isGroupTypeLayout?: boolean;
}

const SelectInputMenuItem = forwardRef<HTMLLIElement, SelectInputMenuItemProps>(
  (
    {
      children,
      isHighlighted = false,
      left = null,
      secondaryLabel = null,
      isGroupBy = false,
      item,
      isGroupTypeLayout = false,
      ...otherProps
    },
    ref,
  ) => {
    const classes = classNames(
      styles.item,
      isHighlighted && styles.itemHighlighted,
      left && styles.hasLeftItem,
      secondaryLabel && styles.hasSecondaryLabel,
      isGroupBy && styles.isGroupBy,
      !isGroupTypeLayout && styles.singleItem,
    );

    if (isGroupTypeLayout) {
      return (
        <li {...otherProps} ref={ref}>
          {item?.menuLabel && (
            <div className={classNames(styles.groupHeader)}>
              {item.menuLabel}
            </div>
          )}
          <div className={classes}>
            {left}
            {children}
            {secondaryLabel && <span>{secondaryLabel}</span>}
          </div>
        </li>
      );
    }

    return (
      <li {...otherProps} ref={ref} className={classes}>
        {left}
        {children}
        {secondaryLabel && <span>{secondaryLabel}</span>}
      </li>
    );
  },
);

export default SelectInputMenuItem;
