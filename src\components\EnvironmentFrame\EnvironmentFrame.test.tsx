import { render } from '@testing-library/react';

import EnvironmentFrame from './EnvironmentFrame';

describe('EnvironmentFrame component', () => {
  it('renders with default props', () => {
    const { getAllByTestId } = render(<EnvironmentFrame label="Editing" />);
    const labels = getAllByTestId('frame-label');
    expect(labels.length).toBeGreaterThan(0);
    expect(labels[0]).toHaveTextContent('Editing');
  });

  it('renders with custom label', () => {
    const { getAllByTestId } = render(
      <EnvironmentFrame label="Custom Label" />,
    );
    const labels = getAllByTestId('frame-label');
    expect(labels.length).toBeGreaterThan(0);
    expect(labels[0]).toHaveTextContent('Custom Label');
  });
});
