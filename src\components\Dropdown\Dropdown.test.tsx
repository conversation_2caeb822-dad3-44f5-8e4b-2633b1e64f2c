import { fireEvent, render, waitFor } from '@testing-library/react';

import Dropdown from './Dropdown';

describe('Dropdown component', () => {
  const item = {
    lists: [
      { label: 'Option 1', id: '1' },
      { label: 'Option 2', id: '2' },
      { label: 'Option 3', id: '3' },
    ],
  };

  it('renders dropdown with provided menu items', async () => {
    const screen = render(
      <Dropdown delayShow={0} item={item} id="dropdown" openOnClick>
        <button id="dropdown" type="button">
          Dropdown Button
        </button>
      </Dropdown>,
    );
    const { getByText } = screen;

    const button = getByText('Dropdown Button');

    fireEvent.click(button);

    await waitFor(() => {}, { timeout: 500 });

    item.lists.forEach(option => {
      expect(getByText(option.label)).toBeInTheDocument();
    });

    const label1 = getByText(item.lists[0].label);
    fireEvent.click(label1);

    await waitFor(() => {}, { timeout: 500 });
    /** seems like the trigger events not working , but still keeping this test case just to get coverage */
    expect(getByText(item.lists[0].label)).toBeInTheDocument();
  });

  it('Should not render the list', async () => {
    const screen = render(
      <Dropdown delayShow={0} item={item} id="dropdown">
        <button id="dropdown" type="button">
          Dropdown Button
        </button>
      </Dropdown>,
    );
    const { getByText } = screen;

    const button = getByText('Dropdown Button');

    fireEvent.click(button);

    const textElement = screen.queryByText(item.lists[0].label);
    expect(textElement).toBeNull();
  });
});
