/* eslint-disable no-console */
import { useEffect, useState } from 'react';
import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta } from '@storybook/react';
import Select from '@components/Select';
import { ToastContextProvider } from '@contexts/ToastContext';
import useToast from '@hooks/useToast';
import variableStyles from '@styles/variables.module.css';

import { SelectSearch } from '..';
import EyebrowDialog from './EyebrowDialog';
import { EyebrowDialogTypes } from './EyebrowDialog.types';

/**
 *
 * <br/>

## Overview

The EyebrowDialog component is built on top of the dialogmodal component, acting as a wrapper modal 
to provide consistency across the apps. To construct the entire modal from an integration perspective, 
developers need to provide props as shown in the Storybook and as described in the props documentation.

## Usage

Import the component into your React application:

```jsx
import { EyebrowDialog } from '@peddleon/ped-ux-react-library';```
 

 */
const meta: Meta<typeof EyebrowDialog> = {
  component: EyebrowDialog,
  title: 'Components/EyebrowDialog',
  tags: ['autodocs'],
  parameters: {
    docs: {
      toc: true,
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
};

const options = [
  { value: 'option1', label: 'Option 1' },
  { value: 'option2', label: 'Option 2' },
  { value: 'option3', label: 'Option 3' },
  { value: 'option4', label: 'Option 4' },
  { value: 'option5', label: 'Option 5' },
];

const EyebrowStoryWrapper = ({ modalConfig, modalContent, modalActions }) => {
  const { isActive } = modalConfig;
  const [isModalActive, setIsModalActive] = useState(isActive);
  const [value, setValue] = useState(null);
  const toast = useToast();
  useEffect(() => {
    setIsModalActive(isActive);
  }, [isActive]);

  useEffect(() => {
    if (isModalActive) {
      document.body.style.overflow = 'auto';
    }
  }, [isModalActive]);

  const openToast = () => {
    toast({
      body: 'Saved successfully',
      isActive: true,
      theme: 'success',
    });
  };

  return (
    <div
      style={{
        padding: variableStyles.oneSpace,
        height: '600px',
      }}
    >
      {!isModalActive && (
        <button
          type="button"
          style={{ cursor: 'pointer' }}
          onClick={() => setIsModalActive(true)}
        >
          {' '}
          Show Dialog{' '}
        </button>
      )}
      <EyebrowDialog
        modalConfig={{ ...modalConfig, isActive: isModalActive }}
        modalContent={{
          ...modalContent,
          bodyContent: modalContent.bodyContent || (
            <>
              <Select
                caption="Example Caption"
                value={value?.value ?? value}
                items={options}
                label="Label"
                onChange={e => {
                  setValue(e);
                }}
                isMobileMenuModal={false}
                isFixedMenu
                mobileModalPosition="bottom"
              />
              <SelectSearch
                caption="Example Caption"
                value={
                  value?.value ? value : options?.find(o => o?.value === value)
                }
                items={options}
                label="Label"
                onChange={e => {
                  setValue(e);
                }}
                isFixedMenu
                mobileModalPosition="bottom"
              />
            </>
          ),
        }}
        modalActions={{
          ...modalActions,
          onClose: () => {
            setIsModalActive(false);
          },
          onAccept: () => {
            openToast();
          },
        }}
      />
    </div>
  );
};

export const EyebrowDialogStory = ({
  modalConfig,
  modalActions,
  modalContent,
}: EyebrowDialogTypes) => (
  <ToastContextProvider timer={3000} position="topCenter" isCancellable>
    <EyebrowStoryWrapper
      modalConfig={modalConfig}
      modalContent={modalContent}
      modalActions={modalActions}
    />
  </ToastContextProvider>
);

EyebrowDialogStory.args = {
  modalConfig: {
    isActive: true,
    acceptButtonTheme: 'primary',
    declineButtonTheme: 'light',
    hasFullWidthButtons: true,
    isAnimated: true,
    hasCloseButton: true,
    acceptButtonDisabled: false,
    declineButtonDisabled: false,
    isAcceptLoading: false,
    isDeclineLoading: false,
    isMobileCentered: false,
    maxBodyHeight: 'none',
  },
  modalActions: {
    onAccept: () => console.log('OnAccept Triggered!!'),
    onClose: () => console.log('OnClose Triggered!!'),
    onDecline: () => console.log('OnDecline Triggered!!'),
  },
  modalContent: {
    headingContent: 'ready to accept?',
    preHeadingContent: 'almost there',
    postHeadingContent: '2008 Honda Civic Sedan LX',
    bodyContent: null,
    acceptLabel: 'Save Offer for $1,902',
    declineLabel: 'Let’s donate my car',
  },
};

export default meta;
