import { createContext, useContext, useMemo, useState } from 'react';
import NotificationModal from '@components/DialogModal/NotificationModal';
import {
  ToastConfigType,
  ToastContextProviderType,
  ToastContextType,
} from '@libtypes/common';

const DEFAULT_TOAST_TIMEOUT = 3000;

export const ToastContext = createContext<ToastContextType>({
  setToastConfig: () => {},
  toastConfig: {
    body: '',
    isActive: false,
    theme: 'success',
  },
  timer: DEFAULT_TOAST_TIMEOUT,
  isCancellable: false,
  position: 'topCenter',
});

export function useToastContext() {
  return useContext(ToastContext);
}

export const ToastContextProvider = ({
  children,
  position,
  isCancellable,
  timer,
}: ToastContextProviderType) => {
  const [toastConfig, setToastConfig] = useState<ToastConfigType>({
    body: '',
    isActive: false,
    theme: 'success',
  });

  const contextValue = useMemo(
    () => ({
      toastConfig,
      setToastConfig,
      position,
      isCancellable,
      timer,
    }),
    [toastConfig, setToastConfig, position, isCancellable, timer],
  );

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
      <NotificationModal key={toastConfig.key} />
    </ToastContext.Provider>
  );
};
