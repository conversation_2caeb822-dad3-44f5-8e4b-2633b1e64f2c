name: PR package cleanup

on:
  pull_request:
    types: [closed]

jobs:
  test:
    name: PR package cleanup
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'
          registry-url: https://npm.pkg.github.com/
          cache: 'npm'

      - name: 'Set $VERSION'
        run: VERSION=$(cat package.json | jq -r '.version')

      ## Build a semver regex that captures all releases under the pr number
      - name: Build semver regex
        uses: frabert/replace-string-action@master
        id: semver
        with:
          pattern: 'PR'
          string: "^\\d+\\.\\d+\\.\\d+\\-pr-PR\\.\\d+$"
          replace-with: ${{ github.event.pull_request.number }}
          flags: 'g'

      - name: Delete closed PR tag
        run: |
          if npm view @peddleon/apex dist-tags | grep -q "pr-$PR"; then
            npm dist-tags rm @peddleon/apex pr-$PR
          fi
        env:
          PR: ${{ github.event.pull_request.number }}
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Delete old pr packages
        uses: smartsquaregmbh/delete-old-packages@v0.4.0
        with:
          keep: 0
          dry-run: false
          names: |
            apex
          version-pattern: ${{ steps.semver.outputs.replaced }}
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
