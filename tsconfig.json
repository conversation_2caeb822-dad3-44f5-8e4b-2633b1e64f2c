{"include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist"], "compilerOptions": {"incremental": true, "allowJs": true, "checkJs": true, "baseUrl": "./src", "jsx": "react-jsx", "paths": {"@components/*": ["components/*"], "@libtypes/*": ["types/*"], "@hooks/*": ["hooks/*"], "@constants/*": ["constants/*"], "@utils/*": ["utils/*"], "@stories/*": ["stories/*"], "@styles/*": ["styles/*"], "@contexts/*": ["contexts/*"]}, "module": "esnext", "target": "esnext", "lib": ["dom"], "declaration": true, "declarationMap": true, "strict": false, "moduleResolution": "node", "skipLibCheck": true, "esModuleInterop": true, "noEmit": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "outDir": "./dist", "types": ["@testing-library/jest-dom"]}}