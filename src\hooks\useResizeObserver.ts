/* eslint-disable @typescript-eslint/no-explicit-any */
import { MutableRefObject, useCallback, useRef, useState } from 'react';
import useIsomorphicLayoutEffect from '@hooks/useIsomorphicLayoutEffect';

interface ObserverEntry {
  contentRect: any;
  target: Element;
  devicePixelContentBoxSize: number;
  contentBoxSize?: any;
  borderBoxSize?: any;
}

function useResizeObserver(target: MutableRefObject<Element>) {
  const observer = useRef(null);

  const [size, setSize] = useState(null);

  /* eslint-disable react-hooks/exhaustive-deps */
  const handleObserver = useCallback((entries: Array<ObserverEntry>) => {
    Object.values(entries).forEach(entry => {
      const {
        contentRect,
        target: entryTarget,
        devicePixelContentBoxSize,
      } = entry;

      let { contentBoxSize = null, borderBoxSize = null } = entry;

      if (entryTarget === target.current) {
        // Ultimately, we won't use these for now, as they are not widely supported.  Including them for the future.
        if (contentBoxSize) {
          // Firefox implements `contentBoxSize` as a single content rect, rather than an array
          contentBoxSize = Array.isArray(contentBoxSize)
            ? contentBoxSize[0]
            : contentBoxSize;
          borderBoxSize = Array.isArray(borderBoxSize)
            ? borderBoxSize[0]
            : borderBoxSize;
        }

        setSize({
          contentBoxSize,
          borderBoxSize,
          contentRect,
          devicePixelContentBoxSize,
          target: entryTarget,
        });
      }
    });
  }, []);
  /* eslint-enable react-hooks/exhaustive-deps */

  useIsomorphicLayoutEffect(() => {
    observer.current?.disconnect();

    if (typeof ResizeObserver !== 'undefined') {
      observer.current = new ResizeObserver(handleObserver as any);
    }
  }, []);

  useIsomorphicLayoutEffect(() => {
    if (!target?.current || typeof ResizeObserver === 'undefined') {
      return () => {};
    }

    if (target?.current) {
      observer.current.observe(target.current);
    }
    return () => {
      if (target?.current) {
        observer?.current?.unobserve(target?.current);
      }
    };
  }, [target?.current]);

  return size;
}

export default useResizeObserver;
