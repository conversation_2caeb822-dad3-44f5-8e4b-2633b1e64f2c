@value variables: "../../../styles/variables.module.css";
@value white, black10, smallWidth, twoSpace, threeSpace, eightSpace from variables;
@value typography: "../../../styles/typography.module.css";
@value h5Strong from typography;

.containerWrapper {
  position: relative;
  display: flex;
  justify-content: center;
  flex: 1;
  min-height: 1px;
  /* This is defined in Modal.module.css */
  border-radius: twoSpace;
  overflow: hidden;
}

.containerScroll {
  overflow: auto;
  width: 100%;
}

.container {
  text-align: center;
  @media (max-width: smallWidth) {
    padding-right: threeSpace;
    padding-left: threeSpace;
    padding-top: twoSpace;
  }
}

.scrollHeading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  transition: opacity 0.1s;
  height: var(--mobileDialogScrollHeadingHeight);
  border-bottom: 1px solid black10;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  background: white;
  display: flex;
  align-items: center;
  padding-left: twoSpace;
  pointer-events: auto;
  opacity: 1;
}

.scrollHeadingText {
  composes: h5Strong;
  transition: opacity 0.3s;
  opacity: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: eightSpace;
  @media (max-width: smallWidth) {
    opacity: 1;
  }
}
