@value variables: "../../../styles/variables.module.css";
@value primary, black, black5, pinnedColumnZIndex, neutralDarker, black, white5, white10, white70,bodySmallStrong, white, neutralLightest, ease-out-expo, aboveZIndex, twoSpace, oneSpace from variables;
@value typography: "../../../styles/typography.module.css";
@value overline, bodySmall, bodyBaseFontSize from typography;

.headerGroup {
  background-color: white;
  padding: oneSpace;
}

.hasFirstColumnPinned {
  position: sticky;
  left: 0;
  z-index: pinnedColumnZIndex !important;
}

.isInverted {
  color: white70;

  & .header {
    color: neutralLightest;
  }

  & .headerGroup {
    background-color: inherit;
    padding-left: twoSpace;
    color: white70;
    &.headerSortable {
      &:focus {
        color: primary;
      }
    }
    &.headerSorted {
      color: white;
    }
  }
}

.disableSorting {
  cursor: default !important;
}

.header {
  composes: bodySmallStrong;
  color: neutralDarker;
  text-align: left;
  transition: color 0.5s ease-out-expo;
  user-select: none;
  padding: twoSpace;
  cursor: pointer;

  &:focus {
    outline: none;
  }

  &.headerSortable {
    &:focus {
      color: primary;
    }
  }

  &.headerSorted {
    color: primary !important;
  }
}

.columnDropdown {
  visibility: hidden;
}

.headerRoot {
  display: flex;
  justify-content: flex-start;
  align-items: center;

  /* add hover state where .test should be visible on hover */
  &:hover {
    .columnDropdown {
      visibility: visible;
    }
  }
}

.arrowIconWrapper {
  display: inline-block;
  transition: transform 0.5s ease-out-expo;
  line-height: 0;
  vertical-align: sub;
  height: 20px;
  margin-left: oneSpace;

  &.ascending {
    transform: rotate(180deg);
  }
}
