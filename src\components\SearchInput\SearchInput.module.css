@value variables: "../../styles/variables.module.css";
@value typography: "../../styles/typography.module.css";
@value smallWidth, mediumWidth from variables;
@value caption, bodySmall from typography;

.input {
  & input {
    @media (max-width: smallWidth) {
      font-size: 14px;
    }
    /* accomodates placeholder */
    @media (min-width: mediumWidth) {
      min-width: 270px;
    }
  }
}

.mobileCloseButton {
  display: flex;
  align-items: center;
}
