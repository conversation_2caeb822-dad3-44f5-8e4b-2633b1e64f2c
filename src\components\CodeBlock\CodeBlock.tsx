import { FC, SyntheticEvent } from 'react';

import styles from './CodeBlock.module.css';
import { CodeBlockPropTypes } from './CodeBlock.types';

/**
 * The CodeBlock component displays a code snippet with an optional icon.
 * It renders the code snippet enclosed in a pre tag and supports an optional icon
 * that triggers a callback function when clicked.
 *
 * @param {CodeBlockPropTypes} props - The props for the CodeBlock component.
 * @param {string} props.code - The code snippet to display.
 * @param {ReactNode} [props.icon] - Optional. The icon to display for performing the action for the code snippet.
 * @param {Function} [props.onClick=() => {}] - Optional. Callback function invoked when the icon is clicked.
 * @returns {JSX.Element} - The rendered CodeBlock component.
 */
const CodeBlock: FC<CodeBlockPropTypes> = ({
  code,
  icon,
  onClick = () => { },
}) => {
  const onClickHandler = (event: SyntheticEvent<Element, Event>) => {
    onClick(code, event);
  };
  return (
    <div className={styles.codeBlock}>
      {icon && (
        <div
          className={styles.copyBtn}
          role="presentation"
          onClick={onClickHandler}
          onKeyDown={onClickHandler}
        >
          {icon}
        </div>
      )}
      <div className={icon ? styles.hasIcon : ''}>
        <pre>{code}</pre>
      </div>
    </div>
  );
};

export default CodeBlock;
