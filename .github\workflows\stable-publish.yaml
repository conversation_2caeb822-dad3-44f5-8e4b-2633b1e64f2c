name: Publish stable release

on:
  push:
    branches:
      - main

jobs:
  publish-gpr:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Delete old stable packages
        uses: smartsquaregmbh/delete-old-packages@v0.4.0
        with:
          keep: 20
          dry-run: false
          ## Production releases!
          version-pattern: "^\\d+\\.\\d+\\.\\d+$"
          names: |
            apex

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version-file: '.nvmrc'
          registry-url: https://npm.pkg.github.com/
          cache: 'npm'

      - name: Install dependencies
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GIT_TOKEN }}

      - name: Publish latest package
        run: npm publish --tag latest
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Publish stable package
        run: npm publish --tag stable
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
