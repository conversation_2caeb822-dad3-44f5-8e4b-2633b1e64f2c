import React, { useState } from 'react';

import SearchInput from './SearchInput';

export default {
  title: 'Components/Inputs/Search Input',
};

export const Story = ({ maxLength }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [value, setValue] = useState('');
  return (
    <div style={{ margin: '1rem' }}>
      <SearchInput
        isOpen={isOpen}
        onOpen={setIsOpen}
        value={value}
        onChange={setValue}
        // eslint-disable-next-line no-alert
        onSearch={search => alert(`Triggered search for "${search}"`)}
        maxLength={maxLength}
      />
    </div>
  );
};

Story.storyName = 'Search Input';

Story.args = {
  maxLength: 100,
};
