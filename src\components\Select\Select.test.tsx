import { useState } from 'react';
import { render, screen } from '@testing-library/react';
// eslint-disable-next-line import/no-extraneous-dependencies
import userEvent from '@testing-library/user-event';
import { SELECT_DATA_TEST_ID } from '@constants/dataTestId';

import Select from './Select';

// Define option items for testing
const OPTION_ITEMS = [
  { value: 'value1', label: 'Label 1' },
  { value: 'test1', label: 'Test 1' },
  { value: 'value2', label: 'Label 2' },
  { value: 'value3', label: 'Label 3' },
];

// Component wrapper for testing the Select component
const SelectWrapper = ({
  items = OPTION_ITEMS,
  value = null,
  label = 'Label',
}) => {
  const [state, setState] = useState(value);
  return (
    <Select
      items={items}
      label={label}
      value={state}
      onChange={v => setState(v)}
    />
  );
};

describe('Select Component', () => {
  it('should render the select input with a label value of "Label"', async () => {
    // Render the component with some props
    render(<SelectWrapper label="Custom Label" />);

    const select = screen.getByTestId(SELECT_DATA_TEST_ID);
    // Expect the select to be visible in the browser
    expect(select).toBeInTheDocument();
    expect(select).toBeVisible();

    // Expect the label to be in the document
    const labels = screen.getAllByText('Custom Label');
    // Check if label has the correct class
    expect(labels[0]).toHaveClass('label');
    // Check if label is rendered as an option
    expect(labels[1]).toHaveRole('option');
  });

  it('renders with a preselected value', async () => {
    // Render the component with a preselected value
    render(<SelectWrapper value="value1" />);

    // Click on the input
    await userEvent.click(screen.getByTestId(SELECT_DATA_TEST_ID));

    // Expect 'Label 1' to be in the document
    const labels = screen.getAllByText('Label 1');

    const selectedLabel = labels.find(label =>
      label.classList.contains('selected'),
    );

    // Check that 'Label 1' is highlighted based on the applied class name
    expect(selectedLabel).not.toBeUndefined();
    expect(selectedLabel).toBeInTheDocument();
    expect(selectedLabel).toBeVisible();
  });
});
