import React from 'react';
import { fireEvent, render } from '@testing-library/react';
import { BUTTON_TEST_ID, NAV_ITEM_MOBILE } from '@constants/dataTestId';

import NavigationBar, { NavigationBarProps } from './NavigationBar';

describe('NavigationBar Component', () => {
  const defaultProps: NavigationBarProps = {
    leftNavItems: [
      {
        label: 'Dashboard',
        theme: 'transparentInverted',
        id: 'left',
      },
    ],
    rightNavItems: [
      {
        label: 'Dashboard-Right',
        theme: 'transparentInverted',
        link: '/',
        id: 'right',
      },
    ],
    logoUrl: 'https://example.com/logo.png',
    mobileNavButton: [
      {
        label: 'Mobile-button',
        theme: 'transparentInverted',
        id: 'mobile',
      },
    ],
    onNavigationClick: jest.fn(),
  };

  it('renders without crashing', () => {
    const { container } = render(<NavigationBar {...defaultProps} />);
    expect(container).toBeTruthy();
  });

  it('opens mobile navigation menu on button click', () => {
    const { getByTestId } = render(<NavigationBar {...defaultProps} />);
    const menuToggleButton = getByTestId(`mobile-${BUTTON_TEST_ID}`);
    fireEvent.click(menuToggleButton);
    expect(defaultProps.onNavigationClick).toHaveBeenCalled();
  });

  it('should toggle the mobile menu', async () => {
    const { getByTestId } = render(<NavigationBar {...defaultProps} />);
    const toggleMenu = getByTestId(`toggle-${BUTTON_TEST_ID}`);
    fireEvent.click(toggleMenu);
    expect(getByTestId(NAV_ITEM_MOBILE)).toBeInTheDocument();
  });

  it('should have right navigation', async () => {
    const { getByText } = render(<NavigationBar {...defaultProps} />);
    expect(getByText('Dashboard-Right')).toBeInTheDocument();
  });

  it('should have mobile button', async () => {
    const { getByText } = render(<NavigationBar {...defaultProps} />);
    expect(getByText('Mobile-button')).toBeInTheDocument();
  });

  // Add more test cases based on your requirements
});
