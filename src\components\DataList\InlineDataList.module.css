@value variables: "../../styles/variables.module.css";
@value typography: "../../styles/typography.module.css";

@value oneSpace, halfSpace, smallWidth, twoSpace, black70, black50, black, black5, black10, white70, white, white5, white10 from variables;
@value ease-out-expo from variables;
@value bodyBase, bodySmall from typography;

.inlineDataList {
  --dt-color: black70;
  --dd-color: black;
  --hover-color: black5;
  --border-color: black10;
  display: flex;
  flex-direction: column;
  max-width: none;

  &.withBorders {
    & > *:not(:last-of-type) {
      box-shadow: inset 0 -1px 0 0 var(--border-color);
    }
  }
  &.hasTopBorder {
    box-shadow: inset 0 1px 0 0 var(--border-color);
  }
}
.primary {
  composes: bodyBase;

  & .dd {
    font-weight: 700;
  }

  & .childWrapper {
    padding-top: oneSpace;
    padding-bottom: oneSpace;
  }
}
.secondary {
  composes: bodySmall;
}

.inlineDataList.inverted {
  --dt-color: white70;
  --dd-color: white;
  --hover-color: white5;
  --border-color: white10;
}

.inlineDataList .editButtonContainer {
  align-self: flex-end;
}

.childWrapper {
  width: 100%;
}

.inlineDataListItem {
  display: flex;
  align-items: center;
  padding-top: oneSpace;
  padding-bottom: oneSpace;
  text-align: left;
  transition: background-color 0.5s ease-out-expo;
  width: 100%;

  &.editable {
    cursor: pointer;
    border-radius: 8px;
  }
}

.inlineDataListItem.editable:hover,
.inlineDataListItem.editable:focus {
  outline: none;
  background-color: var(--hover-color);
}

.dt {
  width: var(--termColumnWidth);
  display: flex;
  justify-content: flex-end;
}

.dd,
.dt {
  margin: 0;
  padding: 0 oneSpace;
}

.dt {
  color: var(--dt-color);
}

.dd {
  color: var(--dd-color);
  width: var(--detailsColumnWidth);
  align-items: center;
  justify-content: space-between;
  display: flex;
  flex: 1;
}

.inlineDataListItem.editable .editItemButton {
  display: flex;
  visibility: hidden;
  align-items: center;
  color: var(--dd-color);
  margin-left: halfSpace;
}

.inlineDataListItem.editable:hover .editItemButton,
.inlineDataListItem.editable:focus .editItemButton {
  visibility: visible;
}

.empty {
  color: var(--dd-color);
  opacity: 0.35;
  font-weight: 400;
  display: block;
  width: 100%;
  text-align: right;
}
