{"jsc": {"target": "es2021", "parser": {"syntax": "typescript", "tsx": true, "dynamicImport": true, "decorators": true, "decoratorsBeforeExport": true, "topLevelAwait": true}, "transform": {"legacyDecorator": true, "decoratorMetadata": true, "react": {"runtime": "automatic", "importSource": "react"}}, "baseUrl": "./src", "externalHelpers": true, "paths": {"~/*": ["./*"]}}, "module": {"type": "es6"}, "sourceMaps": true}