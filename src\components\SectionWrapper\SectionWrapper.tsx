import { FC, forwardRef } from 'react';
import Divider from '@components/Divider';
import SectionTitle from '@components/SectionTitle';
import classNames from '@utils/classNames';

import styles from './SectionWrapper.module.css';
import { SectionWrapperProps } from './SectionWrapper.types';

const SectionWrapper: FC<SectionWrapperProps> = forwardRef<
  HTMLDivElement,
  SectionWrapperProps
>(
  (
    {
      children,
      headingText,
      icon,
      actions,
      options = {
        hasDevider: true,
        dividerTheme: 'grey',
        actionDirection: 'right',
        headingTag: 'h2',
        headingProps: {},
      },
      wrapperClassName,
    },
    ref,
  ) => (
    <div
      ref={ref}
      className={classNames(
        styles.sectionWrapper,
        wrapperClassName,
        'section-wrapper',
      )}
    >
      <SectionTitle
        headingText={headingText}
        icon={icon}
        actionDirection={options.actionDirection}
        actions={actions}
        headingTag={options.headingTag}
        headingProps={options.headingProps}
      />
      {children && (
        <div className={classNames(styles.content, 'content')}>{children}</div>
      )}
      {options.hasDevider && <Divider theme={options.dividerTheme} />}
    </div>
  ),
);

export default SectionWrapper;
