import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

import StatusIndicator from './StatusIndicator';

export default {
  title: 'Components/Utility',
};

export const StatusIndicatorSheet = () => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
    }}
  >
    <ComponentGrid countColumns={4}>
      <ComponentTile label="Dark / Incomplete">
        <StatusIndicator theme="dark" />
      </ComponentTile>

      <ComponentTile label="Dark / Complete">
        <StatusIndicator theme="dark" isComplete />
      </ComponentTile>

      <ComponentTile label="Neutral Dark / Incomplete">
        <StatusIndicator theme="neutralDark" />
      </ComponentTile>

      <ComponentTile label="Neutral Dark / Complete">
        <StatusIndicator theme="neutralDark" isComplete />
      </ComponentTile>

      <ComponentTile label="Neutral Darker / Incomplete">
        <StatusIndicator theme="neutralDarker" />
      </ComponentTile>

      <ComponentTile label="Neutral Darker / Complete">
        <StatusIndicator theme="neutralDarker" isComplete />
      </ComponentTile>

      <ComponentTile label="Danger / Incomplete">
        <StatusIndicator theme="danger" />
      </ComponentTile>

      <ComponentTile label="Danger / Complete">
        <StatusIndicator theme="danger" isComplete />
      </ComponentTile>

      <ComponentTile label="Primary / Incomplete">
        <StatusIndicator theme="primary" />
      </ComponentTile>

      <ComponentTile label="Primary / Complete">
        <StatusIndicator theme="primary" isComplete />
      </ComponentTile>

      <ComponentTile label="Success / Incomplete">
        <StatusIndicator theme="success" />
      </ComponentTile>

      <ComponentTile label="Success / Complete">
        <StatusIndicator theme="success" isComplete />
      </ComponentTile>

      <ComponentTile label="Warning / Incomplete">
        <StatusIndicator theme="warning" />
      </ComponentTile>

      <ComponentTile label="Warning / Complete">
        <StatusIndicator theme="warning" isComplete />
      </ComponentTile>
    </ComponentGrid>
  </div>
);
