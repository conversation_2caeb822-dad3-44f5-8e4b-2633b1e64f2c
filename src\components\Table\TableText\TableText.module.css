@value variables: "../../../styles/variables.module.css";
@value black, black5, primary, neutralDarkest, black, white5, white10, white70, white, neutralLightest, ease-out-expo, aboveZIndex, twoSpace, oneSpace from variables;
@value typography: "../../../styles/typography.module.css";
@value overline, bodySmall, bodySmallStrong, bodyBaseFontSize from typography;

.masked {
  display: flex;
  align-items: center;
}

.maskedIcon {
  cursor: pointer;
  margin-left: oneSpace;
}

.text {
  composes: bodySmall;
  color: neutralDarkest;
  padding: twoSpace;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.active {
  composes: bodySmallStrong;
  color: black;
}

.indicatorRoot {
  display: flex;
  flex-flow: row;
  align-items: center;
}

.indicatorWrapper {
  line-height: 0;
  margin-right: oneSpace;
}

.clipboardIcon {
  margin-left: oneSpace;
  height: twoSpace;
  cursor: pointer;
}

.label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.isInverted {
  color: white !important;

  button:hover {
    color: white;
  }
}

.icon {
  display: flex;
  justify-content: flex-end;
}

.iconButton {
  cursor: pointer;

  &:hover {
    color: black;
  }
}

.link {
  text-decoration: underline;
  /* color: primary; */
  & * {
    text-decoration: underline !important;
  }
}
