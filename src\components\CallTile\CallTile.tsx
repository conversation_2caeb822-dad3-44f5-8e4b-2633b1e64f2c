// import { FC } from 'react';
// import { PhoneIcon } from '@peddleon/ped-ux-react-icons';

// import variableStyles from '../../styles/variables.module.css';
// import styles from './CallTIle.module.css';

// /**
//  * Render a call tile component with a phone icon and a support phone number.
//  *
//  * @param {string} props.supportPhoneNumber - The phone number to be displayed.
//  * @return {JSX.Element} The rendered call tile component.
//  */
// const CallTile: FC<{ supportPhoneNumber: string }> = ({
//   supportPhoneNumber,
// }) => (
//   <div className={styles.supportFooter}>
//     <div className={styles.supportContainer}>
//       <div className={styles.phoneIcon}>
//         <PhoneIcon
//           height={24}
//           width={24}
//           stroke={variableStyles.neutralDarkest}
//         />
//       </div>
//       <div>
//         <div className={styles.supportLabel}>Need to change something?</div>
//         <a href={`tel:${supportPhoneNumber}`} className={styles.supportNumber}>
//           {`Call ${supportPhoneNumber}`}
//         </a>
//       </div>
//     </div>
//   </div>
// );

// export default CallTile;
