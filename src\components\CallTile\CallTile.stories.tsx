import { Meta } from '@storybook/react';

import CallTile from './CallTile';

/**
 * The CallTile component displays a phone icon and a support phone number for contacting customer support.
 * It provides a convenient way for users to initiate a phone call to the provided support phone number.
 *
 * ## Overview
 *
 * The CallTile component is used to present a clickable phone number along with a phone icon,
 * allowing users to quickly initiate a call to the provided support phone number.
 * It serves as a user-friendly interface element for contacting customer support or making inquiries.
 *
 * ## Usage
 *
 * To use the CallTile component in your React application, import it from the appropriate directory and include it in your JSX.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { CallTile } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the CallTile component within your application, passing the support phone number as a prop:
 *
 * ```jsx
 * <CallTile supportPhoneNumber="************" />
 * ```
 *
 * This will render the CallTile component with the provided support phone number, allowing users to initiate a call.
 */

const meta: Meta<typeof CallTile> = {
  title: 'Components/Tiles/Call Tile',
  tags: ['autodocs'],
  component: CallTile,
};

export const CallTileStroy = ({ supportPhoneNumber }) => (
  <div style={{ padding: '1rem' }}>
    <CallTile supportPhoneNumber={supportPhoneNumber} />
  </div>
);

CallTileStroy.args = {
  supportPhoneNumber: '************',
};
CallTileStroy.argTypes = {
  supportPhoneNumber: {
    description:
      'The phone number to be displayed within the call tile component',
  },
};

export default meta;
