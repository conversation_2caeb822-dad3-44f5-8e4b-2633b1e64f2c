/* eslint-disable no-console */
import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta } from '@storybook/react';
import Button from '@components/Button';
import SectionTitle from '@components/SectionTitle';

import { SectionTitleProps } from './SectionTitle.types';
/**
 *
 * <br/>

## Overview


The Section Wrapper component is a versatile container for organizing content within a UI section. 
It offers features like customizable headings, icons, and action buttons. 
To construct the section wrapper from an integration perspective, 
developers need to provide props as shown in the Storybook and as described in the props documentation.


## Usage

Import the component into your React application:

```jsx
import { SectionTitle } from '@peddleon/ped-ux-react-library';```
 

 */

const meta: Meta<typeof SectionTitle> = {
  component: SectionTitle,
  title: 'Components/SectionTitle',
  tags: ['autodocs'],
  parameters: {
    docs: {
      toc: true,
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
};

export const SectionTitleStory = ({
  headingText,
  icon,
  actions,
  actionDirection,
}: SectionTitleProps) => (
  <SectionTitle
    headingText={headingText}
    icon={icon}
    actions={actions}
    actionDirection={actionDirection}
  />
);

SectionTitleStory.args = {
  headingText: 'Sample Section',
  icon: (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      width="24"
      height="24"
    >
      <circle
        cx="12"
        cy="12"
        r="10"
        fill="none"
        stroke="black"
        strokeWidth="2"
      />
      <circle cx="12" cy="12" r="2" fill="black" />
    </svg>
  ),
  actions: [
    [
      <Button
        key="button1"
        label="Button 1"
        theme="light"
        onClick={() => console.log('Action 2 clicked')}
      />,
      <Button
        key="button1"
        label="Button 1"
        theme="light"
        onClick={() => console.log('Action 2 clicked')}
      />,
      <Button
        key="button1"
        label="Button 1"
        theme="light"
        onClick={() => console.log('Action 2 clicked')}
      />,
      <Button
        key="button1"
        label="Button 1"
        theme="light"
        onClick={() => console.log('Action 2 clicked')}
      />,
      <Button
        key="button1"
        label="Button 1"
        theme="light"
        onClick={() => console.log('Action 2 clicked')}
      />,
      <Button
        key="button1"
        label="Button 1"
        theme="light"
        onClick={() => console.log('Action 2 clicked')}
      />,
      <Button
        key="button1"
        label="Button 1"
        theme="light"
        onClick={() => console.log('Action 2 clicked')}
      />,
    ],
  ],
  actionDirection: 'right',
};

SectionTitleStory.argTypes = {
  headingText: {
    control: 'text',
    defaultValue: 'Example Section',
  },
  icon: {
    type: '',
  },
  actions: {
    type: 'array',
  },
  actionDirection: {
    control: { type: 'radio' },
    options: ['left', 'right'],
  },
};
export default meta;
