@value variables: "../../../styles/variables.module.css";
@value oneSpace, twoSpace ,shadow, primary, primaryFade, neutralDark, buttonLabelSmallFontSize from variables;
@value black, black10, ease-out-expo from variables;

.item {
  font-weight: 500;
  font-size: 16px;
  line-height: 1.5;
  color: black;
  padding-top: oneSpace;
  padding-right: twoSpace;
  padding-bottom: oneSpace;
  padding-left: twoSpace;
  cursor: pointer;
  display: block;

  &:first-of-type {
    margin-top: oneSpace;
  }

  &:last-of-type {
    margin-bottom: oneSpace;
  }

  &.itemHighlighted,
  &:hover,
  &:focus {
    color: primary;
    background-color: primaryFade;
  }

  &.hasLeftItem {
    display: flex;
    align-items: center;
  }
  &.hasSecondaryLabel > span {
    color: neutralDark;
    display: block;
    font-size: buttonLabelSmallFontSize;
  }
}

.menuListItem {
  display: flex;
  align-items: center;
  gap: oneSpace;
}
