import React, { ReactElement, useCallback, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { compute } from 'compute-scroll-into-view';
import { useSelect } from 'downshift';
// import {
//   AlertTriangleIcon,
//   CheckIcon,
//   ChevronDownIcon,
// } from '@peddleon/ped-ux-react-icons';
import AnimatedLoader from '@components/AnimatedLoader';
import EyebrowDialog from '@components/EyebrowDialog';
import {
  SelectInputMenu,
  SelectInputMenuItem,
} from '@components/SelectInputMenu';
import { SELECT_DATA_TEST_ID } from '@constants/dataTestId';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import findScrollableParent from '@utils/findScrollableParent';
import variableStyles from '@styles/variables.module.css';

import styles from './Select.module.css';
import { SelectProps } from './Select.types';

/**
 * The Select component renders a select input with customizable options.
 * It supports features such as controlled and uncontrolled behavior, validation,
 * error handling, and mobile-friendly dropdown menus.
 *
 * @param {SelectProps} props - The props for the Select component.
 * @param {string | null} [props.id=null] - Optional. The ID for the component.
 * @param {boolean} [props.isMenuPositionTop=false] - Optional. Specifies whether the menu should be displayed from the top.
 * @param {string | null} [props.name=null] - Optional. The name for the component.
 * @param {string} [props.label=''] - Optional. The label text for the component.
 * @param {string} [props.error=''] - Optional. The error message to display.
 * @param {string} [props.caption=''] - Optional. Additional caption text to display.
 * @param {React.ReactNode} [props.left=null] - Optional. The component to render on the left side of the input.
 * @param {React.ReactNode} [props.right=null] - Optional. The component to render on the right side of the input.
 * @param {Array<SelectItemType>} props.items - Required. An array of items to be displayed in the dropdown menu.
 * @param {string | null} [props.value=null] - Optional. The value of the selected item.
 * @param {boolean} [props.hasSucceeded=false] - Optional. Specifies whether the component has succeeded.
 * @param {boolean} [props.isLoading=false] - Optional. Specifies whether the component is in a loading state.
 * @param {boolean} [props.isDisabled=false] - Optional. Specifies whether the component is disabled.
 * @param {boolean} [props.isBorderless=false] - Optional. Specifies whether the component is borderless.
 * @param {boolean} [props.isActive=false] - Optional. Specifies whether the dropdown menu is active/open.
 * @param {boolean} [props.isActiveControlled=false] - Optional. Specifies whether the active state of the dropdown menu is controlled externally.
 * @param {boolean} [props.isMenuAlignedRight=false] - Optional. Specifies whether the dropdown menu is aligned to the right.
 * @param {boolean} [props.isMobileMenuModal=true] - Optional. Specifies whether the dropdown menu is displayed as a modal on mobile devices.
 * @param {boolean} [props.isFocused=false] - Optional. Specifies whether the select input is focused.
 * @param {Function} [props.onChange=() => {}] - Required. Callback function invoked when the selected value changes.
 * @param {Function} [props.onBlur=() => {}] - Optional. Callback function invoked when the input loses focus.
 * @param {Function} [props.onShouldClose=() => {}] - Optional. Callback function invoked when the menu should be closed.
 * @param {Function} [props.onInvalid=() => {}] - Optional. Callback function invoked when the input is invalid.
 * @param {Function} [props.onToggleButtonClick=() => {}] - Optional. Callback function invoked when the toggle button is clicked while the input is in focus.
 * @param {boolean} [props.isFixedMenu=false] - Optional. Flag to indicate if the menu should be fixed so it can float over elements above or below.
 * @param {string} [props.mobileModalPosition='center'] - Optional. Position of the modal on mobile devices.
 * @returns {JSX.Element} - The rendered Select component.
 */
const Select = ({
  id = null,
  name = null,
  label = '',
  error = '',
  caption = '',
  left = null,
  right = null,
  items,
  /*
  NOTE - here it's actually important to default this to "null" and not
  "undefined" due to the way downshift handles controlled props, see
  https://github.com/downshift-js/downshift#control-props
  */
  value = null,
  hasSucceeded = false,
  isLoading = false,
  isDisabled = false,
  isBorderless = false,
  isActive = false,
  isActiveControlled = false,
  isMenuAlignedRight = false,
  isMobileMenuModal = false,
  isFocused = false,
  onChange = () => {},
  required = false,
  onBlur = () => {},
  onShouldClose = () => {},
  onInvalid = () => {},
  // This should only be use when component is controlled
  onToggleButtonClick = () => {},
  isFixedMenu = false,
  mobileModalPosition = 'center',
  className = '',
  hideErrorMessage = false,
  title = '',
}: SelectProps) => {
  const DEFAULT_VALUE = '';
  // If no item is found, we pass null to avoid downshift uncontrolled prop error
  const selectedItem =
    (value && items.find(item => item.value === value)) ?? null;

  const selectRef = useRef<HTMLSelectElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const selectContainerRef = useRef<HTMLDivElement>(null);
  const menuListRef = useRef<HTMLDivElement>(null);
  const selectWrapperRef = useRef<HTMLDivElement>(null);
  const isMobile = useMediaQueryState({
    query: `(max-width: ${variableStyles.smallWidth})`,
  });

  const {
    isOpen,
    getToggleButtonProps,
    getLabelProps,
    getMenuProps,
    highlightedIndex,
    getItemProps,
    openMenu,
  } = useSelect({
    id,
    items,
    itemToString: item => item?.label,
    isOpen: isActiveControlled ? isActive : undefined,
    selectedItem,
    // https://github.com/downshift-js/downshift/issues/638#issuecomment-451770509
    scrollIntoView: (node, rootNode) => {
      if (node === null) {
        return;
      }

      const actions = compute(node, {
        boundary: rootNode,
        block: 'center',
        inline: 'center',
        scrollMode: 'if-needed',
      });
      /* eslint-disable */
      actions.forEach(({ el, top, left }) => {
        el.scrollTop = top;
        el.scrollLeft = left;
      });
      /* eslint-enable */
    },
    onSelectedItemChange: ({ selectedItem: updatedSelectedItem }) => {
      onShouldClose();
      onChange(updatedSelectedItem.value);
    },
  });

  const hasValue = Boolean(value);
  const hasError = error.length > 0;
  const hasCaption = caption.length > 0;

  let statusRightIcon: ReactElement = null;
  // if (hasSucceeded) {
  //   statusRightIcon = (
  //     <span className={styles.successIconWrapper}>
  //       <CheckIcon width={24} height={24} />
  //     </span>
  //   );
  // } else if (hasError) {
  //   statusRightIcon = (
  //     <span className={styles.errorIconWrapper}>
  //       <AlertTriangleIcon width={24} height={24} />
  //     </span>
  //   );
  // } else
  if (isLoading) {
    statusRightIcon = (
      <span className={styles.fetchingIconWrapper}>
        <AnimatedLoader />
      </span>
    );
  }

  const handleSelectChange: React.ChangeEventHandler<
    HTMLSelectElement
  > = event => {
    onChange(event.currentTarget.value);
  };

  function handleToggleButtonBlur() {
    if (!isOpen) {
      selectRef.current.checkValidity();
      if (onBlur) {
        onBlur();
      }
    }
  }

  const handleToggleButtonClick = useCallback(() => {
    onToggleButtonClick();
  }, [onToggleButtonClick]);

  const handleMenuBlur = () => {
    if (onShouldClose) {
      onShouldClose();
    }
  };

  const handleSelectInvalid: React.FormEventHandler<HTMLSelectElement> = () => {
    if (onInvalid) {
      onInvalid({
        id,
        label,
        value: selectedItem,
        validity: selectRef.current.validity,
        name,
      });
    }
  };

  // NOTE - If using isActive, the parent component should listen for the onShouldClose event to set this back to false if it needs to be triggered again.
  useEffect(() => {
    if (!isActiveControlled && isActive) {
      openMenu();
    }
  }, [isActive, isActiveControlled, openMenu]);

  // NOTE - If using isFocused, the parent component should listen for the onBlur event to set this back to false if it needs to be triggered again.
  useEffect(() => {
    if (isFocused) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (buttonRef?.current as any)?.focus({ focusVisible: true });
    }
  }, [isFocused]);

  useEffect(() => {
    const selectWrapperNode = selectWrapperRef.current;
    const menuListNode = menuListRef.current;

    const handleDropdown = () => {
      if (!selectWrapperNode || !menuListNode || isMobileMenuModal) return;

      const {
        top: wrapperTop,
        left: wrapperLeft,
        bottom: wrapperBottom,
        width: selectWidth,
      } = selectWrapperNode.getBoundingClientRect();
      const { height: menuListHeight } = menuListNode.getBoundingClientRect();

      requestAnimationFrame(() => {
        if (selectWidth > 0) {
          menuListNode.style.maxWidth = `${selectWidth}px`;
        }

        menuListNode.style.left = `${wrapperLeft}px`;
        let top = 0;
        if (window.innerHeight < wrapperBottom + menuListHeight + 16) {
          top = wrapperTop - menuListHeight - 8;
        } else {
          if (!selectContainerRef?.current?.closest('.dialogModalWrapper')) {
            top += 8;
          }
          top += wrapperBottom;
        }
        menuListNode.style.top = `${top}px`;
      });
    };

    if (isFixedMenu) {
      let scrollableAncestor;
      if (selectWrapperNode) {
        scrollableAncestor = findScrollableParent(selectWrapperNode);
      }
      if (isOpen) {
        handleDropdown();
        if (scrollableAncestor) {
          scrollableAncestor.addEventListener('scroll', handleDropdown);
        }
        window.addEventListener('resize', handleDropdown);
        window.addEventListener('scroll', handleDropdown);
      } else {
        if (scrollableAncestor) {
          scrollableAncestor.removeEventListener('scroll', handleDropdown);
        }
        window.removeEventListener('resize', handleDropdown);
        window.removeEventListener('scroll', handleDropdown);
      }
    }

    return () => {
      if (selectWrapperNode) {
        const scrollableAncestor = findScrollableParent(selectWrapperNode);
        scrollableAncestor?.removeEventListener('scroll', handleDropdown);
      }
      window.removeEventListener('scroll', handleDropdown);
      window.removeEventListener('resize', handleDropdown);
    };
  }, [isFixedMenu, isMobileMenuModal, isOpen]);

  const useModalMenu = isMobile && isMobileMenuModal;

  const inputMenu = (
    <SelectInputMenu
      isOpen={useModalMenu ? true : isOpen}
      isAlignedRight={isMenuAlignedRight}
      getMenuProps={getMenuProps}
      onBlur={handleMenuBlur}
      ref={menuListRef}
      className={classNames(
        useModalMenu && styles.inputMenuModal,
        styles.inputMenu,
        isFixedMenu && styles.fixedListWrapper,
      )}
    >
      {items.map((item, index) => (
        <SelectInputMenuItem
          key={item.value}
          isHighlighted={highlightedIndex === index}
          {...getItemProps({ item, index })}
          left={item.left}
        >
          {item.label}
        </SelectInputMenuItem>
      ))}
    </SelectInputMenu>
  );

  return (
    <div
      className={classNames(
        styles.selectInput,
        isDisabled && styles.disabled,
        isBorderless && styles.borderless,
        isOpen && styles.active,
        hasValue && styles.filled,
        hasError && styles.errored,
        hasCaption && styles.captioned,
        className,
      )}
      ref={selectContainerRef}
    >
      <div className={styles.container} ref={selectWrapperRef}>
        <button
          type="button"
          {...getToggleButtonProps({
            onBlur: handleToggleButtonBlur,
            onClick: handleToggleButtonClick,
            ref: buttonRef,
          })}
          className={styles.button}
          tabIndex={isDisabled ? -1 : null}
        >
          <div className={styles.buttonContainer}>
            {left && <span className={styles.leftWrapper}>{left}</span>}

            <div className={styles.centerWrapper} title={title}>
              {label?.length > 0 && (
                // disabling because dropshift should be providing the necessary a11y props
                // eslint-disable-next-line jsx-a11y/label-has-associated-control
                <label {...getLabelProps()} className={styles.label}>
                  {label}
                </label>
              )}

              {selectedItem?.label?.length > 0 && (
                <span className={styles.selected}>{selectedItem?.label}</span>
              )}
            </div>

            {!isDisabled && (
              <span className={styles.rightWrapper}>
                {right}
                {statusRightIcon}
                {/* <span className={styles.chevronIconWrapper}>
                  <ChevronDownIcon width={24} height={24} />
                </span> */}
              </span>
            )}
          </div>
        </button>

        <select
          ref={selectRef}
          className={styles.fallbackSelect}
          value={hasValue ? value : DEFAULT_VALUE}
          name={name || id}
          disabled={isDisabled}
          aria-label={label}
          aria-hidden
          onChange={handleSelectChange}
          onInvalid={handleSelectInvalid}
          required={required}
          data-testid={SELECT_DATA_TEST_ID}
        >
          <option value={DEFAULT_VALUE} aria-label={label} disabled>
            {label}
          </option>
          {items.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>

        {(hasError || hasCaption) && (
          <div className={styles.footer}>
            {hasError && !hideErrorMessage && (
              <span className={styles.error}>{error}</span>
            )}
            {hasCaption && <span className={styles.caption}>{caption}</span>}
          </div>
        )}
      </div>

      {useModalMenu ? (
        <EyebrowDialog
          modalConfig={{
            isActive: isOpen,
            isMobileCentered: true,
            isHeadingStatic: true,
            hasDialogWrapperStyles: true,
            hasNoPaddingAroundForBody: true,
          }}
          modalContent={{
            bodyContent: inputMenu,
            headingContent: label,
          }}
          modalClassNames={{
            dialogWrapperClassName: classNames(
              styles.dialogModalWrapper,
              mobileModalPosition && styles[mobileModalPosition],
            ),
            headerWrapperClassName: styles.modalHeader,
            headerWrapperContainerClassName: styles.inputMenuModalContainer,
            bodyWrapperClassName: styles.modalForCombo,
            bodyWrapperContainerScrollClassName: styles.modalContainer,
          }}
        />
      ) : (
        <div className={styles.menuWrapper}>
          {isFixedMenu
            ? createPortal(
                inputMenu,
                selectContainerRef?.current?.closest('.dialogModalWrapper') ??
                  document.body,
              )
            : inputMenu}
        </div>
      )}
    </div>
  );
};

export default Select;
