import { HTMLAttributes } from 'react';

// Define the possible typography variants
export type TagsTypes = 'p' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

export type TypographyVariants =
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
  | 'h6'
  | 'h1Var'
  | 'h2Var'
  | 'h3Var'
  | 'h4Var'
  | 'h5Var'
  | 'h6Var'
  | 'h1StrongVar'
  | 'h2StrongVar'
  | 'h3StrongVar'
  | 'h4StrongVar'
  | 'h5StrongVar'
  | 'h6StrongVar'
  | 'h1Strong'
  | 'h2Strong'
  | 'h3Strong'
  | 'h4Strong'
  | 'h5Strong'
  | 'h6Strong'
  | 'bodyBase'
  | 'bodySmall'
  | 'bodyLargeStrong'
  | 'bodyBaseStrong'
  | 'bodySmallStrong'
  | 'bodyLarge'
  | 'display'
  | 'displayStrong'
  | 'displayStrongVar'
  | 'displayVar'
  | 'buttonLabelXLarge'
  | 'buttonLabelLarge'
  | 'buttonLabelSmall'
  | 'caption'
  | 'captionStrong'
  | 'overline'
  | 'list'
  | 'orderedList'
  | 'ulLarge'
  | 'ulBase'
  | 'ulSmall'
  | 'olLarge'
  | 'olBase'
  | 'olSmall';

// Omitting incompatible attributes
export interface TypographyProps
  extends Omit<
    HTMLAttributes<HTMLHeadingElement | HTMLParagraphElement | HTMLBodyElement>,
    'color'
  > {
  /**
   * To pass the tag which will getting render with the html, for ex: h1, h2..
   */
  tag?: TagsTypes;
  /**
   * To make text bold or strong, use this props
   */
  strong?: boolean;
  /**
   * To pass different body variant such as bodyStrong, bodyLarge based on your desired font size
   */
  variant?: TypographyVariants;
  /**
   * To have the different variant for the heading fonts, see the example below
   */
  isVar?: boolean;
  /**
   * className : to provide class
   */
  className?: string;
}

export type TypographyStyles = {
  [key: string]: string;
};
