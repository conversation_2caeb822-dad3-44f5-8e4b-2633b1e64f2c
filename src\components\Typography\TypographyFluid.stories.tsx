import { ReactElement } from 'react';
import { useArgs } from '@storybook/preview-api';
import type { Meta, StoryObj } from '@storybook/react';
import FluidRoot from '@components/FluidRoot';

import Typography from './Typography';
import { TypographyProps } from './Typography.types';

const Box = ({ m, p, children }) => {
  const style = {
    margin: m && `${m * 8}px`,
    padding: p && `${p * 8}px`,
  };

  return <div style={style}>{children}</div>;
};
interface WrappedTypographyTypes extends TypographyProps {
  heading: string;
}

const WrappedTypography: ({
  tag,
  strong,
  variant,
  children,
  heading,
  ...props
}: WrappedTypographyTypes) => ReactElement = ({
  tag,
  variant,
  children,
  heading,
  ...props
}) => (
  <Box m={1} p={1}>
    <Box m={1} p={1}>
      <div
        style={{
          width: '70%',
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        <div>{heading}</div>
        <div />
      </div>
    </Box>
    <Box m={1} p={1}>
      <Typography tag={tag} variant={variant} {...props}>
        {children}
      </Typography>
    </Box>
  </Box>
);

/**
 *
 * <br/>

## Overview

The `Typography` component provides a set of typography specifications for various text elements used in the application. It includes specifications for headings, body text.

## Usage

Import the component into your React application:

```jsx
import { Typography } from '@peddleon/ped-ux-react-library';```

This component is wrapped around the fluid layout. 

 */
const meta: Meta<typeof Typography> = {
  component: Typography,
  title: 'Core/Typography Fluid',
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: [
        'h1',
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'h1Var',
        'h2Var',
        'h3Var',
        'h4Var',
        'h5Var',
        'h6Var',
        'h1StrongVar',
        'h2StrongVar',
        'h3StrongVar',
        'h4StrongVar',
        'h5StrongVar',
        'h6StrongVar',
        'h1Strong',
        'h2Strong',
        'h3Strong',
        'h4Strong',
        'h5Strong',
        'h6Strong',
        'bodyBase',
        'bodySmall',
        'bodyLargeStrong',
        'bodyBaseStrong',
        'bodySmallStrong',
        'bodyLarge',
        'display',
        'displayStrong',
        'displayStrongVar',
        'displayVar',
      ],
    },
  },
};

export default meta;
type Story = StoryObj<typeof Typography>;

export const Basic: Story = {
  argTypes: {
    strong: { type: 'boolean' },
    isVar: { type: 'boolean' },
  },
  args: {
    tag: 'h1',
    children: 'Heading',
    strong: false,
  },
  render: () => {
    const [{ children, tag, strong, ...props }] = useArgs();
    return (
      <FluidRoot isFluid>
        <Typography tag={tag} strong={strong} {...props}>
          {children}
        </Typography>
      </FluidRoot>
    );
  },
};

export const Heading: Story = {
  args: {
    children: 'Heading!',
  },
  render: () => {
    const [{ children }] = useArgs();

    return (
      <div>
        <FluidRoot isFluid>
          <Box m={1} p={1}>
            <WrappedTypography heading="Display" variant="display" tag="h1">
              {children}
            </WrappedTypography>
            <WrappedTypography heading="Heading 1" tag="h1">
              {children}
            </WrappedTypography>
            <WrappedTypography heading="Heading 2" tag="h2">
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 3" tag="h3">
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 4" tag="h4">
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 5" tag="h5">
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 6" tag="h6">
              {children}
            </WrappedTypography>
          </Box>
        </FluidRoot>
      </div>
    );
  },
};

export const HeadingStrong: Story = {
  args: {
    children: 'Heading!',
  },
  render: () => {
    const [{ children }] = useArgs();
    return (
      <div>
        <FluidRoot isFluid>
          <Box m={1} p={1}>
            <WrappedTypography
              heading="Display"
              variant="displayStrong"
              tag="h1"
            >
              {children}
            </WrappedTypography>
            <WrappedTypography heading="Heading 1 " tag="h1" strong>
              {children}
            </WrappedTypography>
            <WrappedTypography heading="Heading 2 " tag="h2" strong>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 3 " tag="h3" strong>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 4 " tag="h4" strong>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 5 " tag="h5" strong>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 6 " tag="h6" strong>
              {children}
            </WrappedTypography>
          </Box>
        </FluidRoot>
      </div>
    );
  },
};

export const HeadingVar: Story = {
  args: {
    children: 'Heading!',
  },
  render: () => {
    const [{ children }] = useArgs();
    return (
      <div>
        <FluidRoot isFluid>
          <Box m={1} p={1}>
            <WrappedTypography heading="Display" tag="h1" variant="displayVar">
              {children}
            </WrappedTypography>
            <WrappedTypography heading="Heading 1 " tag="h1" isVar>
              {children}
            </WrappedTypography>
            <WrappedTypography heading="Heading 2 " tag="h2" isVar>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 3 " tag="h3" isVar>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 4 " tag="h4" isVar>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 5 " tag="h5" isVar>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 6 " tag="h6" isVar>
              {children}
            </WrappedTypography>
          </Box>
        </FluidRoot>
      </div>
    );
  },
};

export const HeadingVarStrong: Story = {
  args: {
    children: 'Heading!',
  },
  render: () => {
    const [{ children }] = useArgs();
    return (
      <div>
        <FluidRoot isFluid>
          <Box m={1} p={1}>
            <WrappedTypography
              heading="Display"
              tag="h1"
              variant="displayStrongVar"
            >
              {children}
            </WrappedTypography>
            <WrappedTypography heading="Heading 1 " tag="h1" strong isVar>
              {children}
            </WrappedTypography>
            <WrappedTypography heading="Heading 2 " tag="h2" strong isVar>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 3 " tag="h3" strong isVar>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 4 " tag="h4" strong isVar>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 5" tag="h5" strong isVar>
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography heading="Heading 6 " tag="h6" strong isVar>
              {children}
            </WrappedTypography>
          </Box>
        </FluidRoot>
      </div>
    );
  },
};

export const Body: Story = {
  args: {
    children: `Peddle body!Lorem ipsum dolor sit amet consectetur adipisicing elit. Maxime
    amet, quam dolores soluta, nesciunt alias, ipsam voluptas distinctio
    quaerat at repudiandae iusto voluptatum!`,
  },
  render: () => {
    const [{ children }] = useArgs();
    return (
      <div>
        <FluidRoot isFluid>
          <Box m={1} p={1}>
            <WrappedTypography
              heading="Body / Base"
              tag="p"
              variant="bodyLarge"
            >
              {children}
            </WrappedTypography>
            <WrappedTypography tag="p" variant="bodyBase" heading="Body / Base">
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography
              tag="p"
              variant="bodySmall"
              heading="Body / Small "
            >
              {children}
            </WrappedTypography>{' '}
          </Box>
        </FluidRoot>
      </div>
    );
  },
};

export const BodyStrong: Story = {
  args: {
    children: `Lorem ipsum dolor sit amet consectetur adipisicing elit. Maxime
    amet, quam dolores soluta, nesciunt alias, ipsam voluptas distinctio
    quaerat at repudiandae iusto voluptatum!`,
  },
  render: () => {
    const [{ children }] = useArgs();
    return (
      <div>
        <FluidRoot isFluid>
          <Box m={1} p={1}>
            <WrappedTypography
              tag="p"
              heading="Body / Base"
              variant="bodyLargeStrong"
            >
              {children}
            </WrappedTypography>
            <WrappedTypography
              tag="p"
              variant="bodyBaseStrong"
              heading="Body / Base"
            >
              {children}
            </WrappedTypography>{' '}
            <WrappedTypography
              tag="p"
              variant="bodySmallStrong"
              heading="Body / Small "
            >
              {children}
            </WrappedTypography>{' '}
          </Box>
        </FluidRoot>
      </div>
    );
  },
};
