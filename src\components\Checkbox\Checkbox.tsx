// import { <PERSON>act<PERSON><PERSON><PERSON><PERSON><PERSON>, ReactNode } from 'react';
// import { CheckIcon } from '@peddleon/ped-ux-react-icons';

// import styles from './Checkbox.module.css';

// export type SimpleTheme = 'primaryTheme' | 'secondary';
// export interface CheckableInputProps {
//   /**
//    * A unique identifier for the HTML element.
//    */
//   id?: string;
//   /**
//    * The label associated with the checkbox.
//    */
//   label?: ReactNode;
//   /**
//    * The name attribute for the checkbox, used for form submission.
//    */
//   name?: string;
//   /**
//    * The theme of the checkbox, which can be either 'primaryTheme' or 'secondary'.
//    */
//   theme?: SimpleTheme;
//   /**
//    * Indicates whether the checkbox is checked or not.
//    */
//   isChecked: boolean;
//   /**
//    * Indicates whether the checkbox is disabled or not.
//    */
//   isDisabled?: boolean;
//   /**
//    * The tab index of the checkbox, determines its position in the tab order.
//    */
//   tabIndex?: number;
//   /**
//    * A callback function to be invoked when the checkbox state changes.
//    */
//   onChange?: ReactEventHandler<HTMLInputElement>;
// }

// export interface CheckboxProps extends CheckableInputProps {
//   verticalAlign?: 'top' | 'middle' | 'bottom';
// }
// const Checkbox = ({
//   id,
//   label = null,
//   name = null,
//   theme = 'primaryTheme',
//   isChecked = undefined,
//   isDisabled = false,
//   tabIndex = null,
//   verticalAlign = 'middle',
//   onChange = null,
// }: CheckboxProps) => (
//   <label
//     className={[
//       styles.checkbox,
//       isChecked && styles.checked,
//       isDisabled && styles.disabled,
//       !onChange && styles.disabledPointerEvents,
//       styles[verticalAlign],
//       styles[theme],
//     ]
//       .filter(Boolean)
//       .join(' ')}
//     htmlFor={id}
//   >
//     <span className={styles.indicator}>
//       <span className={styles.checkIconWrapper}>
//         <CheckIcon height="1em" width="1em" />
//       </span>
//     </span>

//     {label && <span className={styles.label}>{label}</span>}

//     <input
//       id={id}
//       type="checkbox"
//       name={name || id}
//       checked={isChecked}
//       disabled={isDisabled}
//       tabIndex={tabIndex}
//       onChange={onChange}
//     />
//   </label>
// );

// export default Checkbox;
