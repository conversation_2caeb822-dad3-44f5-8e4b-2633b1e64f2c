/* eslint-disable react/destructuring-assignment */
import React from 'react';
import { Meta } from '@storybook/react';
import Button from '@components/Button';
import { ToastContextProvider } from '@contexts/ToastContext';
import useToast from '@hooks/useToast';
import variableStyles from '@styles/variables.module.css';

import { ToastTypes } from './NotificationModal.types';

/**
 * The Toast component provides a customizable auto close alert box for displaying messages or notifications to users.
 * It allows for easy customization of themes, content, and optional cancellation functionality.
 *
 * ## Overview
 *
 * The Toast component is designed to display auto close informative messages or notifications to users in a visually appealing way. It offers flexibility in theming, allowing you to match the alert style with your application's design.
 *
 * ## Usage
 *
 * To use the Toast component in your React application, import it ToastContextProvider and wrap it in root component and when you want to use Toast import useToast hook to display it.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { ToastContextProvider } from '@peddleon/ped-ux-react-library';
 * import useToast from '@hooks/useToast';
 * ```
 *
 * Then, use the Alert component and pass relevant props to customize its appearance and behavior:
 *
 * ```jsx
 *  const toast = useToast();
 *
 *  toast({
 *    theme,
 *    body,
 *    isActive: true,
 *  });
 *
 *  <ToastContextProvider
 *     position={props.position}
 *     isCancellable={props.isCancellable}
 *     timer={props.timer}
 *  >
 *    <ToastWrapper {...props} />
 *  </ToastContextProvider>
 * ```
 *
 * This will render an alert box with a success theme, displaying the provided message. It also includes a cancel button with animation effects and triggers a callback function when clicked.
 */

const meta: Meta<typeof Button> = {
  title: 'Components/Toast',
  tags: ['autodocs'],
  component: ToastContextProvider,
};

const ToastStoryWrapper = ({ body, theme }: ToastTypes) => {
  const toast = useToast();

  const triggerNotification = () => {
    toast({
      theme,
      body,
      isActive: true,
    });
  };

  return (
    <Button
      label="Show / Hide"
      size="small"
      theme="primary"
      onClick={() => triggerNotification()}
    />
  );
};

export const ToastStory = (props: ToastTypes) => (
  <div
    style={{
      padding: variableStyles.oneSpace,
    }}
  >
    <ToastContextProvider
      position={props.position}
      isCancellable={props.isCancellable}
      timer={props.timer}
    >
      <ToastStoryWrapper {...props} />
    </ToastContextProvider>
  </div>
);

ToastStory.argTypes = {
  position: {
    control: 'select',
    type: 'select',
    options: [
      'bottomRight',
      'bottomLeft',
      'bottomCenter',
      'topRight',
      'topLeft',
      'topCenter',
    ],
    description: 'Specify toast position .',
  },
  theme: {
    control: 'select',
    type: 'select',
    options: [
      'primary',
      'primaryInverted',
      'primaryFaded',
      'danger',
      'dangerInverted',
      'dangerFaded',
      'success',
      'successInverted',
      'successFaded',
      'warning',
      'warningInverted',
      'warningFaded',
    ],
    description: 'Specify toast position .',
  },
};

ToastStory.args = {
  position: 'topCenter',
  theme: 'primary',
  body: `We'd like to verify your VIN`,
  isCancellable: true,
  timer: 3000,
};

const ToastSheetWrapper = () => {
  const toast = useToast();

  const triggerNotification = ({ theme, body }) => {
    toast({
      theme,
      body,
      isActive: true,
    });
  };

  return (
    <div style={{ display: 'flex', gap: '10px' }}>
      <Button
        label="Show / Hide"
        size="small"
        theme="primary"
        onClick={() =>
          triggerNotification({ theme: 'primary', body: 'Primary toast' })
        }
      />
      <Button
        label="Show / Hide"
        size="small"
        theme="danger"
        onClick={() =>
          triggerNotification({ theme: 'danger', body: 'Danger toast' })
        }
      />
      <Button
        label="Show / Hide"
        size="small"
        theme="success"
        onClick={() =>
          triggerNotification({ theme: 'success', body: 'Success toast' })
        }
      />
    </div>
  );
};

export const ToastSheet = () => (
  <div
    style={{
      padding: variableStyles.oneSpace,
    }}
  >
    <ToastContextProvider position="topCenter" isCancellable timer={3000}>
      <ToastSheetWrapper />
    </ToastContextProvider>
  </div>
);

ToastSheet.args = {};

export default meta;
