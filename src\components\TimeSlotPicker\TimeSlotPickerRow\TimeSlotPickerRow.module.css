@value typography: "../../../styles/typography.module.css";
@value variables: "../../../styles/variables.module.css";
@value oneSpace, twoSpace, fourSpace, fiveSpace, black10, smallWidth from variables;

.row {
  padding-top: fiveSpace;
  padding-bottom: fiveSpace;
  list-style: none;
  &:not(:last-of-type) {
    border-bottom: solid 1px black10;
  }
}

.container {
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
}

.slots {
  list-style: none;
  flex: 1;
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-start;
  align-items: center;
  padding-left: fourSpace;
  width: 100%;
  border-left: solid 1px black10;

  @media (max-width: smallWidth) {
    padding-left: 0;
    border-left: none;
  }
}

.slot {
  margin: oneSpace;

  @media (max-width: smallWidth) {
    width: 100%;
    margin-left: 0;
    margin-right: 0;

    & > * {
      width: 100%;
    }
  }
}
