import { ElementType, ReactNode, SyntheticEvent } from 'react';
import { SimpleTheme } from '@libtypes/common';

export type NotebarTheme = SimpleTheme | 'grey';

export type EmptyStateSize = 'small' | 'medium';
export type ButtonSize = 'xLarge' | 'large' | 'small' | 'xSmall';
export type IconButtonSize = 'large' | 'small';

export type Theme =
  | SimpleTheme
  | 'display'
  | 'darkInverted'
  | 'lightInverted'
  | 'displayInverted';

export type ButtonTheme =
  | SimpleTheme
  | 'darkInverted'
  | 'lightInverted'
  | 'grey'
  | 'greyInverted'
  | 'transparent'
  | 'transparentInverted'
  | 'primary'
  | 'primaryFaded'
  | 'primaryInverted'
  | 'danger'
  | 'dangerFaded'
  | 'dangerInverted'
  | 'success'
  | 'successInverted'
  | 'successFaded'
  | 'warning'
  | 'warningInverted'
  | 'warningFaded';

export type TagTheme = ButtonTheme;

export type IconButtonTheme = ButtonTheme | 'outline';

export type ProgressIndicatorTheme =
  | 'primary'
  | 'danger'
  | 'warning'
  | 'success';

export type PathwayIllustrationVariant =
  | SimpleTheme
  | 'variant1'
  | 'variant2'
  | 'variant3'
  | 'variant4'
  | 'variant5'
  | 'variant6'
  | 'variant7'
  | 'variant8'
  | 'variant9'
  | 'variant10'
  | 'variant11'
  | 'variant12'
  | 'variant13'
  | 'variant14'
  | 'variant15'
  | 'variant16'
  | 'variant17'
  | 'variant18'
  | 'variant19'
  | 'variant20'
  | 'variant21'
  | 'variant22';

export type IllustrationThemePrimary = 'primary' | 'inverted';

export type PriceTagColor = 'success' | 'primary' | 'black';

export type StatusIndicatorTheme =
  | 'dark'
  | 'primary'
  | 'danger'
  | 'success'
  | 'warning'
  | 'neutralDark'
  | 'neutralDarker';

export type DialogButtonTheme = 'warning' | 'grey';

export interface ButtonProps {
  icon?: ReactNode;
  /** The label text for the button */
  label?: string;
  /** The content to be displayed on the left side of the button */
  left?: ReactNode;
  /** The content to be displayed on the right side of the button */
  right?: ReactNode;
  /** The size of the button */
  size?: ButtonSize;
  /** The theme of the button */
  theme?: ButtonTheme;
  /** The type of the button (e.g., 'submit', 'reset', 'button') */
  type?: 'submit' | 'reset' | 'button';
  /** Specifies whether the button should expand to take 100% of the width of its parent container. */
  isFullWidth?: boolean;
  /** Specifies if the button is in a fetching state */
  isLoading?: boolean;
  /** Specifies if the button is disabled */
  isDisabled?: boolean;
  /** Specifies if the button should have a square shape */
  isSquare?: boolean;
  /** Function to be called when the button is clicked */
  onClick?: (event: SyntheticEvent<Element, Event>) => void;
  /** The ID of the button */
  id?: string;
  /** Additional CSS class for styling */
  className?: string;
  /** The tab index of the button */
  tabIndex?: number;
  /** ARIA label for accessibility */
  ariaLabel?: string;
  /** Custom data attribute for tracking */
  dataId?: string;
  /** Specifies if React should suppress hydration warnings */
  suppressHydrationWarning?: boolean;
  /** Function to be called when the mouse is pressed on the button */
  onMouseDown?: (event: SyntheticEvent<Element, Event>) => void;
  /** Function to be called when the mouse is released from the button */
  onMouseUp?: (event: SyntheticEvent<Element, Event>) => void;
  /** Function to be called when a touch event starts on the button */
  onTouchStart?: (event: SyntheticEvent<Element, Event>) => void;
  /** Function to be called when a touch event ends on the button */
  onTouchEnd?: (event: SyntheticEvent<Element, Event>) => void;
  /** Component to be overriden by button */
  component?: ElementType;
  /** The content to be rendered inside the button */
  children?: ReactNode;
  /** href: to use button as link */
  href?: string;
}
