import {
  TABLE_ASC_LABEL_DATE,
  TABLE_ASC_LABEL_NUMBER,
  TABLE_ASC_LABEL_STRING,
  TABLE_DESC_LABEL_DATE,
  TABLE_DESC_LABEL_NUMBER,
  TABLE_DESC_LABEL_STRING,
} from '@constants/components';

export const ascOrder = (type = '') => {
  switch (type) {
    case 'string':
      return TABLE_ASC_LABEL_STRING;

    case 'number':
      return TABLE_ASC_LABEL_NUMBER;

    case 'date':
      return TABLE_ASC_LABEL_DATE;

    default:
      return TABLE_ASC_LABEL_STRING;
  }
};

export const descOrder = (type = '') => {
  switch (type) {
    case 'string':
      return TABLE_DESC_LABEL_STRING;

    case 'number':
      return TABLE_DESC_LABEL_NUMBER;

    case 'date':
      return TABLE_DESC_LABEL_DATE;

    default:
      return TABLE_DESC_LABEL_STRING;
  }
};
