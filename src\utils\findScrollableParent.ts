const findScrollableParent = element => {
  if (!element) return null;

  let parent = element.parentElement;
  while (parent) {
    const { overflowY } = window.getComputedStyle(parent);
    const isScrollable = overflowY !== 'visible' && overflowY !== 'hidden';
    if (isScrollable && parent.scrollHeight > parent.clientHeight) {
      return parent;
    }
    parent = parent.parentElement;
  }
  return document.body; // Fallback to body if no scrollable parent is found
};

export default findScrollableParent;
