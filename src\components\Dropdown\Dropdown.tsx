import React, {
  ReactNode,
  SyntheticEvent,
  useEffect,
  useRef,
  useState,
} from 'react';
import TooltipRC from 'rc-tooltip';
import Menu from '@components/Dropdown/Menu';
import MenuItem from '@components/Dropdown/MenuItem';
import LinkComponent from '@components/NavigationBar/LinkComponent';
import useClickOutside from '@hooks/useClickOutside';
import classNames from '@utils/classNames';

import styles from './Dropdown.module.css';

import 'rc-tooltip/assets/bootstrap.css';

export interface ItemType {
  lists?: {
    label: string;
    id?: string;
    link?: string;
    left?: ReactNode;
    right?: ReactNode;
  }[];
}

export interface DropdownProps {
  /** The content inside the dropdown. */
  children: ReactNode;
  /** An object containing lists of menu items, each with a label. */
  item: ItemType;
  /** Unique identifier for the dropdown. */
  id: string;
  /** Placement of the dropdown (default: 'bottom'). */
  place?: string;
  /** To Open dropdown upon click */
  openOnClick?: boolean;
  /** Callback for the list item click */
  onClick?: (
    event: SyntheticEvent<Element, Event>,
    id: string,
    item?: { label: string; id?: string; link?: string },
  ) => void;
  /** extraStyles: for menu and menu list override */
  extraStyles?: {
    menu?: string;
    menuListItem?: string;
  };
  /** delay to visible tooltip/dropdown */
  delayShow?: number;
  /**
   * useNativeDropdown: custome js dropdown
   */
  useNativeDropdown?: boolean;
}

const Dropdown: React.FC<DropdownProps> = ({
  children,
  item,
  place = 'bottom',
  openOnClick = false,
  onClick = () => {},
  extraStyles = {},
  delayShow = 200,
  useNativeDropdown = false,
  id,
  ...props
}) => {
  const [isMenuVisible, setMenuVisible] = useState(false);
  const tooltipRef = useRef(null);
  const menuRef = useRef(null);
  const [visible, setVisible] = useState(false);

  useClickOutside(menuRef, () => {
    if (!openOnClick) return;
    setTimeout(() => {
      tooltipRef?.current?.close();
    }, delayShow);
    setMenuVisible(false);
  });

  useEffect(() => {
    setTimeout(() => {
      try {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-expect-error
        document.querySelector(`.${id}-custom-tooltip`).style.opacity = visible
          ? 1
          : '';
      } catch (error) {
        // element not found
      }
    }, 200);
  }, [id, visible]);

  const handleItemClick = (
    link: { label: string; id?: string; link?: string; left?: ReactNode },
    event,
  ) => {
    setVisible(false);
    if (tooltipRef.current && tooltipRef.current?.isOpen) {
      setMenuVisible(false);
      onClick(event, link.id);
      return;
    }
    onClick(event, link.id, link);
    setMenuVisible(false);
  };

  useEffect(() => {
    const handleKeyDown = event => {
      if (event.key === 'Escape') {
        setVisible(false);
        setMenuVisible(false);
      }
    };

    const handleScroll = () => {
      setVisible(false);
      setMenuVisible(false);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('scroll', handleScroll);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const hasMenu = item?.lists?.length;

  const menuList = (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events
    <div
      onClick={() => {
        setVisible(false);
      }}
      className={classNames(styles.tooltipContainer)}
      ref={menuRef}
      role="button"
      tabIndex={0}
    >
      <Menu
        className={classNames(styles.primaryLinksWrapper, extraStyles.menu)}
        isOpen
      >
        {item.lists?.map((link, index) => (
          <MenuItem
            {...link}
            key={link.id || `index${index + 1}`}
            tabIndex={0}
            onClick={event => {
              handleItemClick(link, event);
            }}
            aria-label={link.label}
            extraStyles={{
              root: classNames(extraStyles.menuListItem),
              menuListStyles: styles.menuListItem,
            }}
            left={link.left}
            right={link.right}
          >
            <LinkComponent {...link} className={styles.menuItemLink}>
              {link.label}
            </LinkComponent>
          </MenuItem>
        ))}
      </Menu>
    </div>
  );

  if (useNativeDropdown) {
    return (
      <div
        onMouseEnter={() => setMenuVisible(true)}
        onMouseLeave={() => setMenuVisible(false)}
        className={styles.container}
      >
        <div className={styles.children}>{children}</div>
        {isMenuVisible && hasMenu && (
          <div className={styles.popupLayout} ref={menuRef}>
            {menuList}
          </div>
        )}
      </div>
    );
  }

  const onVisibleChange = popupVisible => {
    setVisible(popupVisible);
  };

  return (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
    <div onClick={event => event.stopPropagation()}>
      <div className={visible ? styles.visibleStyles : ''}>
        <TooltipRC
          visible={visible}
          destroyTooltipOnHide
          motion={{
            motionName: 'rc-tooltip-zoom-custom',
            motionAppear: true,
          }}
          placement={place}
          onVisibleChange={onVisibleChange}
          trigger={openOnClick ? 'click' : 'hover'}
          overlay={menuList}
          showArrow={false}
          overlayClassName={`${id}-custom-tooltip`}
          {...props}
        >
          <div className={styles.childWrapper}> {children}</div>
        </TooltipRC>
      </div>
    </div>
  );
};

export default Dropdown;
