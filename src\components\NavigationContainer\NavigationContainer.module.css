@value variables: "../../styles/variables.module.css";
@value frame: "../../components/Frame/Frame.module.css";
@value largeWidth, threeSpace, ease-out-expo, buoyantZIndex from variables;
@value mobileContainerHeight from frame;

.navigationWrapper {
  position: fixed;
  z-index: buoyantZIndex;
  width: 100%;
  transition: all 1s ease-out-expo;

  &.isNavigationSticky {
    background-color: transparent;
    border-bottom-left-radius: threeSpace;
    border-bottom-right-radius: threeSpace;
  }

  &.isNavigationHidden {
    transform: translateY(-100%);
  }

  &.isAdminFrameShown,
  &.environment {
    padding-top: mobileContainerHeight;

    @media (min-width: largeWidth) {
      padding-top: 0;
    }
  }
}
