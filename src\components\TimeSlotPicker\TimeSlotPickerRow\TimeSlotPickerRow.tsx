import { forwardRef } from 'react';
import Radio from '@components/Radio';
import { TIME_SLOT_PICKER_ROW } from '@constants/dataTestId';
import {
  formatDate,
  formatTimeRange,
  generateHref,
  getTimeZone,
} from '@utils/date';

import NoPickupWindow from '../NoPickupWindow/NoPickupWindow';
import TimeSlotDateView from '../TimeSlotDateView/TimeSlotDateView';
import { TimeSlotPickerRowPropTypes } from '../TimeSlotPicker.types';
import styles from './TimeSlotPickerRow.module.css';

const TimeSlotPickerRow = forwardRef<HTMLLIElement, TimeSlotPickerRowPropTypes>(
  ({ dataTestId, date, timeZone, value, items, onSelect, isDisabled }, ref) => (
    <li
      id={generateHref({ date }).replace(/#/g, '')}
      className={styles.row}
      ref={ref}
      data-date={formatDate(date)}
    >
      <div className={styles.container}>
        <TimeSlotDateView date={date} />
        <ul data-id={TIME_SLOT_PICKER_ROW} className={styles.slots}>
          {items.length === 0 && (
            <NoPickupWindow displayText="Darn, no pickup windows available this day" />
          )}

          {items.map(
            ({ id, startAt, endAt, timeStartAt, timeEndAt }, index) => (
              <li
                data-id={`${dataTestId}_slot_${index}`}
                key={id}
                className={styles.slot}
              >
                <Radio
                  dataTestId={`${dataTestId}_slot_${index}_button`}
                  id={id}
                  label={`${formatTimeRange(startAt, endAt)} ${getTimeZone(
                    timeZone,
                  )}`}
                  name={`timeSlotPicker-${id}`}
                  isChecked={id === value}
                  onChange={() =>
                    onSelect({ id, startAt, endAt, timeStartAt, timeEndAt })
                  }
                  isDisabled={isDisabled}
                  value={id}
                  isInButtonShape
                />
              </li>
            ),
          )}
        </ul>
      </div>
    </li>
  ),
);

export default TimeSlotPickerRow;
