import { ChangeEvent, useState } from 'react';
import { render, screen } from '@testing-library/react';
// eslint-disable-next-line import/no-extraneous-dependencies
import userEvent from '@testing-library/user-event';
import { TEXTAREA_DATA_TEST_ID } from '@constants/dataTestId';

import Textarea from './Textarea';

// Mocking the scrollIntoView function
window.HTMLElement.prototype.scrollIntoView = function mock() {};

// Mocking the useDebouncedCallback hook
jest.mock('@hooks/useDebouncedCallback', () => callback => callback);

// Component wrapper for testing the TextInput component
const TextareaWrapper = ({
  value = '',
  label = 'Label',
  placeholder = 'Placeholder',
  ...props
}) => {
  const [state, setState] = useState(value);
  return (
    <Textarea
      label={label}
      placeholder={placeholder}
      value={state}
      onChange={(event: ChangeEvent<HTMLTextAreaElement>) =>
        setState(event.target.value)
      }
      {...props}
    />
  );
};

describe('TextInput Component', () => {
  it('should render the text input with default placeholder, label, value, etc.', async () => {
    // Render the component with some props
    render(<TextareaWrapper />);

    // Expect the placeholder and label to be in the document
    expect(screen.getByPlaceholderText('Placeholder')).toBeInTheDocument();
    expect(screen.getByText('Label')).toBeInTheDocument();

    // Expect the input to be visible in the browser
    expect(screen.getByTestId(TEXTAREA_DATA_TEST_ID)).toBeVisible();
  });

  it('renders with a preselected value', async () => {
    // Render the component with a preselected value
    render(<TextareaWrapper value="peddle" />);

    const input = screen.getByTestId(TEXTAREA_DATA_TEST_ID);

    // Expect the input to have 'peddle' value set by default
    expect(input).toHaveValue('peddle');
  });

  it('should trigger validation', async () => {
    // Render the component with a maximum length and error message
    const errorMessage = 'Input must have a maximum of 10 characters';
    render(<TextareaWrapper maxLength={10} error={errorMessage} />);

    const input = screen.getByTestId(TEXTAREA_DATA_TEST_ID);

    // Type 'The SelectInput component' which exceeds the maximum length
    await userEvent.type(input, 'The SelectInput component');

    // Expect the error message to be displayed with the 'error' class
    expect(screen.getByText(errorMessage)).toHaveClass('error');
  });

  it('should render with provided props', () => {
    // Render the component with wrap, resize, and rows props
    render(<TextareaWrapper wrap="soft" resize="horizontal" rows={8} />);

    const textarea = screen.getByTestId(TEXTAREA_DATA_TEST_ID);

    // Expect the textarea to have the provided wrap, resize, and rows props
    expect(textarea).toHaveAttribute('wrap', 'soft');
    expect(textarea).toHaveStyle('resize: horizontal');
    expect(textarea).toHaveAttribute('rows', '8');
  });
});
