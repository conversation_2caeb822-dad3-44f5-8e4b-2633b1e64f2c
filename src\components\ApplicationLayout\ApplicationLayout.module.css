@value variables: "../../styles/variables.module.css";
@value frame: "../../components/Frame/Frame.module.css";
@value utility: "../../styles/utility.module.css";
@value responsivePageGutters from utility;
@value twoSpace, pageMaxWidth, smallWidth, largeWidth, ease-out-expo, swingTo, topZIndex from variables;
@value responsiveAdditivePageGutters from utility;

.layout {
  /* overridden by inline style */
  --backgroundColor: white;
  --mobileBackgroundColor: white;
  --navigationHeight: 108px;

  height: 100%;
  width: 100%;
  display: flex;
  flex-flow: column;
  overflow-y: auto;
  background-color: var(--backgroundColor);

  @media (max-width: smallWidth) {
    --navigationHeight: 80px;
    background-color: var(--mobileBackgroundColor);
  }
  &.backgroundTransition {
    transition: background-color 0.5s ease-out-expo;
  }
}

.isOverflowHidden {
  overflow: hidden;
}

.navigationWrapper {
  flex: 0 0 auto;
  z-index: topZIndex;
  position: relative;
}

.isNavigationFloating {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.childrenWrapper {
  width: 100%;
  height: 100%;
  border-top-right-radius: twoSpace;
  border-top-left-radius: twoSpace;

  @media (max-width: smallWidth) {
    background: none;
  }
}

.applicationRoot {
  margin-top: var(--navigationHeight);
  min-height: calc(100vh - var(--navigationHeight));
  border-top-right-radius: twoSpace;
  width: 100%;
  border-top-left-radius: twoSpace;

  @media (max-width: smallWidth) {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    margin-top: 78px !important;
    min-height: calc(100vh - 78px) !important;
  }
}

.applicationRootAdminFrame {
  @media (max-width: smallWidth) {
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    margin-top: 136px !important;
    min-height: calc(100vh - 136px) !important;
  }
}

.stickyApplicationRoot {
  margin-top: var(--navigationHeight);
  overflow: auto;
  height: calc(100vh - var(--navigationHeight));
}
