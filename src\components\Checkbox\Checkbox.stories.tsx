import React, { useState } from 'react';
import { Meta } from '@storybook/react';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

import Checkbox from './Checkbox';

interface CheckboxSheetProps {
  label: string;
}

/**
 * <br/>
 *
 * ## Overview
 *
 * The `Checkbox` component provides a customizable checkbox input for use in forms or UI components. It allows users to select or deselect options by clicking on a checkbox.
 *
 * ## Usage
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import Checkbox from './Checkbox';
 * ```
 *
 * Use the `Checkbox` component within your JSX markup, providing necessary props like `id`, `label`, `isChecked`, and `onChange`:
 *
 * ```jsx
 * <Checkbox
 *   id="exampleCheckbox"
 *   label="Example Checkbox"
 *   isChecked={isChecked}
 *   onChange={handleChange}
 * />
 * ```
 *
 * Optionally, you can provide additional props like `theme`, `isDisabled`, and `tabIndex` for customization.
 *
 * Implement event handling logic for the `onChange` event to respond to checkbox state changes.
 *
 * Use state management to track the checkbox's checked state if needed.
 *
 * Customize the appearance and behavior of the `Checkbox` component according to your application's requirements.
 */

const meta: Meta<typeof Checkbox> = {
  title: 'Components/Inputs/Checkboxes',
  tags: ['autodocs'],
  component: Checkbox,
};

export const CheckboxSheet = ({ label }: CheckboxSheetProps) => {
  const [state, setState] = useState({
    standard: false,
    withLabel: false,
  });

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <ComponentGrid countColumns={4}>
        <ComponentTile label="Primary">
          <Checkbox
            id="primary"
            isChecked={state.standard}
            onChange={event =>
              setState({
                ...state,
                standard: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Primary / Disabled">
          <Checkbox
            id="primary-disabled"
            isChecked={state.standard}
            isDisabled
            onChange={event =>
              setState({
                ...state,
                standard: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Primary / Labelled">
          <Checkbox
            id="primary-labelled"
            label={label}
            isChecked={state.withLabel}
            onChange={event =>
              setState({
                ...state,
                withLabel: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Primary / Labelled / Disabled">
          <Checkbox
            id="primary-labelled-disabled"
            label={label}
            isChecked={state.withLabel}
            onChange={event =>
              setState({
                ...state,
                withLabel: (event.target as HTMLInputElement).checked,
              })
            }
            isDisabled
          />
        </ComponentTile>

        <ComponentTile label="Secondary">
          <Checkbox
            id="secondary"
            theme="secondary"
            isChecked={state.standard}
            onChange={event =>
              setState({
                ...state,
                standard: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Secondary / Disabled">
          <Checkbox
            id="secondary-disabled"
            theme="secondary"
            isDisabled
            isChecked={state.standard}
            onChange={event =>
              setState({
                ...state,
                standard: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Secondary / Labelled">
          <Checkbox
            id="secondary-labelled"
            label={label}
            theme="secondary"
            isChecked={state.withLabel}
            onChange={event =>
              setState({
                ...state,
                withLabel: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Secondary / Labelled / Disabled">
          <Checkbox
            id="secondary-labelled-disabled"
            label={label}
            theme="secondary"
            isChecked={state.withLabel}
            onChange={event =>
              setState({
                ...state,
                withLabel: (event.target as HTMLInputElement).checked,
              })
            }
            isDisabled
          />
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
};
CheckboxSheet.args = {
  label: 'Label',
};

export default meta;
