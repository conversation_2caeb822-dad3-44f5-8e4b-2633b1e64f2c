import React, { CSSProperties, FC } from 'react';
// import { EyeIcon, EyeOffIcon } from '@peddleon/ped-ux-react-icons';
import Button from '@components/Button';
import { ButtonTheme } from '@components/Button/Button.types';
import { EMPTY_TABLE_VALUE } from '@constants/common';
import classNames from '@utils/classNames';

import styles from './TableText.module.css';

interface TableTextMaskedProps {
  isInverted?: boolean;
  isActive?: boolean;
  customStyle?: CSSProperties;
  className?: string;
  label: string;
  hasMaskedText: boolean;
  maskedTextFn: () => string;
  handleMasked: (event: React.MouseEvent<HTMLButtonElement>) => void;
  iconButtonTheme?: ButtonTheme;
}

const TableTextMasked: FC<TableTextMaskedProps> = ({
  isInverted,
  isActive,
  customStyle,
  className,
  label = EMPTY_TABLE_VALUE,
  hasMaskedText,
  maskedTextFn,
  handleMasked,
  iconButtonTheme,
}) => (
  <div
    className={classNames(
      styles.masked,
      styles.text,
      isInverted && styles.isInverted,
      className,
    )}
    style={customStyle}
  >
    <div className={classNames(styles.label, isActive && styles.active)}>
      {hasMaskedText ? maskedTextFn() : label}
    </div>
    {/* <div className={classNames(isActive && styles.active)}>
      <Button
        onClick={handleMasked}
        theme={iconButtonTheme}
        size="small"
        id="masked"
      >
        {hasMaskedText ? (
          <EyeIcon height="20px" width="20px" />
        ) : (
          <EyeOffIcon height="20px" width="20px" />
        )}
      </Button>
    </div> */}
  </div>
);

export default TableTextMasked;
