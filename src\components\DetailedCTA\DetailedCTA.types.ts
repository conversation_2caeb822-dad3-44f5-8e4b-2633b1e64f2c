import { ReactNode } from 'react';

export interface DetailedCTAPropTypes {
  /**
   * The heading of the call-to-action.
   */
  heading: ReactNode | string;
  /**
   * The body content of the call-to-action.
   */
  body?: ReactNode | string;
  /**
   * The action element (e.g., a Button) for the call-to-action.
   */
  action?: ReactNode;
  /**
   * The footer element can be set to render somthing at the end of CTA.
   */
  footer?: ReactNode;
  /**
   * The illustration element for the call-to-action.
   */
  illustration?: ReactNode;
  /**
   * Optional. The size of the call-to-action ('medium' or 'small').
   * @default 'medium'
   */
  size?: 'medium' | 'small';
  /**
   * Optional. The position of the call-to-action ('top' or 'center').
   * @default 'center'
   */
  position?: 'top' | 'center';
  /** *
   * Optional. Apply additional CSS when the card is in landscape mode
   */
  isInLandscapeMode?: boolean;
}
