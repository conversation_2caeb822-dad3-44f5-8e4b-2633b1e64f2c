import React, { ElementType, SyntheticEvent } from 'react';
import { NAV_ITEM_MOBILE } from '@constants/dataTestId';
import classNames from '@utils/classNames';

import LinkComponent from '../LinkComponent';
import styles from './NavItemMobile.module.css';

interface NavItemProps {
  label: string;
  isActive?: boolean;
  link?: string;
  isSubList?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onClick?: () => void;
  onLeftIconClick?: () => void;
  onRightIconClick?: () => void;
  className?: string;
  labelClassName?: string;
  handleClick?: (event: SyntheticEvent<Element, Event>) => void;
  component?: ElementType;
}

const NavItemMobile: React.FC<NavItemProps> = ({
  label,
  isActive = false,
  link,
  isSubList = false,
  leftIcon,
  rightIcon,
  onLeftIconClick = () => {},
  className,
  labelClassName,
  handleClick = () => {},
  component = 'a',
  ...props
}: NavItemProps) => (
  <LinkComponent
    className={classNames(
      styles.list,
      isSubList && styles.isSubList,
      isActive && styles.isActive,
      className,
    )}
    component={component}
    link={link}
    {...props}
    onClick={leftIcon ? onLeftIconClick : handleClick}
    data-active={isActive ? 'true' : 'false'}
    key={label}
    data-testid={NAV_ITEM_MOBILE}
  >
    <div className={styles.left}>
      <button type="button" className={styles.icon}>
        {leftIcon}
      </button>

      <div className={classNames(leftIcon && styles.backBtn, labelClassName)}>
        {label}
      </div>
    </div>
    <button type="button" className={styles.icon}>
      {rightIcon}
    </button>
  </LinkComponent>
);

export default NavItemMobile;
