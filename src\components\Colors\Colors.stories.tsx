import { Meta } from '@storybook/react';
import classNames from '@utils/classNames';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

import themeStyles from '../../styles/themes.module.css';
import styles from './Colors.module.css';

const meta: Meta = {
  title: 'Utils/Colors',
  tags: ['autodocs'],
};

export const ColorSheet = ({ label }) => (
  <>
    <div style={{ padding: variableStyles.fiveSpace }}>
      <ComponentGrid countColumns={3}>
        <ComponentTile label="Primary">
          <div
            className={classNames(
              themeStyles.primaryTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Primary Inverted">
          <div
            className={classNames(
              themeStyles.primaryInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Primary Faded">
          <div
            className={classNames(
              themeStyles.primaryFadedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>

        <ComponentTile label="Success">
          <div
            className={classNames(
              themeStyles.successTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Success Inverted">
          <div
            className={classNames(
              themeStyles.successInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Success Faded">
          <div
            className={classNames(
              themeStyles.successFadedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>

        <ComponentTile label="Danger">
          <div
            className={classNames(themeStyles.dangerTheme, styles.colorPalette)}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Danger Inverted">
          <div
            className={classNames(
              themeStyles.dangerInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Danger Faded">
          <div
            className={classNames(
              themeStyles.dangerFadedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>

        <ComponentTile label="Warning">
          <div
            className={classNames(
              themeStyles.warningTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Warning Inverted">
          <div
            className={classNames(
              themeStyles.warningInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Warning Faded">
          <div
            className={classNames(
              themeStyles.warningFadedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
      </ComponentGrid>
    </div>

    <div style={{ padding: variableStyles.fiveSpace }}>
      <ComponentGrid countColumns={2}>
        <ComponentTile label="Grey">
          <div
            className={classNames(themeStyles.greyTheme, styles.colorPalette)}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Grey Inverted">
          <div
            className={classNames(
              themeStyles.greyInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>

        <ComponentTile label="Dark">
          <div
            className={classNames(themeStyles.darkTheme, styles.colorPalette)}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Dark Inverted">
          <div
            className={classNames(
              themeStyles.darkInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>

        <ComponentTile label="Light">
          <div
            className={classNames(themeStyles.lightTheme, styles.colorPalette)}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Light Inverted">
          <div
            className={classNames(
              themeStyles.lightInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
      </ComponentGrid>
    </div>
  </>
);

ColorSheet.args = {
  label: 'Text',
};

export const ColorInvertedSheet = ({ label }) => (
  <div
    style={{
      backgroundColor: variableStyles.black,
      color: variableStyles.white,
    }}
  >
    <div style={{ padding: variableStyles.fiveSpace }}>
      <ComponentGrid countColumns={3}>
        <ComponentTile label="Primary">
          <div
            className={classNames(
              themeStyles.primaryTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Primary Inverted">
          <div
            className={classNames(
              themeStyles.primaryInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Primary Faded">
          <div
            className={classNames(
              themeStyles.primaryFadedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>

        <ComponentTile label="Success">
          <div
            className={classNames(
              themeStyles.successTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Success Inverted">
          <div
            className={classNames(
              themeStyles.successInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Success Faded">
          <div
            className={classNames(
              themeStyles.successFadedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>

        <ComponentTile label="Danger">
          <div
            className={classNames(themeStyles.dangerTheme, styles.colorPalette)}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Danger Inverted">
          <div
            className={classNames(
              themeStyles.dangerInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Danger Faded">
          <div
            className={classNames(
              themeStyles.dangerFadedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>

        <ComponentTile label="Warning">
          <div
            className={classNames(
              themeStyles.warningTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Warning Inverted">
          <div
            className={classNames(
              themeStyles.warningInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Warning Faded">
          <div
            className={classNames(
              themeStyles.warningFadedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
      </ComponentGrid>
    </div>

    <div style={{ padding: variableStyles.fiveSpace }}>
      <ComponentGrid countColumns={2}>
        <ComponentTile label="Grey">
          <div
            className={classNames(themeStyles.greyTheme, styles.colorPalette)}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Grey Inverted">
          <div
            className={classNames(
              themeStyles.greyInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>

        <ComponentTile label="Dark">
          <div
            className={classNames(themeStyles.darkTheme, styles.colorPalette)}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Dark Inverted">
          <div
            className={classNames(
              themeStyles.darkInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>

        <ComponentTile label="Light">
          <div
            className={classNames(themeStyles.lightTheme, styles.colorPalette)}
          >
            {label}
          </div>
        </ComponentTile>
        <ComponentTile label="Light Inverted">
          <div
            className={classNames(
              themeStyles.lightInvertedTheme,
              styles.colorPalette,
            )}
          >
            {label}
          </div>
        </ComponentTile>
      </ComponentGrid>
    </div>
  </div>
);

ColorInvertedSheet.args = {
  label: 'Text',
};

export default meta;
