import { forwardRef, useRef } from 'react';
import Scrims from '@components/Scrims';
import classNames from '@utils/classNames';

import styles from './ModalBody.module.css';
import { ModalBodyProps } from './ModalBody.types';

const ModalBody = forwardRef<HTMLDivElement, ModalBodyProps>(
  (
    {
      children,
      wrapperClassName,
      hasNoScroll = false,
      hasNoPaddingAround = false,
      wrapperContainerScrollClassName = '',
      maxBodyHeight = 'none',
    }: ModalBodyProps,
    ref,
  ) => {
    const listRef = useRef<HTMLDivElement>(null);

    return (
      <div className={classNames(styles.containerWrapper, wrapperClassName)}>
        <Scrims overflowRef={listRef} />
        <div
          ref={listRef}
          style={{
            maxHeight: maxBodyHeight,
            '--scrollMinHeight':
              typeof maxBodyHeight !== 'number'
                ? maxBodyHeight
                : `${maxBodyHeight}px`,
          }}
          className={classNames(
            styles.containerScroll,
            hasNoScroll && styles.hasNoScroll,
            wrapperContainerScrollClassName,
          )}
        >
          <div
            className={classNames(
              styles.body,
              !hasNoPaddingAround && styles.container,
            )}
            ref={ref}
          >
            {children}
          </div>
        </div>
      </div>
    );
  },
);

export default ModalBody;
