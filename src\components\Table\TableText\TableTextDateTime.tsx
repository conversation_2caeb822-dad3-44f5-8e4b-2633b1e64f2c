import React from 'react';
import { EMPTY_TABLE_VALUE } from '@constants/common';
import classNames from '@utils/classNames';
import { formatDateTable, formatTimeRange, getTimeZone } from '@utils/date';

import styles from './TableText.module.css';

interface TableTextDateTimeProps {
  isInverted?: boolean;
  isActive?: boolean;
  customStyle?: React.CSSProperties;
  className?: string;
  dateDetails: {
    startAt?: string;
    endAt?: string;
    timeZone?: string;
    date: string;
  };
}

const TableTextDateTime: React.FC<TableTextDateTimeProps> = ({
  isInverted,
  isActive,
  customStyle,
  className,
  dateDetails: { startAt, endAt, timeZone, date },
}) => {
  const formattedDate = formatDateTable(date);
  const formattedTimeRange = formatTimeRange(startAt, endAt);
  const timeZoneString = getTimeZone(timeZone);

  const isInvalidDate =
    formattedDate === 'Invalid Date' || formattedTimeRange === 'Invalid Date';

  return (
    <div
      className={classNames(
        styles.text,
        isActive && styles.active,
        isInverted && styles.isInverted,
        className,
      )}
      style={customStyle}
    >
      {!date || isInvalidDate
        ? EMPTY_TABLE_VALUE
        : `${formattedDate} ${formattedTimeRange} ${timeZoneString}`}
    </div>
  );
};

export default TableTextDateTime;
