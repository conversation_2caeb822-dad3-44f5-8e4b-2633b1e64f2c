{"name": "@peddleon/ped-ux-react-library", "version": "1.16.0", "description": "Peddle UX react component library", "publishConfig": {"registry": "https://npm.pkg.github.com"}, "files": ["dist", "src/**/!(*.spec|*.story).(js|css)", "README.md"], "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/types/index.d.ts", "sideEffects": ["*.css"], "scripts": {"build": "rollup -c", "prebuild": "rimraf dist/", "prepublishOnly": "npm run build", "clean": "rimraf dist .storybook/public", "dev:storybook": "storybook dev -p 8003", "build:storybook": "storybook build -o .storybook/public", "preview:storybook": "npx --yes http-server -p 7007 .storybook/public", "test": "jest", "coverage": "jest --coverage", "lint": "eslint --ext .tsx,.ts .", "lint:fix": "npm run lint -- --fix", "prepare": "husky install"}, "author": "peddle", "license": "ISC", "bugs": {"url": "https://github.com/peddleon/ped-ux-react-library/issues"}, "devDependencies": {"@commitlint/cli": "^19.2.1", "@commitlint/config-conventional": "^19.1.0", "@rollup/plugin-alias": "^5.0.0", "@rollup/plugin-node-resolve": "^13.0.0", "@rollup/plugin-typescript": "^11.1.0", "@storybook/addon-essentials": "^7.6.17", "@storybook/addon-interactions": "^7.6.17", "@storybook/addon-links": "^7.6.17", "@storybook/addon-onboarding": "^1.0.11", "@storybook/addon-styling": "^1.3.7", "@storybook/blocks": "^7.6.17", "@storybook/manager-api": "^7.6.17", "@storybook/preview-api": "^8.0.0", "@storybook/react": "^7.6.17", "@storybook/react-webpack5": "^7.6.17", "@storybook/test": "^7.6.17", "@swc/cli": "^0.1.62", "@swc/core": "^1.3.56", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@types/jest": "^29.5.12", "@types/node": "^20.11.20", "@types/react": "^18.2.58", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-css-modules": "^2.12.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-storybook": "^0.8.0", "husky": "^9.0.11", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.2", "postcss-import": "^16.0.1", "postcss-import-alias-resolver": "^0.1.1", "postcss-preset-env": "^9.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup": "^2.79.2", "rollup-plugin-copy": "^3.4.0", "rollup-plugin-postcss": "^4.0.0", "rollup-plugin-swc": "^0.2.1", "storybook": "^7.6.17", "swc-loader": "^0.2.3", "swc-plugin-auto-css-modules": "^1.3.0", "ts-jest": "^29.1.2", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5.3.3"}, "dependencies": {"@react-pdf/renderer": "^4.3.0", "@tanstack/react-table": "^8.15.3", "compute-scroll-into-view": "^3.1.0", "downshift": "^6.1.7", "pure-react-carousel": "^1.30.1", "qr-code-styling": "^1.7.2", "rc-tooltip": "^6.2.0", "react-day-picker": "^9.4.4", "vcard-creator": "^0.7.2"}, "lint-staged": {"*.{jsx,js,tsx,ts}": "eslint --cache --fix", "*.{js,jsx,ts,tsx,css,md}": "prettier --write"}, "browserslist": ["defaults"]}