@value variables: "../../styles/variables.module.css";
@value halfSpace, oneSpace, twoSpace, black, black5, black10, black35, black50, black70, smallWidth, h4FontSize, fourSpace from variables;
@value danger, white, primary, success from variables;
@value ease-out-expo from variables;

.textInput {
  &:hover {
    & .container {
      border-color: black35;
    }
  }
}

.disabled {
  pointer-events: none;

  & .container {
    pointer-events: none;
    background-color: black5;
    color: black35;
  }

  & .input {
    color: black50;
  }
}

.borderless {
  & .container {
    border: none;
  }
}

.square {
  & .container {
    border-radius: 0;
  }
}

.textInput.errored {
  & .container {
    border-color: danger;
  }

  &:focus-within {
    & .container {
      border-color: danger;
    }

    & .label {
      color: black;
    }

    & .leftWrapper {
      color: black;
    }

    & .rightWrapper {
      color: black;
    }
  }
}

.textInput {
  &:focus-within {
    & .container {
      border-color: primary;
    }

    & .label {
      color: primary;
    }

    & .leftWrapper {
      color: primary;
    }

    & .rightWrapper {
      color: black;
    }
  }
}

.textInput:focus-within,
.filled {
  & .label {
    top: 0;
    transform: translateY(8px) scale(0.75);
    line-height: 20px;
  }

  & .input::placeholder {
    opacity: 1;
  }
}

.textInput:focus-within:not(.filled) {
  &.readOnly {
    & .label {
      top: 50%;
      transform: translateY(-50%);
      line-height: 20px;
    }
    & .input {
      &::placeholder {
        opacity: 0;
        color: black50;
        font-weight: 400;
        letter-spacing: 0.01em;
        transition: opacity 0.5s ease-out-expo;
      }
    }
  }
}

.container {
  height: 64px;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
  background: white;
  border: solid 1px;
  border-color: black10;
  border-radius: 8px;
  transition: border-color 0.5s ease-out-expo;
  padding-left: twoSpace;
  padding-right: twoSpace;
}

.inputWrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

.wrapper {
  line-height: 0;
}

.leftWrapper {
  composes: wrapper;
  margin-right: oneSpace;
  color: black50;
  transition: color 0.5s ease-out-expo;
}

.rightWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  display: flex;
  flex-flow: row;
  align-items: center;
  color: black50;

  & > * {
    line-height: 0;
  }
}

.errorIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: danger;
}

.successIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
  color: success;
}

.fetchingIconWrapper {
  composes: wrapper;
  margin-left: oneSpace;
}

.label {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  font-size: 16px;
  line-height: 20px;
  letter-spacing: 0.01em;
  color: black50;
  transform-origin: left center;
  will-change: top, transform, color;
  transition:
    color 0.5s ease-out-expo,
    top 0.5s ease-out-expo,
    transform 0.5s ease-out-expo;
  pointer-events: none;
  user-select: none;
  white-space: nowrap;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

.input {
  width: 100%;
  height: 100%;
  font-size: 16px;
  line-height: 1.5;
  letter-spacing: 0.01em;
  color: black;
  align-self: flex-end;

  &::placeholder {
    opacity: 0;
    color: black50;
    font-weight: 400;
    letter-spacing: 0.01em;
    transition: opacity 0.5s ease-out-expo;
  }

  &.hasLabel {
    padding-top: twoSpace;
  }
}

.input[readonly] {
  cursor: pointer;
}

.footer {
  display: flex;
  flex-direction: column;
  text-align: left;
  padding: halfSpace twoSpace 0 0;
  font-size: 12px;
  line-height: 20px;
}

.error {
  color: danger;
}

.caption {
  color: black70;
}

.newVarientWrapper {
  & .container {
    height: 72px;

    @media (max-width: smallWidth) {
      height: 56px;
    }
  }

  & .label {
    text-transform: uppercase;
    font-family: Helvetica Now Var;
    font-size: h4FontSize;
    font-weight: 900;
    line-height: fourSpace;
    letter-spacing: 0.02em;
    text-align: left;
    font-variation-settings:
      'wght' 900,
      'wdth' 50,
      'opsz' 30;
    color: black35;

    @media (max-width: smallWidth) {
      font-size: 20px;
      line-height: 20px;
    }
  }

  &.focused {
    & .container {
      border-color: black;
    }

    & .label {
      color: black;
    }

    & .leftWrapper {
      color: black;
    }

    & .rightWrapper {
      color: black;
    }
  }

  &.filled:not(.focused) {
    & .label {
      color: black35;
    }
  }

  &.focused,
  &.filled {
    & .label {
      font-family: Helvetica Now Var;
      font-size: 19px;
      line-height: 19px;
      color: black50;
      font-weight: 900;
      letter-spacing: 0.02em;
      text-align: left;
    }

    & .input {
      line-height: fourSpace;
      padding-top: 0;
      margin-top: 27px;
      height: 30px;
      text-transform: uppercase;
    }

    @media (max-width: smallWidth) {
      & .label {
        font-size: 21px;
        line-height: 18px;
      }

      & .input {
        height: 26px;
      }
    }
  }

  & .input {
    font-family: Helvetica Now Var;
    font-size: h4FontSize;
    font-weight: 900;
    letter-spacing: 0.02em;
    line-height: 30px;
    color: black;
    font-variation-settings:
      'wght' 900,
      'wdth' 50,
      'opsz' 30;

    &::placeholder {
      color: black35;
      text-transform: uppercase;
    }

    @media (max-width: smallWidth) {
      font-size: 25px;
    }
  }
}
