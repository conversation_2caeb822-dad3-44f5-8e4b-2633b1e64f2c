/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/naming-convention */
import React, { useEffect, useState } from 'react';
import { Meta } from '@storybook/react';
import { MOCK_GROUP_OPTIONS } from '@constants/stories';

import AutoComplete from './AutoComplete';

const meta: Meta<typeof AutoComplete> = {
  title: 'Components/AutoComplete',
  tags: ['autodocs'],
  component: AutoComplete,
};

const useLocalSearch = ({ query }) => {
  const [data, setData] = useState([]);
  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `https://jsonplaceholder.typicode.com/todos`,
        );
        const result = await response.json();
        const mappedData = result
          .map(item => ({
            label: item.title,
            value: item.id,
          }))
          .filter(item =>
            item.label.toLowerCase().includes(query.toLowerCase()),
          );
        setData(mappedData);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [query]);

  return {
    data,
    isLoading,
  };
};

export const AutoCompleteStory = () => {
  const [address, setAddress] = useState(null);
  const [street, setStreet] = useState('');

  const { data: response, isLoading } = useLocalSearch({ query: street });

  console.log({ response, street, isLoading });

  return (
    <div
      style={{
        width: '500px',
        height: '450px',
      }}
    >
      <AutoComplete
        id="streetID"
        label="Street Address"
        items={MOCK_GROUP_OPTIONS}
        inputValue={street}
        value={address}
        onChange={val => {
          console.log({ val });
          setAddress(val);
        }}
        onInputValueChange={({ inputValue }) => {
          setStreet(inputValue);
        }}
        required
        onInvalid={() => {
          console.log('On Invalid');
        }}
        onFocus={() => console.log('On Focus')}
        onBlur={() => console.log('On Blur')}
        isLoading={isLoading}
        isFixedMenu
      />
    </div>
  );
};

export default meta;
