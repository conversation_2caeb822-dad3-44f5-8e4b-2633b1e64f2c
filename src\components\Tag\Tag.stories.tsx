// import { CarIcon } from '@peddleon/ped-ux-react-icons';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

import Tag from './Tag';

const iconKeys = [''];

export default {
  title: 'Components/Utility',
};

export const TagSheet = ({ label, type }) => {
  const icon = '';

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <ComponentGrid countColumns={3}>
        <ComponentTile label="Dark">
          <Tag label={label} theme="dark" type={type} />
        </ComponentTile>
        <ComponentTile label="Dark / Fetching">
          <Tag label={label} theme="dark" isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Dark / Disabled">
          <Tag label={label} theme="dark" isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Dark / Left icon">
          <Tag label={label} theme="dark" left={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Dark / Left icon / Fetching">
          <Tag label={label} theme="dark" left={icon} isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Dark / Left icon / Disabled">
          <Tag label={label} theme="dark" left={icon} isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Dark / Right icon">
          <Tag label={label} theme="dark" right={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Dark / Right icon / Fetching">
          <Tag label={label} theme="dark" right={icon} isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Dark / Right icon / Disabled">
          <Tag label={label} theme="dark" right={icon} isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Light">
          <Tag label={label} theme="light" type={type} />
        </ComponentTile>
        <ComponentTile label="Light / Fetching">
          <Tag label={label} theme="light" isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Light / Disabled">
          <Tag label={label} theme="light" isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Light / Left icon">
          <Tag label={label} theme="light" left={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Light / Left icon / Fetching">
          <Tag label={label} theme="light" left={icon} isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Light / Left icon / Disabled">
          <Tag label={label} theme="light" left={icon} isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Light / Right icon">
          <Tag label={label} theme="light" right={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Light / Right icon / Fetching">
          <Tag label={label} theme="light" right={icon} isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Light / Right icon / Disabled">
          <Tag
            label={label}
            theme="light"
            right={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Grey">
          <Tag label={label} theme="grey" type={type} />
        </ComponentTile>
        <ComponentTile label="Grey / Fetching">
          <Tag label={label} theme="grey" isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Grey / Disabled">
          <Tag label={label} theme="grey" isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Grey / Left icon">
          <Tag label={label} theme="grey" left={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Grey / Left icon / Fetching">
          <Tag label={label} theme="grey" left={icon} isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Grey / Left icon / Disabled">
          <Tag label={label} theme="grey" left={icon} isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Grey / Right icon">
          <Tag label={label} theme="grey" right={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Grey / Right icon / Fetching">
          <Tag label={label} theme="grey" right={icon} isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Grey / Right icon / Disabled">
          <Tag label={label} theme="grey" right={icon} isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Transparent">
          <Tag label={label} theme="transparent" type={type} />
        </ComponentTile>
        <ComponentTile label="Transparent / Fetching">
          <Tag label={label} theme="transparent" isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Transparent / Disabled">
          <Tag label={label} theme="transparent" isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Transparent / Left icon">
          <Tag label={label} theme="transparent" left={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Transparent / Left icon / Fetching">
          <Tag
            label={label}
            theme="transparent"
            left={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Transparent / Left icon / Disabled">
          <Tag
            label={label}
            theme="transparent"
            left={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Transparent / Right icon">
          <Tag label={label} theme="transparent" right={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Transparent / Right icon / Fetching">
          <Tag
            label={label}
            theme="transparent"
            right={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Transparent / Right icon / Disabled">
          <Tag
            label={label}
            theme="transparent"
            right={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Primary">
          <Tag label={label} theme="primary" type={type} />
        </ComponentTile>
        <ComponentTile label="Primary / Fetching">
          <Tag label={label} theme="primary" isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Primary / Disabled">
          <Tag label={label} theme="primary" isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Primary / Left icon">
          <Tag label={label} theme="primary" left={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Primary / Left icon / Fetching">
          <Tag
            label={label}
            theme="primary"
            left={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Primary / Left icon / Disabled">
          <Tag
            label={label}
            theme="primary"
            left={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Primary / Right icon">
          <Tag label={label} theme="primary" right={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Primary / Right icon / Fetching">
          <Tag
            label={label}
            theme="primary"
            right={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Primary / Right icon / Disabled">
          <Tag
            label={label}
            theme="primary"
            right={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Primary Faded">
          <Tag label={label} theme="primaryFaded" type={type} />
        </ComponentTile>
        <ComponentTile label="Primary Faded / Fetching">
          <Tag label={label} theme="primaryFaded" isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Primary Faded / Disabled">
          <Tag label={label} theme="primaryFaded" isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Primary Faded / Left icon">
          <Tag label={label} theme="primaryFaded" left={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Primary Faded / Left icon / Fetching">
          <Tag
            label={label}
            theme="primaryFaded"
            left={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Primary Faded / Left icon / Disabled">
          <Tag
            label={label}
            theme="primaryFaded"
            left={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Primary Faded / Right icon">
          <Tag label={label} theme="primaryFaded" right={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Primary Faded / Right icon / Fetching">
          <Tag
            label={label}
            theme="primaryFaded"
            right={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Primary Faded / Right icon / Disabled">
          <Tag
            label={label}
            theme="primaryFaded"
            right={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Primary Inverted">
          <Tag label={label} theme="primaryInverted" type={type} />
        </ComponentTile>
        <ComponentTile label="Primary Inverted / Fetching">
          <Tag label={label} theme="primaryInverted" isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Primary Inverted / Disabled">
          <Tag label={label} theme="primaryInverted" isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Primary Inverted / Left icon">
          <Tag label={label} theme="primaryInverted" left={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Primary Inverted / Left icon / Fetching">
          <Tag
            label={label}
            theme="primaryInverted"
            left={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Primary Inverted / Left icon / Disabled">
          <Tag
            label={label}
            theme="primaryInverted"
            left={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Primary Inverted / Right icon">
          <Tag label={label} theme="primaryInverted" right={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Primary Inverted / Right icon / Fetching">
          <Tag
            label={label}
            theme="primaryInverted"
            right={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Primary Inverted / Right icon / Disabled">
          <Tag
            label={label}
            theme="primaryInverted"
            right={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Danger">
          <Tag label={label} theme="danger" type={type} />
        </ComponentTile>
        <ComponentTile label="Danger / Fetching">
          <Tag label={label} theme="danger" isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Danger / Disabled">
          <Tag label={label} theme="danger" isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Danger / Left icon">
          <Tag label={label} theme="danger" left={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Danger / Left icon / Fetching">
          <Tag label={label} theme="danger" left={icon} isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Danger / Left icon / Disabled">
          <Tag
            label={label}
            theme="danger"
            left={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Danger / Right icon">
          <Tag label={label} theme="danger" right={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Danger / Right icon / Fetching">
          <Tag
            label={label}
            theme="danger"
            right={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Danger / Right icon / Disabled">
          <Tag
            label={label}
            theme="danger"
            right={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Danger Faded">
          <Tag label={label} theme="dangerFaded" type={type} />
        </ComponentTile>
        <ComponentTile label="Danger Faded / Fetching">
          <Tag label={label} theme="dangerFaded" isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Danger Faded / Disabled">
          <Tag label={label} theme="dangerFaded" isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Danger Faded / Left icon">
          <Tag label={label} theme="dangerFaded" left={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Danger Faded / Left icon / Fetching">
          <Tag
            label={label}
            theme="dangerFaded"
            left={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Danger Faded / Left icon / Disabled">
          <Tag
            label={label}
            theme="dangerFaded"
            left={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Danger Faded / Right icon">
          <Tag label={label} theme="dangerFaded" right={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Danger Faded / Right icon / Fetching">
          <Tag
            label={label}
            theme="dangerFaded"
            right={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Danger Faded / Right icon / Disabled">
          <Tag
            label={label}
            theme="dangerFaded"
            right={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Danger Inverted">
          <Tag label={label} theme="dangerInverted" type={type} />
        </ComponentTile>
        <ComponentTile label="Danger Inverted / Fetching">
          <Tag label={label} theme="dangerInverted" isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Danger Inverted / Disabled">
          <Tag label={label} theme="dangerInverted" isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Danger Inverted / Left icon">
          <Tag label={label} theme="dangerInverted" left={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Danger Inverted / Left icon / Fetching">
          <Tag
            label={label}
            theme="dangerInverted"
            left={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Danger Inverted / Left icon / Disabled">
          <Tag
            label={label}
            theme="dangerInverted"
            left={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Danger Inverted / Right icon">
          <Tag label={label} theme="dangerInverted" right={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Danger Inverted / Right icon / Fetching">
          <Tag
            label={label}
            theme="dangerInverted"
            right={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Danger Inverted / Right icon / Disabled">
          <Tag
            label={label}
            theme="dangerInverted"
            right={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Warning">
          <Tag label={label} theme="warning" type={type} />
        </ComponentTile>
        <ComponentTile label="Warning / Fetching">
          <Tag label={label} theme="warning" isLoading type={type} />
        </ComponentTile>
        <ComponentTile label="Warning / Disabled">
          <Tag label={label} theme="warning" isDisabled type={type} />
        </ComponentTile>

        <ComponentTile label="Warning / Left icon">
          <Tag label={label} theme="warning" left={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Warning / Left icon / Fetching">
          <Tag
            label={label}
            theme="warning"
            left={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Warning / Left icon / Disabled">
          <Tag
            label={label}
            theme="warning"
            left={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>

        <ComponentTile label="Warning / Right icon">
          <Tag label={label} theme="warning" right={icon} type={type} />
        </ComponentTile>
        <ComponentTile label="Warning / Right icon / Fetching">
          <Tag
            label={label}
            theme="warning"
            right={icon}
            isLoading
            type={type}
          />
        </ComponentTile>
        <ComponentTile label="Warning / Right icon / Disabled">
          <Tag
            label={label}
            theme="warning"
            right={icon}
            isDisabled
            type={type}
          />
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
};
TagSheet.args = {
  label: 'Tag label',
  icon: 'SettingsIcon',
};
TagSheet.argTypes = {
  icon: {
    options: iconKeys,
    control: {
      type: 'select',
    },
  },
  type: {
    options: ['button', 'label'],
    control: {
      type: 'select',
    },
  },
};

export const TagInvertedSheet = ({ label }) => {
  const icon = '';

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
        backgroundColor: variableStyles.black,
        color: variableStyles.white,
      }}
    >
      <ComponentGrid countColumns={3}>
        <ComponentTile label="Dark Inverted">
          <Tag label={label} theme="darkInverted" />
        </ComponentTile>
        <ComponentTile label="Dark Inverted / Fetching">
          <Tag label={label} theme="darkInverted" isLoading />
        </ComponentTile>
        <ComponentTile label="Dark Inverted / Disabled">
          <Tag label={label} theme="darkInverted" isDisabled />
        </ComponentTile>

        <ComponentTile label="Dark Inverted / Left icon">
          <Tag label={label} theme="darkInverted" left={icon} />
        </ComponentTile>
        <ComponentTile label="Dark Inverted / Left icon / Fetching">
          <Tag label={label} theme="dark" left={icon} isLoading />
        </ComponentTile>
        <ComponentTile label="Dark Inverted / Left icon / Disabled">
          <Tag label={label} theme="darkInverted" left={icon} isDisabled />
        </ComponentTile>

        <ComponentTile label="Dark Inverted / Right icon">
          <Tag label={label} theme="darkInverted" right={icon} />
        </ComponentTile>
        <ComponentTile label="Dark Inverted / Right icon / Fetching">
          <Tag label={label} theme="darkInverted" right={icon} isLoading />
        </ComponentTile>
        <ComponentTile label="Dark Inverted / Right icon / Disabled">
          <Tag label={label} theme="darkInverted" right={icon} isDisabled />
        </ComponentTile>

        <ComponentTile label="Light Inverted">
          <Tag label={label} theme="lightInverted" />
        </ComponentTile>
        <ComponentTile label="Light Inverted / Fetching">
          <Tag label={label} theme="lightInverted" isLoading />
        </ComponentTile>
        <ComponentTile label="Light Inverted / Disabled">
          <Tag label={label} theme="lightInverted" isDisabled />
        </ComponentTile>

        <ComponentTile label="Light Inverted / Left icon">
          <Tag label={label} theme="lightInverted" left={icon} />
        </ComponentTile>
        <ComponentTile label="Light Inverted / Left icon / Fetching">
          <Tag label={label} theme="lightInverted" left={icon} isLoading />
        </ComponentTile>
        <ComponentTile label="Light Inverted / Left icon / Disabled">
          <Tag label={label} theme="lightInverted" left={icon} isDisabled />
        </ComponentTile>

        <ComponentTile label="Light Inverted / Right icon">
          <Tag label={label} theme="lightInverted" right={icon} />
        </ComponentTile>
        <ComponentTile label="Light Inverted / Right icon / Fetching">
          <Tag label={label} theme="lightInverted" right={icon} isLoading />
        </ComponentTile>
        <ComponentTile label="Light Inverted / Right icon / Disabled">
          <Tag label={label} theme="lightInverted" right={icon} isDisabled />
        </ComponentTile>

        <ComponentTile label="Grey Inverted">
          <Tag label={label} theme="greyInverted" />
        </ComponentTile>
        <ComponentTile label="Grey Inverted / Fetching">
          <Tag label={label} theme="greyInverted" isLoading />
        </ComponentTile>
        <ComponentTile label="Grey Inverted / Disabled">
          <Tag label={label} theme="greyInverted" isDisabled />
        </ComponentTile>

        <ComponentTile label="Grey Inverted / Left icon">
          <Tag label={label} theme="greyInverted" left={icon} />
        </ComponentTile>
        <ComponentTile label="Grey Inverted / Left icon / Fetching">
          <Tag label={label} theme="greyInverted" left={icon} isLoading />
        </ComponentTile>
        <ComponentTile label="Grey Inverted / Left icon / Disabled">
          <Tag label={label} theme="greyInverted" left={icon} isDisabled />
        </ComponentTile>

        <ComponentTile label="Grey Inverted / Right icon">
          <Tag label={label} theme="greyInverted" right={icon} />
        </ComponentTile>
        <ComponentTile label="Grey Inverted / Right icon / Fetching">
          <Tag label={label} theme="greyInverted" right={icon} isLoading />
        </ComponentTile>
        <ComponentTile label="Grey Inverted / Right icon / Disabled">
          <Tag label={label} theme="greyInverted" right={icon} isDisabled />
        </ComponentTile>

        <ComponentTile label="Transparent Inverted">
          <Tag label={label} theme="transparentInverted" />
        </ComponentTile>
        <ComponentTile label="Transparent Inverted / Fetching">
          <Tag label={label} theme="transparentInverted" isLoading />
        </ComponentTile>
        <ComponentTile label="Transparent Inverted / Disabled">
          <Tag label={label} theme="transparentInverted" isDisabled />
        </ComponentTile>

        <ComponentTile label="Transparent Inverted / Left icon">
          <Tag label={label} theme="transparentInverted" left={icon} />
        </ComponentTile>
        <ComponentTile label="Transparent Inverted / Left icon / Fetching">
          <Tag
            label={label}
            theme="transparentInverted"
            left={icon}
            isLoading
          />
        </ComponentTile>
        <ComponentTile label="Transparent Inverted / Left icon / Disabled">
          <Tag
            label={label}
            theme="transparentInverted"
            left={icon}
            isDisabled
          />
        </ComponentTile>

        <ComponentTile label="Transparent Inverted / Right icon">
          <Tag label={label} theme="transparentInverted" right={icon} />
        </ComponentTile>
        <ComponentTile label="Transparent Inverted / Right icon / Fetching">
          <Tag
            label={label}
            theme="transparentInverted"
            right={icon}
            isLoading
          />
        </ComponentTile>
        <ComponentTile label="Transparent Inverted / Right icon / Disabled">
          <Tag label={label} theme="transparent" right={icon} isDisabled />
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
};
TagInvertedSheet.args = {
  label: 'Tag label',
  icon: 'SettingsIcon',
};
TagInvertedSheet.argTypes = {
  icon: {
    control: {
      type: 'select',
    },
  },
};
