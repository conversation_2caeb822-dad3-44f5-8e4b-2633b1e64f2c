import { ReactNode } from 'react';
import { ModalTracker } from '@libtypes/modal';

export interface DialogModalProps {
  /**
   * This property is set to track the modal by name, allowing for easy distinction in tracking
   */
  name?: string;
  /**
   * To set the maximum width of the modal, enabling users to maintain modal responsiveness even for large content
   */
  maxWidth?: number | string;
  /**
   * To set the maximum height of the dialog wrapper for mobile devices
   */
  maxHeightMobile?: number | string;
  /**
   * To hide/show modal
   */
  isActive: boolean;
  /**
   * To prevent animation flickering, we set a default opacity to zero using this prop.
   */
  isAnimated?: boolean;
  /**
   * To center the modal relative to the screen, set this property to true
   */
  isMobileCentered?: boolean;
  /**
   * Children can be placed within the children placeholder in the JSX of the modal
   */
  children?: ReactNode;
  /**
   * This property can be set to provide external CSS to a wrapper
   */
  wrapperClassName?: string;
  /**
   * This callback handler takes the modal name along with the modal action to track user interactions with the modal
   */
  tracker?: ModalTracker;
  /**
   * A callback handler triggered when the user clicks outside of the modal
   */
  onModalOutsideClick?: () => void;
  /**
   * isScrollable prop is used to set the modal to scrollable
   */
  isScrollable?: boolean;
}

export interface DialogModalStoryProps extends DialogModalProps {
  heading: ReactNode;
  body: ReactNode;
  acceptLabel: string;
  declineLabel: string;
  isFullWidthButton?: boolean;
  title?: ReactNode;
  subHeading?: ReactNode;
}
