@value variables: "../../styles/variables.module.css";
@value oneSpace from variables;

.menuItemLink {
  display: block;
}

.children {
  position: relative;
  width: fit-content;
}

.popupLayout {
  display: block;
  position: absolute;
  padding-top: oneSpace;
  left: 50%;
  transform: translateX(-50%);
}

.children:hover + .popupLayout,
.popupLayout:hover {
  display: block;
}

.visibleStyles {
  * {
    visibility: visible;
  }
}

.menuListItem {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.container {
  position: relative;
  width: fit-content;
}

.childWrapper {
  width: fit-content;
}
