import { InputPropType, SelectItemType } from '@libtypes/common';

export interface SelectSearchPropType
  extends Omit<InputPropType, 'onChange' | 'value'> {
  /**
   * A supplementary text displayed at the bottom of the input.
   */
  caption?: string;

  /**
   * The maximum number of characters allowed in the input.
   */
  maxLength?: number;

  /**
   * Callback function invoked when the menu is closed.
   */
  onMenuClose?: () => void;

  /**
   * An array of items to be displayed in the dropdown menu.
   */
  items: Array<SelectItemType>;

  /**
   * Callback function invoked when an item is selected from the dropdown menu.
   */
  onChange: (arg: SelectItemType) => void;

  /**
   * Specifies whether the dropdown menu is aligned to the right.
   */
  isMenuAlignedRight?: boolean;

  /**
   * Placeholder text displayed when no option is selected.
   */
  placeholder?: string;

  /**
   * Indicates whether the active state of the dropdown is controlled externally.
   */
  isActiveControlled?: boolean;

  /**
   * Specifies whether the dropdown menu is active/open.
   */
  isActive?: boolean;

  /**
   * Callback function invoked when the toggle button is clicked while the input is in focus.
   */
  onToggleButtonClick?: () => void;

  /**
   * Callback function invoked when the menu should be closed.
   */
  onShouldClose?: () => void;

  /**
   * The currently selected value from the dropdown menu.
   */
  value: SelectItemType;

  /**
   * The input mode for mobile devices.
   */
  inputMode?:
    | 'text'
    | 'email'
    | 'search'
    | 'tel'
    | 'url'
    | 'none'
    | 'numeric'
    | 'decimal';

  /**
   * Text displayed when no matching options are found in the dropdown.
   */
  noMatchFoundText?: string;
  /**
   * Flag to enable the 'Powered by Google' UI while searching for the address.
   */
  isPoweredByGoogle?: boolean;
  /**
   * Callback function invoked when the input value changes.
   */
  onInputValueChange?: (val: string) => void;
  /**
   * Flag to indicate if the menu should be fixed so it can float over elements above or below.
   */
  isFixedMenu?: boolean;
  /**
   * Position of the modal on mobile devices.
   */
  mobileModalPosition?: 'center' | 'bottom' | 'top';
  /**
   * className for the dropdown menu.
   */
  className?: string;
  /**
   *  Flag to hide the error message.
   */

  hideErrorMessage?: boolean;

  /**
   * title for the dropdown menu.
   * */

  title?: string;
  /**
   * variant for the dropdown menu.
   */
  variant?: 'helveticaFonts' | 'normalFonts';
}
