/* eslint-disable no-console */
import { useState } from 'react';
import { Meta } from '@storybook/react';
import Field<PERSON>abel from '@components/FieldLabel';
import Select from '@components/Select';
import {
  MOCK_VEHICLE_MAKE_OPTIONS,
  MOCK_VEHICLE_MODEL_OPTIONS,
  MOCK_VEHICLE_YEAR_OPTIONS,
} from '@constants/stories';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import typographyStyles from '@styles/typography.module.css';
import variableStyles from '@styles/variables.module.css';

import { SelectProps } from './Select.types';
/**
 *
 * The Select component, utilizing the Downshift library, offers smooth scrolling and automatically adjusts to bring the selected option into view, ensuring an intuitive user experience. With its focus on usability and accessibility, this component streamlines option selection while providing flexibility for customization and extension by developers
 *
 * ## Overview
 *
 * The Select component serves as a platform for demonstrating its advanced features and capabilities. It provides multiple instances with different configurations, allowing developers to visualize and test various scenarios.
 *
 * ## Usage
 *
 * To use the Select component, simply import it into your React application and render it within your component hierarchy.
 * Customize the props of the Select component to control the labels, captions, errors, placeholders, and other aspects of the Select components it contains.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { Select } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, include the Select component in your JSX:
 *
 * ```jsx
 * <Select
 *  caption="Example Caption"
 *  inputMode="text"
 *  items={[
 *   { value: 'option1', label: 'Option 1' },
 *   { value: 'option2', label: 'Option 2' },
 *   { value: 'option3', label: 'Option 3' },
 *  ]}
 *  label="Label"
 *  maxLength={50}
 *  noMatchFoundText="No matches"
 *  placeholder="Placeholder"
 *  value={null}
 * >
 * ```
 *
 * This will render a grid layout containing multiple Select components with different configurations.
 */
const meta: Meta<typeof Select> = {
  title: 'Components/Select',
  tags: ['autodocs'],
  component: Select,
};

export const SelectStory = (args: SelectProps) => {
  const [state, setState] = useState('');
  return (
    <div style={{ minHeight: '400px' }}>
      <Select {...args} value={state} onChange={value => setState(value)} />
    </div>
  );
};

SelectStory.args = {
  caption: 'Example Caption',
  items: MOCK_VEHICLE_MAKE_OPTIONS,
  onChange: selectedItem => {
    console.log('Selected Item:', selectedItem);
  },
  isMenuAlignedRight: false,
  isActiveControlled: false,
  isActive: false,
  onToggleButtonClick: () => {
    console.log('on toggle button click');
  },
  onShouldClose: () => {
    console.log('on menu should close');
  },
  value: null,
  label: 'Label',
  onInvalid: () => {
    console.log('on invalid');
  },
  required: false,
  onBlur: () => {
    console.log('on blur');
  },
};
SelectStory.argTypes = {
  caption: {
    control: 'text',
    description: 'A supplementary text displayed at the bottom of the input.',
  },
  items: {
    control: 'object',
    description: 'An array of items to be displayed in the dropdown menu.',
  },
  onChange: {
    action: 'onChange',
    description:
      'Callback function invoked when an item is selected from the dropdown menu.',
  },
  isMenuAlignedRight: {
    control: 'boolean',
    description: 'Specifies whether the dropdown menu is aligned to the right.',
  },
  isActiveControlled: {
    control: 'boolean',
    description:
      'Indicates whether the active state of the dropdown is controlled externally.',
  },
  isActive: {
    control: 'boolean',
    description: 'Specifies whether the dropdown menu is active/open.',
  },
  onToggleButtonClick: {
    action: 'onToggleButtonClick',
    description:
      'Callback function invoked when the toggle button is clicked while the input is in focus.',
  },
  onShouldClose: {
    action: 'onShouldClose',
    description: 'Callback function invoked when the menu should be closed.',
  },
  value: {
    control: 'object',
    description: 'The currently selected value from the dropdown menu.',
  },
  id: {
    control: 'text',
    description: 'A unique identifier for the input element.',
  },
  label: {
    control: 'text',
    description: 'The label displayed alongside the input element.',
  },
  error: {
    control: 'text',
    description: 'An error message to display when the input value is invalid.',
  },
  left: {
    control: 'none',
    description: 'An element to display on the left side of the input.',
  },
  right: {
    control: 'none',
    description: 'An element to display on the right side of the input.',
  },
  hasSucceeded: {
    control: 'boolean',
    description:
      'Indicates whether the input has succeeded (e.g., successfully submitted).',
  },
  isLoading: {
    control: 'boolean',
    description:
      'Indicates whether data is being fetched or processed for the input.',
  },
  isDisabled: {
    control: 'boolean',
    description: 'Indicates whether the input element is disabled.',
  },
  isBorderless: {
    control: 'boolean',
    description: 'Indicates whether the input element has a borderless style.',
  },
  onInvalid: {
    action: 'invalid',
    description:
      'Callback function invoked when the input value is deemed invalid.',
  },
  onBlur: {
    action: 'blur',
    description: 'Callback function invoked when the input loses focus.',
  },
  required: {
    control: 'boolean',
    description: 'Indicates whether the input field is required.',
  },
  isFocused: {
    control: 'boolean',
    description: 'Indicates whether the input should be focused initially.',
  },
  isMobileMenuModal: {
    control: 'boolean',
    description:
      'Indicates whether the menu is displayed as a modal on mobile devices.',
  },
  name: {
    control: 'text',
    description: 'The name attribute of the input field.',
  },
  isFixedMenu: {
    control: 'boolean',
    description:
      'Indicates whether the menu is fixed or relative to select input',
  },
  mobileModalPosition: {
    control: 'select',
    options: ['center', 'bottom', 'top'],
    description: 'Position of the modal on mobile devices',
  },
};

export const SelectSheet = ({
  label,
  fieldLabel,
  error,
  caption,
  rightOverline,
  isMenuAlignedRight,
  isFirstSelectFocused,
}) => {
  const [state, setState] = useState({
    standard: null,
    withLeft: null,
    withRight: null,
    make: null,
    year: null,
    model: null,
  });

  const [isYearActive, setIsYearActive] = useState(false);
  const [isModelActive, setIsModelActive] = useState(false);
  const [isMakeActive, setIsMakeActive] = useState(false);

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <ComponentGrid countColumns={2}>
        <ComponentTile label="Standard">
          <Select
            label={label}
            value={state.standard}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            onChange={value => {
              setState({ ...state, standard: value });
            }}
            isFocused={isFirstSelectFocused}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline">
          <Select
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Caption">
          <Select
            label={label}
            value={state.standard}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            caption={caption}
            onChange={value => setState({ ...state, standard: value })}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Caption">
          <Select
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            caption={caption}
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Error / Fixed">
          <Select
            label={label}
            value={state.standard}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            error={error}
            onChange={value => setState({ ...state, standard: value })}
            isFixedMenu
          />
        </ComponentTile>

        <ComponentTile label="Standard / Right overline / Error">
          <Select
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            error={error}
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Success">
          <Select
            label={label}
            value={state.standard}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            hasSucceeded
            onChange={value => setState({ ...state, standard: value })}
          />
        </ComponentTile>

        <ComponentTile label="Standard / Right overline / Success">
          <Select
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            hasSucceeded
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Loading / Fixed">
          <Select
            label={label}
            value={state.standard}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            isLoading
            onChange={value => setState({ ...state, standard: value })}
            isFixedMenu
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Loading">
          <Select
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            isLoading
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Disabled">
          <Select
            label={label}
            value={state.standard}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            isDisabled
            onChange={value => setState({ ...state, standard: value })}
          />
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Disabled">
          <Select
            label={label}
            value={state.withRight}
            items={MOCK_VEHICLE_MAKE_OPTIONS}
            isMenuAlignedRight={isMenuAlignedRight}
            isDisabled
            onChange={value => setState({ ...state, withRight: value })}
            right={
              <span className={typographyStyles.overline}>{rightOverline}</span>
            }
          />
        </ComponentTile>

        <ComponentTile label="Standard / Field label / Fixed">
          <div style={{ textAlign: 'left' }}>
            <FieldLabel label={fieldLabel} />
            <Select
              label={label}
              value={state.standard}
              isMenuAlignedRight={isMenuAlignedRight}
              items={MOCK_VEHICLE_MAKE_OPTIONS}
              onChange={value => setState({ ...state, standard: value })}
              isFixedMenu
            />
          </div>
        </ComponentTile>
        <ComponentTile label="Standard / Right overline / Field label">
          <div style={{ textAlign: 'left' }}>
            <FieldLabel label={fieldLabel} />
            <Select
              label={label}
              value={state.withRight}
              items={MOCK_VEHICLE_MAKE_OPTIONS}
              isMenuAlignedRight={isMenuAlignedRight}
              onChange={value => setState({ ...state, withRight: value })}
              right={
                <span className={typographyStyles.overline}>
                  {rightOverline}
                </span>
              }
            />
          </div>
        </ComponentTile>
        <ComponentTile label="Triggered Selects 1">
          <div style={{ textAlign: 'left' }}>
            <Select
              label="Year"
              value={state.year}
              items={MOCK_VEHICLE_YEAR_OPTIONS}
              isMenuAlignedRight={isMenuAlignedRight}
              onChange={value => setState({ ...state, year: value })}
              onShouldClose={() => {
                setIsYearActive(undefined);
                setIsMakeActive(true);
              }}
              isActive={isYearActive}
            />
          </div>
        </ComponentTile>
        {/* remaining */}
        <ComponentTile label="Triggered Selects 2">
          <div style={{ textAlign: 'left' }}>
            <Select
              label="Make"
              value={state.make}
              items={MOCK_VEHICLE_MAKE_OPTIONS}
              isMenuAlignedRight={isMenuAlignedRight}
              onChange={value => setState({ ...state, make: value })}
              onShouldClose={() => {
                setIsMakeActive(undefined);
                setIsModelActive(true);
              }}
              isActive={isMakeActive}
            />
          </div>
        </ComponentTile>
        <ComponentTile label="Triggered Selects 3 / Fixed">
          <div style={{ textAlign: 'left' }}>
            <Select
              label="Model"
              value={state.model}
              items={MOCK_VEHICLE_MODEL_OPTIONS}
              isMenuAlignedRight={isMenuAlignedRight}
              onChange={value => setState({ ...state, model: value })}
              onShouldClose={() => {
                setIsModelActive(undefined);
              }}
              isActive={isModelActive}
              isFixedMenu
            />
          </div>
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
};

SelectSheet.args = {
  label: 'Label',
  fieldLabel: 'Field label',
  placeholder: 'Placeholder',
  error: 'Input error message',
  caption: 'Input caption message',
  rightOverline: 'UNIT',
  isMenuAlignedRight: false,
  isFirstSelectFocused: false,
};

export default meta;
