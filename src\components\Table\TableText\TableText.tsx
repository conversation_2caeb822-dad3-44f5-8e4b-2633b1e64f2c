import React, { ReactNode, useCallback, useEffect, useState } from 'react';
import { StatusIndicatorTheme } from '@components/StatusIndicator/StatusIndicator';
import { EMPTY_TABLE_VALUE } from '@constants/common';
import classNames from '@utils/classNames';

import styles from './TableText.module.css';
import TableTextCopyClipboard from './TableTextCopyClipboard';
import CurrencyText from './TableTextCurrency';
import TableTextDate from './TableTextDate';
import TableTextDateTime from './TableTextDateTime';
import TableTextIcon from './TableTextIcon';
import TableTextLink from './TableTextLink';
import TableTextMasked from './TableTextMasked';
import TableTextStatusIndicator from './TableTextStatusIndicator';
import TableTextText from './TableTextText';
import TableTextTooltip from './TableTextTooltip';

interface IconObject {
  icon: React.ReactNode;
  id: string;
}

/**
 * Props for the TableText component.
 */
interface TableTextProps {
  /**
   * Determines the type of content to display.
   *
   * @default 'default'
   */
  variant?:
    | 'text'
    | 'copyClipboard'
    | 'masked'
    | 'icon'
    | 'statusIndicator'
    | 'currency'
    | 'default'
    | 'link'
    | 'date'
    | 'dateTime'
    | 'tooltip';

  /**
   * Boolean flag indicating whether the text is active or not.
   */
  isActive?: boolean;

  /**
   * Custom CSS styles to be applied to the text element.
   */
  customStyle?: React.CSSProperties;

  /**
   * Length of masked text if `variant` is set to 'masked'.
   */
  maskedTextLen?: number;

  /**
   * Event handler function triggered on click events.
   */
  onClick?: (
    event:
      | React.MouseEvent<HTMLButtonElement>
      | React.KeyboardEvent<HTMLDivElement>
      | React.MouseEvent<HTMLAnchorElement>
      | string,
  ) => void;

  /**
   * Theme for the status indicator if `variant` is set to 'statusIndicator'.
   */
  indicatorTheme?: StatusIndicatorTheme;

  /**
   * Text content to be displayed.
   */
  label?: string;

  /**
   * Boolean flag indicating whether the text should have inverted styling.
   */
  isInverted?: boolean;

  /**
   * Array of icon objects to be displayed if `variant` is set to 'icon'.
   */
  icons?: IconObject[];

  /**
   * Hide the success icon.
   */
  hideMaskedSuccess?: boolean;

  /**
   * Custom class name for the component.
   */
  className?: string;

  /**
   * Child elements to be rendered within the component.
   */
  children?: ReactNode;

  /**
   * Boolean flag to manually enable or disable masking.
   */
  hasMaskedTextApplied?: boolean;

  /**
   * Custom component to be rendered.
   */
  component?: React.ElementType;

  /**
   * Tooltip configuration.
   */
  tooltip?: {
    /**
     * Text to be displayed in the tooltip.
     */
    text?: string;

    /**
     * Placement of the tooltip.
     */
    placement?: string;
  };

  /**
   * Unique identifier for the component.
   */
  id?: string;

  /**
   * Date details for the component.
   */
  dateDetails?: {
    /**
     * Start date and time.
     */
    startAt?: string;

    /**
     * End date and time.
     */
    endAt?: string;

    /**
     * Time zone for the date.
     */
    timeZone?: string;

    /**
     * Date to be displayed.
     */
    date: string;
  };
}

/**
 * TableText component provides a customizable text display element for use in tables.
 * It supports various types of content rendering based on the specified variant and props.
 *
 */
const TableText: React.FC<TableTextProps> = ({
  variant = 'default',
  isActive = false,
  customStyle,
  maskedTextLen = 0,
  onClick = () => {},
  indicatorTheme,
  label = 'Default label',
  isInverted = false,
  icons = [],
  hideMaskedSuccess = true,
  className = '',
  children = null,
  hasMaskedTextApplied = undefined,
  component = 'div',
  tooltip,
  id,
  dateDetails: { startAt, endAt, timeZone, date } = {},
  ...props
}) => {
  const [hasMaskedText, setHasMaskedText] = useState(true);
  const [isTextCopied, setIsTextCopied] = useState(false);

  useEffect(() => {
    if (typeof hasMaskedTextApplied !== 'undefined')
      setHasMaskedText(hasMaskedTextApplied);
  }, [hasMaskedTextApplied, hasMaskedText]);

  const maskedTextFn = useCallback(() => {
    let maskedText = 'x';
    const len = maskedTextLen || (label && label.length);

    for (let index = 1; index < len; index += 1) {
      maskedText += 'x';
    }

    return maskedText;
  }, [label, maskedTextLen]);

  const handleIconClick = useCallback(
    iconID => () => {
      onClick(iconID);
    },
    [onClick],
  );

  const handleMasked = useCallback(
    (event: React.MouseEvent<HTMLButtonElement>) => {
      onClick(event);
      if (label) {
        setHasMaskedText(state => !state);
      }
    },
    [label, onClick],
  );

  const handleCopyClipboard = event => {
    onClick(event);
    setIsTextCopied(true);
    setTimeout(() => {
      setIsTextCopied(false);
    }, 2000);
  };

  const iconButtonTheme = isInverted ? 'transparentInverted' : 'transparent';

  const renderContent = () => {
    switch (variant) {
      case 'copyClipboard':
        return (
          <TableTextCopyClipboard
            isInverted={isInverted}
            isActive={isActive}
            customStyle={customStyle}
            className={className}
            label={label}
            handleCopyClipboard={handleCopyClipboard}
            isTextCopied={isTextCopied}
            hideMaskedSuccess={hideMaskedSuccess}
            iconButtonTheme={iconButtonTheme}
          />
        );
      case 'statusIndicator':
        return (
          <TableTextStatusIndicator
            isInverted={isInverted}
            isActive={isActive}
            customStyle={customStyle}
            className={className}
            label={label}
            indicatorTheme={indicatorTheme}
          />
        );
      case 'masked':
        return (
          <TableTextMasked
            isInverted={isInverted}
            isActive={isActive}
            customStyle={customStyle}
            className={className}
            label={label}
            hasMaskedText={hasMaskedText}
            maskedTextFn={maskedTextFn}
            handleMasked={handleMasked}
            iconButtonTheme={iconButtonTheme}
          />
        );
      case 'icon':
        return (
          <TableTextIcon
            isInverted={isInverted}
            isActive={isActive}
            customStyle={customStyle}
            className={className}
            icons={icons}
            handleIconClick={handleIconClick}
            iconButtonTheme={iconButtonTheme}
          />
        );
      case 'text':
        return (
          <TableTextText
            isInverted={isInverted}
            isActive={isActive}
            customStyle={customStyle}
            className={className}
          >
            {label || EMPTY_TABLE_VALUE}
          </TableTextText>
        );
      case 'link':
        return (
          <TableTextLink
            isInverted={isInverted}
            isActive={isActive}
            customStyle={customStyle}
            className={className}
            label={label}
            onClick={onClick}
            component={component}
            {...props}
          />
        );
      case 'currency':
        return (
          <CurrencyText
            label={label}
            isActive={isActive}
            isInverted={isInverted}
            customStyle={customStyle}
            className={className}
          />
        );
      case 'tooltip':
        return (
          <TableTextTooltip
            isInverted={isInverted}
            isActive={isActive}
            customStyle={customStyle}
            className={className}
            tooltip={tooltip}
            id={id}
          >
            {children}
          </TableTextTooltip>
        );
      case 'date':
        return (
          <TableTextDate
            isInverted={isInverted}
            isActive={isActive}
            customStyle={customStyle}
            className={className}
            date={date}
          />
        );
      case 'dateTime':
        return (
          <TableTextDateTime
            isInverted={isInverted}
            isActive={isActive}
            customStyle={customStyle}
            className={className}
            dateDetails={{ startAt, endAt, timeZone, date }}
          />
        );
      default:
        return (
          <div
            className={classNames(
              styles.text,
              isActive && styles.active,
              isInverted && styles.isInverted,
              className,
            )}
            style={customStyle}
          >
            {children || label || EMPTY_TABLE_VALUE}
          </div>
        );
    }
  };

  return <>{renderContent()}</>;
};

export default TableText;
