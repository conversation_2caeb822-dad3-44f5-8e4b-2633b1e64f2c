import React from 'react';
import { EMPTY_TABLE_VALUE } from '@constants/common';
import classNames from '@utils/classNames';
import { formatCurrency } from '@utils/currency';

import styles from './TableText.module.css';

interface CurrencyTextProps {
  label: string;
  isActive?: boolean;
  isInverted?: boolean;
  customStyle?: React.CSSProperties;
  className?: string;
}

const CurrencyText: React.FC<CurrencyTextProps> = ({
  label,
  isActive,
  isInverted,
  customStyle,
  className,
}) => {
  const formattedLabel = formatCurrency(Number(label));
  const displayLabel =
    !label || Number.isNaN(Number(label)) ? EMPTY_TABLE_VALUE : formattedLabel;

  return (
    <div
      className={classNames(
        styles.text,
        isActive && styles.active,
        isInverted && styles.isInverted,
        className,
      )}
      style={customStyle}
      title={displayLabel}
    >
      {displayLabel}
    </div>
  );
};

export default CurrencyText;
