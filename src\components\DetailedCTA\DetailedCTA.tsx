import { DETAILED_CTA_DATA_TEST_ID } from '@constants/dataTestId';
import classNames from '@utils/classNames';

import styles from './DetailedCTA.module.css';
import { DetailedCTAPropTypes } from './DetailedCTA.types';

/**
 * The DetailedCTA component displays a call-to-action with detailed information and an optional illustration.
 *
 * @param {Object} props - The props for the DetailedCTA component.
 * @param {string} props.heading - The heading of the call-to-action.
 * @param {ReactNode | string} props.body - The body content of the call-to-action.
 * @param {string} [props.size='medium'] - Optional. The size of the call-to-action ('medium' or 'small').
 * @param {ReactNode} props.action - The action element (e.g., a Button) for the call-to-action.
 * @param {string} [props.position='center'] - Optional. The position of the call-to-action ('top' or 'center').
 * @returns {JSX.Element} - The rendered DetailedCTA component.
 */

const DetailedCTA = ({
  illustration = null,
  heading = null,
  body = null,
  action = null,
  position = 'top',
  footer = null,
  isInLandscapeMode = false,
}: DetailedCTAPropTypes) => (
  <div
    className={classNames(
      styles.container,
      isInLandscapeMode && styles.landScapeMode,
    )}
    style={{
      alignSelf:
        position === 'center' && !isInLandscapeMode ? 'center' : undefined,
    }}
    data-testid={DETAILED_CTA_DATA_TEST_ID}
  >
    {illustration && (
      <div className={styles.illustrationWrapper}>{illustration}</div>
    )}

    {heading}

    {body}

    {action && <div className={styles.buttonWrapper}>{action}</div>}
    {footer && <p className={styles.footer}>{footer}</p>}
  </div>
);

export default DetailedCTA;
