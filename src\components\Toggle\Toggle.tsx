import type { ChangeEventHandler } from 'react';
import React from 'react';

import styles from './Toggle.module.css';

interface ToggleProps {
  id: number | string;
  label?: string;
  isChecked?: boolean;
  isDisabled?: boolean;
  onChange?: ChangeEventHandler<HTMLInputElement>;
}

const Toggle = ({
  label = 'toggle',
  isChecked = false,
  isDisabled = false,
  onChange = null,
  id,
}: ToggleProps) => (
  // eslint-disable-next-line jsx-a11y/label-has-associated-control
  <label
    className={[
      styles.toggleTrack,
      isChecked && styles.toggleTrackOn,
      isDisabled && styles.toggleDisabled,
    ]
      .filter(Boolean)
      .join(' ')}
    htmlFor={id.toString()}
  >
    <input
      id={id.toString()}
      name={label}
      type="checkbox"
      onChange={onChange}
      checked={isChecked}
      disabled={isDisabled}
    />
    <span className={styles.toggleKnob} />
  </label>
);

export default Toggle;
