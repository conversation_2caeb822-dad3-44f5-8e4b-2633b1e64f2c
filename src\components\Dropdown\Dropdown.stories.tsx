import React from 'react';
import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta } from '@storybook/react';
import Button from '@components/Button';
import Dropdown from '@components/Dropdown';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';

/**
 * <br/>
 * ## Overview
 * Dropdown component is used to render a dropdown menu.
 * ## Usage
 * Import the component into your React application:
 *
 * ```jsx
 * import { Dropdown } from '@peddleon/ped-ux-react-library';
 * ```
 */
export default {
  /**
   * The title of the component in Storybook.
   */
  title: 'Components/Dropdown',
  /**
   * The component to be documented.
   */
  component: Dropdown,
  /**
   * Tags to categorize the component for documentation purposes.
   */
  tags: ['autodocs'],
  /**
   * Arguments types for the component's props to generate controls in Storybook.
   */
  argTypes: {
    place: {
      control: 'select',
      options: ['top', 'right', 'bottom', 'left'],
    },
    // You may add more controls for other props as needed
  },
  /**
   * Documentation parameters for the component.
   */
  parameters: {
    docs: {
      /**
       * Table of contents (TOC) visibility in the documentation page.
       */
      toc: true,
      /**
       * Custom page layout for the documentation.
       */
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
} as Meta;

const Template = args => (
  <div
    style={{
      height: '50vh',
    }}
  >
    <div
      style={{
        transform: 'translate(-50%, -50%)',
      }}
      className="position-absolute left-50 top-50"
    >
      <Dropdown {...args} />
    </div>
  </div>
);
const ITEM_LIST = {
  lists: [
    {
      label: 'Accept',
    },
    { label: 'Cancel' },
    {
      label: 'Edit reference ID',
    },
    {
      label: 'View',
    },
  ],
};

export const Default = Template.bind({});
Default.args = {
  /**
   * Content of the Trigger button.
   */
  children: <Button label="Trigger" id="dropdown" theme="dark" />,
  /**
   * Unique identifier for the dropdown.
   */
  id: 'dropdown',
  /**
   * Whether to open the dropdown on click.
   */
  openOnClick: true,
  /**
   * Configuration for the dropdown items.
   */
  item: ITEM_LIST,
};

const ITEM_LIST_WITH_LEFT_ICON = {
  lists: [],
};

const ITEM_LIST_WITH_RIGHT_ICON = {
  lists: [
    // {
    //   label: 'Accept',
    //   right: <CheckIcon height={20} width={20} color="green" />,
    // },
    // { label: 'Cancel', right: <XIcon height={20} width={20} color="red" /> },
    // {
    //   label: 'Edit reference ID',
    //   right: <Edit2Icon color="grey" height={20} width={20} />,
    // },
    // {
    //   label: 'View',
    //   right: <ChevronRightIcon color="grey" height={20} width={20} />,
    // },
  ],
};

const Wrapper = ({ children }) => (
  <div
    style={{
      height: '70px',
    }}
    className="w-100 position-relative"
  >
    <div
      style={{
        transform: 'translate(-50%, -50%)',
      }}
      className="position-absolute left-50 top-50"
    >
      {children}
    </div>
  </div>
);

export const DropdownSheet = () => (
  <div className="m-4">
    <ComponentGrid countColumns={3}>
      <ComponentTile label="Dropdown / Bottom">
        <Wrapper>
          <Dropdown id="test1" item={ITEM_LIST}>
            <Button label="Trigger" id="dropdown" theme="grey" />
          </Dropdown>
        </Wrapper>
      </ComponentTile>
      <ComponentTile label="Dropdown / Bottom / With left icon">
        <Wrapper>
          <Dropdown id="test2" item={ITEM_LIST_WITH_LEFT_ICON}>
            <Button label="Trigger" id="dropdown" theme="grey" />
          </Dropdown>
        </Wrapper>
      </ComponentTile>
      <ComponentTile label="Dropdown / Bottom / With Right icon">
        <Wrapper>
          <Dropdown id="test3" item={ITEM_LIST_WITH_RIGHT_ICON}>
            <Button label="Trigger" id="dropdown" theme="grey" />
          </Dropdown>
        </Wrapper>
      </ComponentTile>

      <ComponentTile label="Dropdown / Bottom / On click">
        <Wrapper>
          <Dropdown id="test4" openOnClick item={ITEM_LIST}>
            <Button label="Trigger" id="dropdown" theme="dark" />
          </Dropdown>
        </Wrapper>
      </ComponentTile>
      <ComponentTile label="Dropdown / Bottom / With left icon  / On click">
        <Wrapper>
          <Dropdown id="test5" openOnClick item={ITEM_LIST_WITH_LEFT_ICON}>
            <Button label="Trigger" id="dropdown" theme="dark" />
          </Dropdown>
        </Wrapper>
      </ComponentTile>
      <ComponentTile label="Dropdown / Bottom / With Right icon  / On click">
        <Wrapper>
          {' '}
          <Dropdown id="test6" openOnClick item={ITEM_LIST_WITH_RIGHT_ICON}>
            <Button label="Trigger" id="dropdown" theme="dark" />
          </Dropdown>
        </Wrapper>
      </ComponentTile>

      <ComponentTile label="Dropdown / Bottom / Position Top">
        <Wrapper>
          {' '}
          <Dropdown id="test7" openOnClick item={ITEM_LIST} place="top">
            <Button label="Trigger" id="dropdown" theme="grey" />
          </Dropdown>
        </Wrapper>
      </ComponentTile>
      <ComponentTile label="Dropdown / Bottom / With left icon  / Position Left">
        <Wrapper>
          <Dropdown
            id="test8"
            openOnClick
            item={ITEM_LIST_WITH_LEFT_ICON}
            place="left"
          >
            {
              // eslint-disable-next-line react/jsx-max-depth
              <Button label="Trigger" id="dropdown" theme="grey" />
            }
          </Dropdown>
        </Wrapper>
      </ComponentTile>
      <ComponentTile label="Dropdown / Bottom / With Right icon  / Position Bottom">
        <Wrapper>
          {' '}
          <Dropdown
            id="test11"
            openOnClick
            item={ITEM_LIST_WITH_RIGHT_ICON}
            place="bottom"
          >
            <Button label="Trigger" id="dropdown" theme="grey" />
          </Dropdown>
        </Wrapper>
      </ComponentTile>

      <ComponentTile label="Dropdown / Bottom / Top / useNativeDropdown">
        <Wrapper>
          {' '}
          <Dropdown
            id="test122"
            openOnClick
            item={ITEM_LIST}
            place="top"
            useNativeDropdown
          >
            <Button label="Trigger" id="dropdown" theme="dark" />
          </Dropdown>
        </Wrapper>
      </ComponentTile>
      <ComponentTile label="Dropdown / Bottom / With left icon  / Left / useNativeDropdown">
        <Wrapper>
          {' '}
          <Dropdown
            id="test223"
            openOnClick
            item={ITEM_LIST_WITH_LEFT_ICON}
            place="left"
            useNativeDropdown
          >
            <Button label="Trigger" id="dropdown" theme="dark" />
          </Dropdown>
        </Wrapper>
      </ComponentTile>
      <ComponentTile label="Dropdown / Bottom / With Right icon  / Bottom / useNativeDropdown">
        <Wrapper>
          {' '}
          <Dropdown
            id="test341"
            openOnClick
            item={ITEM_LIST_WITH_RIGHT_ICON}
            place="bottom"
            useNativeDropdown
          >
            <Button label="Trigger" id="dropdown" theme="dark" />
          </Dropdown>
        </Wrapper>
      </ComponentTile>
    </ComponentGrid>
  </div>
);
