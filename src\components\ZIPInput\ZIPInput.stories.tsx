import React, { useState } from 'react';
import { Meta } from '@storybook/react';
import ZIPInput from '@components/ZIPInput';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

/**
 * The ZIPInput component is used to input and display ZIP codes.
 *
 * ## Overview
 *
 * The ZIPInput component provides a text input field specifically designed for entering ZIP codes. It includes a label, a default value, and an optional caption.
 *
 * ## Usage
 *
 * To use the ZIPInput component in your React application, import it from the appropriate directory and render it with the desired props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import ZIPInput from '@components/ZIPInput';
 * ```
 *
 * Then, use the ZIPInput component in your JSX:
 *
 * ```jsx
 * <ZIPInput onChange={handleChange} value={zipCode} caption="Enter your ZIP code" />
 * ```
 *
 * This will render the ZIPInput component with the specified props.
 *
 */
const meta: Meta<typeof ZIPInput> = {
  title: 'Components/ZIPInput',
  tags: ['autodocs'],
  component: ZIPInput,
};

export const ZIPInputStory = () => {
  const [ZIP, setZIP] = useState('');

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <ComponentGrid countColumns={2}>
        <ComponentTile label="ZIP Input field">
          <ZIPInput onChange={setZIP} value={ZIP} caption="Austin, TX" />
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
};

ZIPInputStory.args = {};

export default meta;
