import React from 'react';
import { StatusIndicatorTheme } from '@components/Button/Button.types';
import StatusIndicator from '@components/StatusIndicator';
import { EMPTY_TABLE_VALUE } from '@constants/common';
import classNames from '@utils/classNames';

import styles from './TableText.module.css';

interface TableTextStatusIndicatorProps {
  isInverted?: boolean;
  isActive?: boolean;
  customStyle?: React.CSSProperties;
  className?: string;
  label: string;
  indicatorTheme: StatusIndicatorTheme;
}

const TableTextStatusIndicator: React.FC<TableTextStatusIndicatorProps> = ({
  isInverted,
  isActive,
  customStyle,
  className,
  label = EMPTY_TABLE_VALUE,
  indicatorTheme,
}) => (
  <div
    className={classNames(
      styles.text,
      isActive && styles.active,
      isInverted && styles.isInverted,
      className,
    )}
    style={customStyle}
  >
    <div className={styles.indicatorRoot}>
      <div className={styles.indicatorWrapper}>
        <StatusIndicator theme={indicatorTheme} />
      </div>
      <span className={styles.label}>{label}</span>
    </div>
  </div>
);

export default TableTextStatusIndicator;
