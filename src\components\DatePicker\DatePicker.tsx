// /* eslint-disable no-nested-ternary */
// /* eslint-disable react/no-unstable-nested-components */
// import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
// import { DayPicker, Mode, TZDate } from 'react-day-picker';
// import {
//   ChevronLeftIcon,
//   ChevronRightIcon,
// } from '@peddleon/ped-ux-react-icons';

// import variableStyles from '../../styles/variables.module.css';
// import { Button } from '..';
// import DatePickerFooter from './DatePickerFooter/DatePickerFooter';
// import DayButton from './DayButton/DayButton';
// import styles from './DatePicker.module.css';
// import { DatePickerPropTypes } from './DatePicker.types';

// import 'react-day-picker/style.css';

// /**
//  * Checks whether a given date is disabled based on a set of constraints (before, after, specific dates).
//  * This is used to disable dates on the calendar that are unavailable for selection.
//  */
// const isDateDisabled = (date, disabledDates, timezone: string) => {
//   if (!disabledDates) return false;

//   const tzDate = new TZDate(date, timezone);

//   if (Array.isArray(disabledDates)) {
//     return disabledDates.some(disabledDate => {
//       if (disabledDate?.before && tzDate < disabledDate?.before) return true;
//       if (disabledDate?.after && tzDate > disabledDate?.after) return true;
//       if (disabledDate instanceof Date || disabledDate instanceof TZDate) {
//         return disabledDate?.getTime() === tzDate?.getTime();
//       }
//       return false;
//     });
//   }

//   if (disabledDates?.before && tzDate < disabledDates?.before) return true;
//   if (disabledDates?.after && tzDate > disabledDates?.after) return true;
//   if (disabledDates instanceof Date || disabledDates instanceof TZDate) {
//     return tzDate.getTime() === disabledDates.getTime();
//   }

//   return false;
// };

// /**
//  * Formats a date based on the picker type and handles ranges and multiple selections.
//  */
// const formatDate = (date, pickerType, timezone) => {
//   if (!date) return undefined;
//   let formattedDate;

//   if (date?.from && pickerType === 'range') {
//     formattedDate = {
//       from: formatDate(date.from, pickerType, timezone),
//       to: formatDate(date.to, pickerType, timezone),
//     };
//   } else if (pickerType === 'multiple') {
//     formattedDate = !Array.isArray(date)
//       ? new TZDate(date, timezone)
//       : date.map(d => new TZDate(d, timezone)) ?? [];
//   } else {
//     formattedDate = new TZDate(date, timezone);
//   }
//   return formattedDate;
// };

// /**
//  * Compares two dates and determines if they are equal, ignoring the time component.
//  */
// function compareOnlyDates(date1, date2) {
//   if (!date1 || !date2) return;
//   const dateKey1 = `${date1.getFullYear()}${String(date1.getMonth()).padStart(2, '0')}${String(date1.getDate()).padStart(2, '0')}`;
//   const dateKey2 = `${date2.getFullYear()}${String(date2.getMonth()).padStart(2, '0')}${String(date2.getDate()).padStart(2, '0')}`;

//   // Direct string comparison to determine order
//   // eslint-disable-next-line no-nested-ternary, consistent-return
//   return dateKey1 === dateKey2;
// }

// /**
//  * Determines the default date or date range if not explicitly set.
//  */
// const getDefaultIfNotSet = (
//   defaultValue,
//   pickerType,
//   disabledDates,
//   timezone,
// ) => {
//   if (pickerType === 'range') {
//     const fromTo = { from: undefined, to: undefined };
//     if (defaultValue instanceof Date) {
//       return {
//         from: formatDate(defaultValue, pickerType, timezone),
//         to: formatDate(defaultValue, pickerType, timezone),
//       };
//     }
//     fromTo.from =
//       defaultValue?.from &&
//       !isDateDisabled(fromTo?.from, disabledDates, timezone)
//         ? formatDate(defaultValue.from, pickerType, timezone)
//         : undefined;
//     fromTo.to =
//       defaultValue?.to && !isDateDisabled(fromTo?.to, disabledDates, timezone)
//         ? formatDate(defaultValue.to, pickerType, timezone)
//         : undefined;
//     fromTo.from = fromTo?.from ?? fromTo?.to;
//     fromTo.to = fromTo?.to ?? fromTo?.from;
//     return fromTo;
//   }

//   if (pickerType === 'multiple') {
//     let dates = [];
//     if (Array.isArray(defaultValue)) {
//       dates = defaultValue.reduce((acc, current) => {
//         if (!isDateDisabled(current, disabledDates, timezone)) {
//           acc.push(formatDate(current, pickerType, timezone));
//         }
//         return acc;
//       }, []);
//     }
//     return dates ?? [];
//   }

//   return formatDate(defaultValue, pickerType, timezone);
// };

// /**
//  * Determines the default date or range based on the predefined selection (e.g., "today", "this week").
//  */
// const getDefaultRange = (
//   defaultValue,
//   setTo,
//   pickerType: Mode = 'single',
//   disabledDates = undefined,
//   timezone = 'America/New_York',
// ) => {
//   const today = formatDate(new TZDate(), pickerType, timezone);
//   switch (setTo) {
//     case 'today':
//       return pickerType === 'range' && today
//         ? !isDateDisabled(today, disabledDates, timezone)
//           ? { from: today, to: today }
//           : undefined
//         : today;
//     case 'tomorrow':
//       // eslint-disable-next-line no-case-declarations
//       const tomorrow = new TZDate(
//         today.getTime() + 24 * 60 * 60 * 1000,
//         timezone,
//       );
//       return pickerType === 'range'
//         ? !isDateDisabled(today, disabledDates, timezone)
//           ? { from: tomorrow, to: tomorrow }
//           : undefined
//         : tomorrow;
//     case 'this week': {
//       const startOfWeek = new TZDate(today, timezone);
//       startOfWeek.setDate(today.getDate() - today.getDay() + 1); // Monday as start of the week
//       const endOfWeek = new TZDate(startOfWeek, timezone);
//       endOfWeek.setDate(startOfWeek.getDate() + 6);
//       return pickerType === 'multiple'
//         ? Array.from({ length: 7 }, (_, i) => {
//             const date = new TZDate(startOfWeek, timezone);
//             date.setDate(startOfWeek.getDate() + i);
//             return date;
//           })
//         : { from: startOfWeek, to: endOfWeek };
//     }
//     case 'this month': {
//       const startOfMonth = new TZDate(
//         today.getFullYear(),
//         today.getMonth(),
//         1,
//         timezone,
//       );
//       const endOfMonth = new TZDate(
//         today.getFullYear(),
//         today.getMonth() + 1,
//         0,
//         timezone,
//       );
//       return pickerType === 'multiple'
//         ? Array.from({ length: endOfMonth.getDate() }, (_, i) => {
//             const date = new TZDate(startOfMonth, timezone);
//             date.setDate(startOfMonth.getDate() + i);
//             return date;
//           })
//         : { from: startOfMonth, to: endOfMonth };
//     }
//     default:
//       return getDefaultIfNotSet(
//         defaultValue,
//         pickerType,
//         disabledDates,
//         timezone,
//       );
//   }
// };

// /**
//  * The DatePicker component provides an interface for selecting dates or date ranges.
//  * It supports single, range, and multiple selection modes with customization options.
//  */
// const DatePicker = ({
//   id,
//   pickerType = 'single',
//   onDateChange,
//   disabledDates,
//   setTo,
//   isRequired,
//   excludeDisabled = false,
//   timezone,
//   value = null,
//   hasFooter = true,
//   onFooterTodayButtonClick = () => {},
//   ...restprops
// }: DatePickerPropTypes<Mode>) => {
//   const tz = useRef(timezone);
//   const [month, setMonth] = useState(new TZDate());

//   // Set timezone based on default or fallback to system timezone
//   tz.current = useMemo(
//     () =>
//       timezone ??
//       (Intl.DateTimeFormat()?.resolvedOptions()?.timeZone ||
//         'America/New_York'),
//     [timezone],
//   );

//   // Format the disabled dates
//   const formattedDisabledDates = useMemo(
//     () =>
//       disabledDates
//         ? [
//             ...(Array.isArray(disabledDates)
//               ? disabledDates.map(date => formatDate(date, pickerType, tz))
//               : disabledDates && Object.keys(disabledDates).length
//                 ? []
//                 : [disabledDates]),
//             {
//               ...(Array.isArray(disabledDates) ? {} : disabledDates),
//             },
//           ]
//         : [],
//     [disabledDates, pickerType, tz],
//   );

//   // Initialize the selected date or range
//   const [selected, setSelected] = useState(() =>
//     getDefaultRange(value, setTo, pickerType, disabledDates, tz.current),
//   );

//   // Handle date selection and update the selected date
//   const handleSelect = useCallback(
//     date => {
//       const formattedDate = formatDate(date, pickerType, tz.current);
//       if (
//         pickerType === 'range' &&
//         selected &&
//         !compareOnlyDates(selected?.to, selected?.from)
//       ) {
//         if (compareOnlyDates(selected?.to, formattedDate?.to)) {
//           formattedDate.to = formattedDate?.from;
//         } else if (compareOnlyDates(selected?.from, formattedDate?.from)) {
//           formattedDate.from = formattedDate?.to;
//         }
//       }
//       onDateChange(formattedDate);
//       setSelected(formattedDate);
//     },
//     [onDateChange, pickerType, selected],
//   );

//   const handleTodayClick = useCallback(() => {
//     let date = getDefaultRange(
//       undefined,
//       'today',
//       pickerType,
//       disabledDates,
//       tz.current,
//     );
//     if (pickerType === 'range') {
//       setMonth(date.from);
//     } else {
//       setMonth(date);
//     }
//     if (pickerType === 'multiple') {
//       date = [date];
//     }
//     onDateChange(date);
//     setSelected(date);
//     onFooterTodayButtonClick(date);
//   }, [disabledDates, onDateChange, onFooterTodayButtonClick, pickerType]);

//   // Update selected date when 'setTo' value changes
//   useEffect(() => {
//     setSelected(
//       getDefaultRange(undefined, setTo, pickerType, disabledDates, tz.current),
//     );
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, [setTo]);

//   useEffect(() => {
//     setSelected(
//       getDefaultRange(value, undefined, pickerType, disabledDates, tz.current),
//     );
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, [value]);

//   return (
//     <div id={id} className={styles.datePicker}>
//       <DayPicker
//         showOutsideDays
//         required={isRequired}
//         month={month}
//         onMonthChange={m => setMonth(new TZDate(m, tz.current))}
//         mode={pickerType}
//         selected={selected}
//         onSelect={handleSelect}
//         timeZone={tz.current}
//         footer={
//           hasFooter && <DatePickerFooter onTodayClick={handleTodayClick} />
//         }
//         components={{
//           DayButton: props => {
//             const { day, modifiers, ...buttonProps } = props;
//             const formattedDay = formatDate(day.date, pickerType, timezone);
//             return (
//               <DayButton
//                 day={formattedDay}
//                 modifiers={modifiers}
//                 buttonProps={buttonProps}
//                 pickerType={pickerType}
//                 selectedDate={selected}
//                 key={formattedDay}
//               />
//             );
//           },
//           NextMonthButton: props => (
//             <Button {...props} theme="transparent">
//               <ChevronRightIcon
//                 height={24}
//                 width={24}
//                 stroke={variableStyles.neutralDarkest}
//               />
//             </Button>
//           ),
//           PreviousMonthButton: props => (
//             <Button {...props} theme="transparent">
//               <ChevronLeftIcon
//                 height={24}
//                 width={24}
//                 stroke={variableStyles.neutralDarkest}
//               />
//             </Button>
//           ),
//           Weekday: props => (
//             <td {...props} className={styles.weekday}>
//               {String(props?.children)?.charAt(0) ?? props?.children}
//             </td>
//           ),
//         }}
//         excludeDisabled={excludeDisabled}
//         disabled={formattedDisabledDates}
//         {...restprops}
//       />
//     </div>
//   );
// };

// export default DatePicker;
