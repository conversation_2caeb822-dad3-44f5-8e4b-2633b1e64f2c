import React from 'react';
// import { ArrowDownIcon, EyeIcon } from '@peddleon/ped-ux-react-icons';
import { Meta } from '@storybook/react';

import TableText from './TableText';

/**
 * TableText component is used to render the text within table.
 * It has multiple variants to get the view which is required
 * The `isInverted` prop can be used to apply an inverted style to the table header.
 *
 * ## Usage
 *
 * To use the TableText component in your React application, import it from the appropriate directory and render it with the desired props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { TableText } from '@peddleon/ped-ux-react-library';
 * ```
 */
export default {
  title: 'Components/Table/TableText',
  component: TableText,
  argTypes: {
    isInverted: { control: 'boolean' },
    hideMaskedSuccess: { control: 'boolean' },
    maskedTextLen: { control: 'number' },
    variant: {
      control: 'select',
    },
  },
  args: {
    isInverted: false,
    isActive: false,
  },
  tags: ['autodocs'],
} as Meta;

const Template = args => <TableText {...args} />;

/**
 * Default TableText story with regular styling.
 */
export const Default = Template.bind({});

Default.args = {
  children: 'Label',
};

export const StatusIndicator = Template.bind({});

StatusIndicator.args = {
  indicatorTheme: 'warning',
  label: 'Assigned',
  variant: 'statusIndicator',
};

export const CopyClipboard = Template.bind({});

CopyClipboard.args = {
  label: 'Some sensitive information',
  variant: 'copyClipboard',
  // eslint-disable-next-line no-alert
  onClick: () => alert('Text copied to clipboard!'),
};

export const Masked = Template.bind({});

Masked.args = {
  label: 'Sensitive Data',
  variant: 'masked',
  // eslint-disable-next-line no-alert
  onClick: () => alert('Show original text!'),
};

export const Link = Template.bind({});

Link.args = {
  href: 'https://www.peddleon.com',
  variant: 'link',
  label: 'Peddleon',
  // eslint-disable-next-line no-alert
  onClick: () => alert('Show original text!'),
  component: 'a',
  target: '_blank',
};

export const Currency = Template.bind({});
Currency.args = {
  label: '10001923.2',
  variant: 'currency',
};

export const Tooltip = Template.bind({});

Tooltip.args = {
  variant: 'tooltip',
  children: 'Hover over me',
  id: 'tooltip',
  tooltip: {
    text: 'This is a tooltip message',
    placement: 'top',
  },
};

export const Date = Template.bind({});

Date.args = {
  variant: 'date',
  dateDetails: {
    date: '2021-06-01T00:00:00Z',
    startAt: '2024-04-16T07:00',
    endAt: '2024-04-16T11:00',
    timeStartAt: '07:00',
    timeEndAt: '11:00',
  },
};

export const DateTime = Template.bind({});

DateTime.args = {
  variant: 'dateTime',
  dateDetails: {
    date: '2021-06-01T00:00:00Z',
    startAt: '2024-04-16T07:00',
    endAt: '2024-04-16T11:00',
    timeStartAt: '07:00',
    timeEndAt: '11:00',
  },
};

export const Icon = Template.bind({});

Icon.args = {
  variant: 'icon',
  icons: [
    // {
    //   icon: <ArrowDownIcon height="20px" width="20px" />,
    //   id: 'arrowDown',
    // },
    // {
    //   icon: <EyeIcon height="20px" width="20px" />,
    //   id: 'eyeIcon',
    // },
  ],
};
