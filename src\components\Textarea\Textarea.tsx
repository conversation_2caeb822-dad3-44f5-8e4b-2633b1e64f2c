import { FC, useEffect, useRef } from 'react';
// import { AlertTriangleIcon, CheckIcon } from '@peddleon/ped-ux-react-icons';
import AnimatedLoaderIcon from '@components/AnimatedLoader';
import { TEXTAREA_DATA_TEST_ID } from '@constants/dataTestId';
import classNames from '@utils/classNames';

import styles from './Textarea.module.css';
import { TextAreaInputProps } from './Textarea.types';

/**
 * The Textarea component provides a textarea input field with various customizable options.
 * It supports features such as labels, placeholders, validation messages, icons, and more.
 *
 * @param {Object} props - The props for the Textarea component.
 * @param {string} [props.id=''] - Optional. The ID for the textarea input.
 * @param {string} [props.label=''] - Optional. The label text for the textarea input.
 * @param {string} [props.name=''] - Optional. The name for the textarea input.
 * @param {string} [props.error] - Optional. The error message to display.
 * @param {string} [props.caption=''] - Optional. Additional caption text to display.
 * @param {string} [props.value=''] - Optional. The value of the textarea input.
 * @param {string} [props.placeholder=''] - Optional. The placeholder text for the textarea input.
 * @param {React.ReactNode} [props.left=null] - Optional. The component to render on the left side of the textarea.
 * @param {React.ReactNode} [props.right=null] - Optional. The component to render on the right side of the textarea.
 * @param {number} [props.minLength=null] - Optional. The minimum length allowed for the textarea input.
 * @param {number} [props.maxLength=null] - Optional. The maximum length allowed for the textarea input.
 * @param {boolean} [props.hasSucceeded=false] - Optional. Specifies whether the input has succeeded.
 * @param {boolean} [props.isLoading=false] - Optional. Specifies whether the input is in a loading state.
 * @param {boolean} [props.isDisabled=false] - Optional. Specifies whether the textarea input is disabled.
 * @param {boolean} [props.isBorderless=false] - Optional. Specifies whether the textarea input has borders.
 * @param {boolean} [props.isSquare=false] - Optional. Specifies whether the textarea input has square corners.
 * @param {boolean} [props.isFocused=false] - Optional. Specifies whether the textarea input is focused.
 * @param {Function} [props.onChange] - Required. The onChange event handler for the textarea input.
 * @param {Function} [props.onFocus] - Optional. The onFocus event handler for the textarea input.
 * @param {Function} [props.onBlur] - Optional. The onBlur event handler for the textarea input.
 * @param {Function} [props.onInvalid] - Optional. The onInvalid event handler for the textarea input.
 * @param {boolean} [props.isAutoFocused] - Optional. Specifies whether the textarea input should be autofocused.
 * @param {string} [props.className=''] - Optional. Additional CSS classes for styling purposes.
 * @param {string} [props.inputMode] - Optional. The input mode for the textarea input.
 * @param {boolean} [props.required=false] - Optional. Specifies whether the textarea input is required.
 * @param {string} [props.autoComplete=''] - Optional. The autocomplete attribute for the textarea input.
 * @param {boolean} [props.hideRightIcon=false] - Optional. Specifies whether to hide the right icon.
 * @param {boolean} [props.isWrapped=true] - Optional. Specifies whether the textarea input should wrap text.
 * @param {string} [props.wrap='hard'] - Optional. The wrap attribute for the textarea input.
 * @param {string} [props.resize='vertical'] - Optional. The resize attribute for the textarea input.
 * @param {number} [props.rows=4] - Optional. The number of rows for the textarea input.
 * @param {Function} [props.onClick] - Optional. The onClick event handler for the textarea input.
 * @param {Function} [props.onCut] - Optional. The onCut event handler for the textarea input.
 * @param {Function} [props.onKeyDown] - Optional. The onKeyDown event handler for the textarea input.
 * @param {Function} [props.onPaste] - Optional. The onPaste event handler for the textarea input.
 * @returns {JSX.Element} - The rendered Textarea component.
 */
const Textarea: FC<TextAreaInputProps> = ({
  id = '',
  label = '',
  name = '',
  error,
  caption = '',
  value,
  placeholder = '',
  left = null,
  right = null,
  minLength = null,
  maxLength = null,
  hasSucceeded = false,
  isLoading = false,
  isDisabled = false,
  isBorderless = false,
  isSquare = false,
  isFocused = false,
  onChange,
  onFocus,
  onBlur,
  onInvalid,
  isAutoFocused,
  className,
  inputMode,
  required = false,
  autoComplete = '',
  hideRightIcon = false,
  wrap = 'hard',
  resize = 'vertical',
  rows = 4,
  onClick,
  onCut,
  onKeyDown,
  onPaste,
  isWrapped = true,
}) => {
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const hasId = String(id).length > 0;
  const hasValue = String(value).length > 0;

  const hasError = error?.length > 0;
  const hasCaption = caption;

  let statusRightIcon = null;
  if (hasSucceeded) {
    // statusRightIcon = (
    //   <span className={styles.successIconWrapper}>
    //     <CheckIcon height={24} width={24} />
    //   </span>
    // );
  } else if (hasError) {
    // statusRightIcon = (
    //   <span className={styles.errorIconWrapper}>
    //     <AlertTriangleIcon height={24} width={24} />
    //   </span>
    // );
  } else if (isLoading) {
    statusRightIcon = (
      <span className={styles.fetchingIconWrapper}>
        <AnimatedLoaderIcon />
      </span>
    );
  }

  if (hideRightIcon) {
    statusRightIcon = null;
  }

  const INPUT_ID = hasId ? id : null;
  const DESCRIBED_BY_ID = INPUT_ID ? `${INPUT_ID}-description` : null;

  useEffect(() => {
    if (isFocused) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (inputRef?.current as any)?.focus({ focusVisible: true });
    }
  }, [isFocused]);

  return (
    <div
      className={classNames(
        styles.textareaWrapper,
        isDisabled && styles.disabled,
        isBorderless && styles.borderless,
        isSquare && styles.square,
        hasValue && styles.filled,
        hasError && styles.errored,
        hasCaption && styles.captioned,
        className,
      )}
    >
      <div className={styles.container}>
        {left && <span className={styles.leftWrapper}>{left}</span>}

        <div className={styles.inputWrapper}>
          {label.length > 0 && (
            <label htmlFor={INPUT_ID} className={styles.label}>
              {label}
            </label>
          )}

          <textarea
            ref={inputRef}
            id={INPUT_ID}
            name={name || INPUT_ID}
            className={classNames(
              isDisabled && styles.disabled,
              styles.textarea,
              label.length > 0 && styles.hasLabel,
              (right || hasError || hasSucceeded || isLoading) &&
                styles.hasRight,
              isWrapped && styles.wrapped,
            )}
            style={{
              resize,
            }}
            value={value}
            rows={rows}
            placeholder={placeholder}
            disabled={isDisabled}
            onChange={onChange}
            minLength={minLength}
            maxLength={maxLength}
            required={required}
            onClick={onClick}
            onCut={onCut}
            onKeyDown={onKeyDown}
            onPaste={onPaste}
            onFocus={() => {
              if (onFocus) {
                onFocus();
              }
            }}
            onBlur={() => {
              inputRef.current.checkValidity();
              if (onBlur) {
                onBlur();
              }
            }}
            onInvalid={() => {
              if (onInvalid) {
                onInvalid({
                  id,
                  label,
                  value,
                  validity: inputRef.current.validity,
                });
              }
            }}
            // eslint-disable-next-line jsx-a11y/no-autofocus
            autoFocus={isAutoFocused}
            inputMode={inputMode}
            wrap={wrap}
            aria-describedby={DESCRIBED_BY_ID}
            aria-invalid={hasError}
            autoComplete={autoComplete}
            data-testid={TEXTAREA_DATA_TEST_ID}
          />
        </div>

        {(right || hasError || hasSucceeded || isLoading) && (
          <span className={styles.rightWrapper}>
            {right}
            {statusRightIcon}
          </span>
        )}
      </div>

      {(hasError || hasCaption) && (
        <div className={styles.footer} id={DESCRIBED_BY_ID}>
          {hasError && <span className={styles.error}>{error}</span>}
          {hasCaption && <span className={styles.caption}>{caption}</span>}
        </div>
      )}
    </div>
  );
};

export default Textarea;
