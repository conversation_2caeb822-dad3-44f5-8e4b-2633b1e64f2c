// import React from 'react';
// import { CheckIcon, ClipboardIcon } from '@peddleon/ped-ux-react-icons';
// import Button from '@components/Button';
// import { ButtonTheme } from '@components/Button/Button.types';
// import { EMPTY_TABLE_VALUE } from '@constants/common';
// import classNames from '@utils/classNames';

// import styles from './TableText.module.css';

// interface TableTextCopyClipboardProps {
//   isInverted?: boolean;
//   isActive?: boolean;
//   customStyle?: React.CSSProperties;
//   className?: string;
//   label: string;
//   handleCopyClipboard: (
//     event: React.MouseEvent<HTMLButtonElement, MouseEvent>,
//   ) => void;
//   isTextCopied: boolean;
//   hideMaskedSuccess?: boolean;
//   iconButtonTheme?: ButtonTheme;
// }

// const TableTextCopyClipboard: React.FC<TableTextCopyClipboardProps> = ({
//   isInverted,
//   isActive,
//   customStyle,
//   className,
//   label = EMPTY_TABLE_VALUE,
//   handleCopyClipboard,
//   isTextCopied,
//   hideMaskedSuccess,
//   iconButtonTheme,
// }) => (
//   <div
//     className={classNames(
//       isInverted && styles.isInverted,
//       styles.text,
//       isActive && styles.active,
//       className,
//     )}
//     style={customStyle}
//   >
//     <div className={styles.indicatorRoot}>
//       <div className={styles.label}>{label}</div>

//       <Button
//         theme={iconButtonTheme}
//         size="small"
//         onClick={handleCopyClipboard}
//         id="copyClipboard"
//       >
//         {isTextCopied && !hideMaskedSuccess ? <CheckIcon /> : <ClipboardIcon />}
//       </Button>
//     </div>
//   </div>
// );

// export default TableTextCopyClipboard;
