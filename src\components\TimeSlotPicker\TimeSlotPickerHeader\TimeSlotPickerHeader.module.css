@value variables: "../../../styles/variables.module.css";
@value oneSpace, twoSpace, threeSpace from variables;
@value black10, black50, black70, primaryDark from variables;
@value smallWidth, mediumWidth from variables;

.header {
  width: 100%;
  border-radius: 8px;
  border: solid 1px black10;

  @media (max-width: smallWidth) {
    border: none;
  }
}

.container {
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  max-width: 100%;
  overflow-x: auto;
}

.slider {
  flex: 1;
}

.slide {
  text-align: center;
}

.column {
  text-align: center;
  border-top: solid 1px transparent;
  padding: twoSpace threeSpace;

  &:hover,
  &:focus,
  &.active {
    outline: none;
    color: primaryDark;

    & .weekday {
      color: primaryDark;
    }

    & .month {
      color: primaryDark;
    }
  }

  @media (max-width: smallWidth) {
    padding: 0 twoSpace;
  }

  @media (min-width: smallWidth) {
    &.visible {
      border-top-color: primaryDark;
    }
  }
}

.buttonBack,
.buttonNext {
  &[disabled] {
    display: none;
  }
}

.back,
.next {
  padding: twoSpace;
  color: black70;
  line-height: 0;
  font-size: 20px;

  @media (max-width: smallWidth) {
    font-size: 16px;
    border-radius: 50%;
    background-color: black10;
    padding: oneSpace;
  }
}

.next {
  margin-right: twoSpace;

  @media (max-width: mediumWidth) {
    margin-right: 0;
  }
}

.back {
  margin-left: twoSpace;

  @media (max-width: mediumWidth) {
    margin-left: 0;
  }
}
