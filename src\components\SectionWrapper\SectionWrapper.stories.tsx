/* eslint-disable no-console */
// import { CircleIcon } from '@peddleon/ped-ux-react-icons';
import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta } from '@storybook/react';
import Button from '@components/Button';

import SectionWrapper from './SectionWrapper';
import { SectionWrapperProps } from './SectionWrapper.types';
/**
 *
 * <br/>

## Overview

The Section Wrapper component is a versatile container for organizing content within a UI section.
It offers features like customizable headings, icons, and action buttons.
To construct the section wrapper from an integration perspective,
developers need to provide props as shown in the Storybook and as described in the props documentation.

## Usage

Import the component into your React application:

```jsx
import { SectionWrapper } from '@peddleon/ped-ux-react-library';```

 */

const meta: Meta<typeof SectionWrapper> = {
  component: SectionWrapper,
  title: 'Components/SectionWrapper',
  tags: ['autodocs'],
  parameters: {
    docs: {
      toc: true,
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
};

export const SectionWrapperStory = ({
  headingText,
  actions,
  options,
  icon,
}: SectionWrapperProps) => (
  <SectionWrapper
    headingText={headingText}
    icon={icon}
    actions={actions}
    options={options}
  >
    <div>
      {`Lorem Ipsum is simply dummy text of the printing and typesetting industry.
      Lorem Ipsum has been the industry's standard dummy text ever since the
      1500s, when an unknown printer took a galley of type and scrambled it to
      make a type specimen book. It has survived not only five centuries, but
      also the leap into electronic typesetting, remaining essentially
      unchanged. Lorem Ipsum is simply dummy text of the printing and
      typesetting industry. Lorem Ipsum has been the industry's standard dummy
      text ever since the 1500s, when an unknown printer took a galley of type
      and scrambled it to make a type specimen book. It has survived not only
      five centuries, but also the leap into electronic typesetting, remaining
      essentially unchanged.`}
    </div>
  </SectionWrapper>
);

SectionWrapperStory.args = {
  headingText: 'Publisher Peddle Integration',
  // icon: <CircleIcon height={24} width={24} />,
  actions: [
    [
      <Button
        key="button1"
        label="Button 1"
        theme="light"
        onClick={() => console.log('Action 2 clicked')}
      />,
    ],
  ],
  options: {
    hasDevider: true,
    dividerTheme: 'grey',
    actionDirection: 'right',
    headingTag: 'h2',
  },
};

SectionWrapperStory.argTypes = {
  headingText: {
    control: 'text',
    defaultValue: 'Publisher Peddle Integration',
  },
  actions: {
    type: 'array',
  },
  options: {
    type: 'object',
    defaultValue: {
      hasDevider: true,
      dividerTheme: 'grey',
      actionDirection: 'right',
      headingTag: 'h2',
    },
  },
};
export default meta;
