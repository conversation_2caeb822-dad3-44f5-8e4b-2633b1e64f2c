### ☑️ Checklist for creating a PR

- [ ] Filled in PR description with relevant links to tickets, other Github
      PRs/issues, testing steps, and future work, as applicable
- [ ] Named PR title appropriately; starts with verb, and accurately describes the chages made
- [ ] Named variables appropriately; consistently named with rest of codebase, and follows patterns like Boolean variables prefixed with `is`/`can`/`should`
- [ ] Commented code where control flow or intent is not obvious
- [ ] If console statements left in this PR, they are intentionally meant for production
- [ ] If UI work, built mobile (80% traffic) and desktop (19% traffic), and everything in-between (1% traffic)
- [ ] If UI work, updated relevant Storybook stories
- [ ] If tech debt has been introduced, called out the future work explicitly

### ☑️ Checklist for merging a PR

- [ ] Issued PR, and code checks have settled
- [ ] Ensured code checks are green; linting, static analysis, tests, and immutable deployment(s)
- [ ] Read my own PR and called out specific areas worth reviewing more carefully
- [ ] If merge conflict introduced, rebased this branch to resolve conflicts
