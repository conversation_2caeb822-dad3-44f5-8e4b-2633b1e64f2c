/* eslint-disable no-console */
import React from 'react';
import { Meta } from '@storybook/react';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

import Alert from './Alert';
import { AlertProps } from './Alert.types';

/**
 * The Alert component provides a customizable alert box for displaying messages or notifications to users.
 * It allows for easy customization of themes, content, and optional cancellation functionality.
 *
 * ## Overview
 *
 * The Alert component is designed to display informative messages or notifications to users in a visually appealing way. It offers flexibility in theming, allowing you to match the alert style with your application's design.
 *
 * ## Usage
 *
 * To use the Alert component in your React application, import it from the appropriate directory and include it in your JSX.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { Alert } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the Alert component and pass relevant props to customize its appearance and behavior:
 *
 * ```jsx
 * <Alert
 *       theme="success"
 *       body="This is a success message."
 *       isCancellable={false}
 *       onCancelButtonClick={() => {}}
 *       isAnimated={false}
 *     />
 * ```
 *
 * This will render an alert box with a success theme, displaying the provided message. It also includes a cancel button with animation effects and triggers a callback function when clicked.
 */

const meta: Meta<typeof Alert> = {
  title: 'Components/Alert',
  tags: ['autodocs'],
  component: Alert,
};

export const AlertStory = (alertProps: AlertProps) => <Alert {...alertProps} />;

AlertStory.args = {
  body: "We'd like to verify your VIN",
  theme: 'primary',
  left: null,
  isCancellable: false,
  isAnimated: false,
};

export const AlertSheet = ({
  body,
}: {
  body: {
    message: string;
    success: string;
    error: string;
  };
}) => (
  <div
    style={{
      padding: variableStyles.fiveSpace,
    }}
  >
    <ComponentGrid countColumns={2}>
      <ComponentTile label="Message">
        <Alert theme="primary" body={body.message} />
      </ComponentTile>

      <ComponentTile label="Message Inverted">
        <Alert theme="primaryInverted" body={body.message} />
      </ComponentTile>

      <ComponentTile label="Success">
        <Alert theme="success" body={body.success} />
      </ComponentTile>

      <ComponentTile label="Success Inverted">
        <Alert theme="successInverted" body={body.success} />
      </ComponentTile>

      <ComponentTile label="Error">
        <Alert theme="danger" body={body.error} />
      </ComponentTile>

      <ComponentTile label="Error Inverted">
        <Alert theme="dangerInverted" body={body.error} />
      </ComponentTile>

      <ComponentTile label="Error Inverted">
        <Alert theme="dangerFaded" body={body.message} />
      </ComponentTile>

      <ComponentTile label="Warning">
        <Alert theme="warning" body={body.message} />
      </ComponentTile>

      <ComponentTile label="Success Cancallable">
        <Alert
          theme="success"
          body="Text has been copied"
          isCancellable
          onCancelButtonClick={() => console.log('onCancel')}
          isAnimated
        />
      </ComponentTile>
    </ComponentGrid>
  </div>
);

AlertSheet.args = {
  body: {
    message: "We'd like to verify your VIN",
    success: 'Awesome! We were able to verify that VIN',
    error: "Hmm, we weren't able to verify that VIN",
  },
};

export default meta;
