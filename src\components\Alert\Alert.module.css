@value variables: "../../styles/variables.module.css";
@value oneSpace, twoSpace from variables;
@value typography: "../../styles/typography.module.css";
@value buttonLabelSmall from typography;

.alert {
  justify-content: center;
  align-items: center;
  display: flex;
  padding: oneSpace twoSpace;
  border: 1px solid var(--alertThemeBorderColor);
  border-radius: 8px;
  pointer-events: none;
  background-position: right;
}

.alertContent {
  composes: buttonLabelSmall;
  display: flex;
  align-items: center;
}

.iconWrapper {
  line-height: 0;
  margin-right: oneSpace;
}

.alignIconRight {
  justify-content: space-between;
}

.isCancellable {
  pointer-events: all;
  background: linear-gradient(
      to left,
      var(--animationThemeColorDark) 50%,
      var(--animationThemeColor) 50%
    )
    right;
  background-size: 200% 100%;
  background-position: right;
  transition: all 2s ease;
  border: none;
}

.isCancellable.animateColorLeftToRight {
  background-position: left;
}

.cancelButton {
  pointer-events: all;
  cursor: pointer;
  border: none;
  width: unset;
}

.oneSpacePadding {
  padding-right: oneSpace;
}
