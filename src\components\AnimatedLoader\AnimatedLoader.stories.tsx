import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta } from '@storybook/react';
import AnimatedLoaderIcon from '@components/AnimatedLoader';
import variableStyles from '@styles/variables.module.css';

import { AnimatedLoaderProps } from './AnimatedLoader.types';
/**

Import the component into your React application:

```jsx
import { AnimatedLoaderIcon } from '@peddleon/ped-ux-react-library';```
 
 */
const meta: Meta<typeof AnimatedLoaderIcon> = {
  title: 'Core/Utility',
  tags: ['autodocs'],
  component: AnimatedLoaderIcon,
  parameters: {
    docs: {
      toc: true,
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
};

export const AnimatedLoader = ({
  color,
  theme,
  size,
  isDisabled,
}: AnimatedLoaderProps) => (
  <AnimatedLoaderIcon
    color={color}
    theme={theme}
    size={size}
    isDisabled={isDisabled}
  />
);
AnimatedLoader.args = {
  color: variableStyles.black,
  theme: 'dark',
  size: 24,
  isDisabled: false,
};
AnimatedLoader.argTypes = {
  theme: {
    options: ['dark', 'light'],
    control: {
      type: 'select',
    },
  },
};

export default meta;
