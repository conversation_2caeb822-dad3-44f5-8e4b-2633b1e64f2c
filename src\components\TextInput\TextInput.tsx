import {
  forwardRef,
  ForwardRefExoticComponent,
  Ref,
  RefAttributes,
  useEffect,
  useRef,
} from 'react';
// import { AlertTriangleIcon, CheckIcon } from '@peddleon/ped-ux-react-icons';
import AnimatedLoaderIcon from '@components/AnimatedLoader';
import { TEXT_INPUT_DATA_TEST_ID } from '@constants/dataTestId';
import classNames from '@utils/classNames';
import mergeRefs from '@utils/mergeRefs';

import styles from './TextInput.module.css';
import { TextInputProps } from './TextInput.types';

/**
 * The TextInput component provides an input field with various customizable options.
 * It supports features such as labels, placeholders, validation messages, icons, and more.
 *
 * @param {Object} props - The props for the TextInput component.
 * @param {string} [props.id=''] - Optional. The ID for the input element.
 * @param {string} [props.label=''] - Optional. The label text for the input element.
 * @param {string} [props.name=''] - Optional. The name for the input element.
 * @param {string} [props.error] - Optional. The error message to display.
 * @param {string} [props.caption=''] - Optional. Additional caption text to display.
 * @param {string} [props.value=''] - Optional. The value of the input element.
 * @param {string} [props.placeholder=''] - Optional. The placeholder text for the input element.
 * @param {React.ReactNode} [props.left=null] - Optional. The component to render on the left side of the input.
 * @param {React.ReactNode} [props.right=null] - Optional. The component to render on the right side of the input.
 * @param {string} [props.type='text'] - Optional. The type of the input element.
 * @param {number} [props.minLength=null] - Optional. The minimum length allowed for the input element.
 * @param {number} [props.maxLength=null] - Optional. The maximum length allowed for the input element.
 * @param {boolean} [props.hasSucceeded=false] - Optional. Specifies whether the input has succeeded.
 * @param {boolean} [props.isLoading=false] - Optional. Specifies whether the input is in a loading state.
 * @param {boolean} [props.isDisabled=false] - Optional. Specifies whether the input element is disabled.
 * @param {boolean} [props.isBorderless=false] - Optional. Specifies whether the input element has borders.
 * @param {boolean} [props.isSquare=false] - Optional. Specifies whether the input element has square corners.
 * @param {boolean} [props.isFocused=false] - Optional. Specifies whether the input element is focused.
 * @param {boolean} [props.shouldHideErrorMessage=false] - Optional. Specifies whether the error message should be hidden.
 * @param {Function} [props.onChange] - Required. The onChange event handler for the input element.
 * @param {Function} [props.onFocus] - Optional. The onFocus event handler for the input element.
 * @param {Function} [props.onBlur] - Optional. The onBlur event handler for the input element.
 * @param {Function} [props.onInvalid] - Optional. The onInvalid event handler for the input element.
 * @param {boolean} [props.isAutoFocused] - Optional. Specifies whether the input element should be autofocused.
 * @param {string} [props.className=''] - Optional. Additional CSS classes for styling purposes.
 * @param {string} [props.inputMode] - Optional. The input mode for the input element.
 * @param {string} [props.pattern] - Optional. The pattern attribute for the input element.
 * @param {boolean} [props.required=false] - Optional. Specifies whether the input element is required.
 * @param {Function} [props.onKeyDown] - Optional. The onKeyDown event handler for the input element.
 * @param {Function} [props.onClick] - Optional. The onClick event handler for the input element.
 * @param {Function} [props.onPaste] - Optional. The onPaste event handler for the input element.
 * @param {Function} [props.onCut] - Optional. The onCut event handler for the input element.
 * @param {string} [props.autoComplete=''] - Optional. The autoComplete attribute for the input element.
 * @param {boolean} [props.hideRightIcon=false] - Optional. Specifies whether to hide the right icon.
 * @returns {JSX.Element} - The rendered TextInput component.
 */
const TextInput: ForwardRefExoticComponent<
  Omit<TextInputProps & { ref?: Ref<HTMLDivElement> }, 'ref'> &
    RefAttributes<HTMLDivElement>
> = forwardRef(
  (
    {
      id = '',
      label = '',
      name = '',
      error,
      caption = '',
      value,
      placeholder = '',
      left = null,
      right = null,
      type = 'text',
      minLength = null,
      maxLength = null,
      hasSucceeded = false,
      isLoading = false,
      isDisabled = false,
      isBorderless = false,
      isSquare = false,
      isFocused = false,
      shouldHideErrorMessage = false,
      onChange,
      onFocus,
      onBlur,
      onInvalid,
      isAutoFocused,
      className,
      inputMode,
      pattern,
      required = false,
      onKeyDown,
      onClick,
      onPaste,
      onCut,
      onCopy,
      autoComplete = '',
      hideRightIcon = false,
      readOnly = false,
      hideErrorMessage = false,
      title = '',
      isNewVariant = false,
    },
    ref,
  ) => {
    const inputRef = useRef<HTMLInputElement>(null);
    const mergedRefs = mergeRefs(ref, inputRef);

    const hasId = String(id).length > 0;
    const hasValue = String(value).length > 0;

    const hasError = error?.length > 0;
    const hasCaption =
      typeof caption === 'string' ? caption.length > 0 : caption;

    let statusRightIcon = null;
    // if (hasSucceeded) {
    //   statusRightIcon = (
    //     <span className={styles.successIconWrapper}>
    //       <CheckIcon height={24} width={24} />
    //     </span>
    //   );
    // } else if (hasError) {
    //   statusRightIcon = (
    //     <span className={styles.errorIconWrapper}>
    //       <AlertTriangleIcon height={24} width={24} />
    //     </span>
    //   );
    // } else

    if (isLoading) {
      statusRightIcon = (
        <span className={styles.fetchingIconWrapper}>
          <AnimatedLoaderIcon />
        </span>
      );
    }

    if (hideRightIcon) {
      statusRightIcon = null;
    }

    const INPUT_ID = hasId ? id : null;
    const DESCRIBED_BY_ID = INPUT_ID ? `${INPUT_ID}-description` : null;

    useEffect(() => {
      if (isFocused) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (inputRef?.current as any)?.focus({ focusVisible: true });
      }
    }, [isFocused]);

    return (
      <div
        className={classNames(
          styles.textInput,
          isDisabled && styles.disabled,
          isBorderless && styles.borderless,
          isSquare && styles.square,
          hasValue && styles.filled,
          hasError && styles.errored,
          hasCaption && styles.captioned,
          isNewVariant && styles.newVarientWrapper,
          className,
        )}
      >
        <div className={styles.container}>
          {left && <span className={styles.leftWrapper}>{left}</span>}

          <div className={styles.inputWrapper}>
            {label.length > 0 && (
              <label htmlFor={INPUT_ID} className={styles.label}>
                {label}
              </label>
            )}

            <input
              ref={mergedRefs}
              id={INPUT_ID}
              name={name || INPUT_ID}
              className={classNames(
                styles.input,
                isDisabled && styles.disabled,
                label.length > 0 && styles.hasLabel,
              )}
              type={type}
              value={value}
              placeholder={placeholder}
              disabled={isDisabled}
              onChange={onChange}
              minLength={minLength}
              maxLength={maxLength}
              required={required}
              onKeyDown={onKeyDown}
              readOnly={readOnly}
              autoComplete={autoComplete}
              onFocus={() => {
                if (onFocus) {
                  onFocus();
                }
              }}
              onBlur={() => {
                inputRef.current.checkValidity();
                if (onBlur) {
                  onBlur();
                }
              }}
              onInvalid={() => {
                if (onInvalid) {
                  onInvalid({
                    id,
                    label,
                    value,
                    validity: inputRef.current.validity,
                    name,
                  });
                }
              }}
              // eslint-disable-next-line jsx-a11y/no-autofocus
              autoFocus={isAutoFocused}
              inputMode={inputMode}
              pattern={pattern}
              aria-describedby={DESCRIBED_BY_ID}
              aria-invalid={hasError}
              onClick={onClick}
              onCopy={onCopy}
              onPaste={onPaste}
              onCut={onCut}
              data-testid={TEXT_INPUT_DATA_TEST_ID}
              title={title}
            />
          </div>

          {(right || hasError || hasSucceeded || isLoading) && (
            <span className={styles.rightWrapper}>
              {right}
              {statusRightIcon}
            </span>
          )}
        </div>

        {((hasError && !shouldHideErrorMessage) || hasCaption) && (
          <div className={styles.footer} id={DESCRIBED_BY_ID}>
            {/* TODO: refactor the code for error and caption hidden */}
            {hasError && (
              <span
                className={classNames(
                  styles.error,
                  hideErrorMessage && 'd-none',
                )}
              >
                {error}
              </span>
            )}
            {hasCaption && <span className={styles.caption}>{caption}</span>}
          </div>
        )}
      </div>
    );
  },
);

export default TextInput;
