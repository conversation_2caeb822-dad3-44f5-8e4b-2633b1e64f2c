import { ReactNode } from 'react';

export interface ModalProps {
  /**
   * Children can be placed within the children placeholder in the JSX of the modal
   */
  children: ReactNode;
  /**
   * Callback handler triggered when the close button is clicked
   */
  onClose?: () => void;
  /**
   * Callback handler triggered when the back button is clicked
   */
  hasCloseButton?: boolean;
  /**
   * To display/hide back button on the left of header
   */
  className?: string;
  /**
   * To display/hide shadow around modal
   */
  isShadowless?: boolean;
  /**
   * To add space around content of modal
   */
  hasPadding?: boolean;
  /**
   * To remove extra spacing from content and header
   */
  hasDialogWrapperStyles?: boolean;
  /**
   * To make modal scrollable
   */
  isScrollable?: boolean;
  /**
   * has max height: when modal have auto scroll and max height will be 95vh
   */
  hasMaxHeight?: boolean;
}
