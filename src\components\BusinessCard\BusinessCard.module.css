
.businessCardContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.formContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.inputGroup {
  grid-column: span 1;
}

.inputGroup:nth-child(7),
.inputGroup:nth-child(8) {
  grid-column: span 2;
}

.buttonContainer {
  grid-column: span 2;
  display: flex;
  justify-content: center;
  /* margin-top: 1rem; */
}

.qrCodeContainer {
  display: none;
}

.qrCode {
  background: transparent;
  padding: 0.5rem;
} 