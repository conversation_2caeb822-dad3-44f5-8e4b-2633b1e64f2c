import { BusinessCardFormData } from '../components/BusinessCard/BusinessCard.types';

export const validateEmail = (email: string): string | undefined => {
    if (!email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
        return 'Please enter a valid email address';
    }
    return undefined;
};

const requiredFields: (keyof BusinessCardFormData)[] = [
    'firstName',
    'lastName',
    'email',
    'organization',
    'title'
];

export const validateField = (
    name: keyof BusinessCardFormData,
    value: string,
): string | undefined => {
    if (requiredFields.includes(name) && value.trim() === '') {
        return 'This field is required';
    }

    switch (name) {
        case 'email':
            return value.trim() ? validateEmail(value) : undefined;
        default:
            return undefined;
    }
}; 