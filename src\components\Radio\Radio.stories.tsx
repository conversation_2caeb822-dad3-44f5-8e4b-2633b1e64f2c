import { useEffect, useState } from 'react';
import { Meta } from '@storybook/react';
import ComponentGrid from '@stories/ComponentGrid';
import ComponentTile from '@stories/ComponentTile';
import variableStyles from '@styles/variables.module.css';

import Radio from './Radio';
/**
 * The Radio component provides a styled radio input with customizable label and state.
 * It supports various states such as checked, disabled, and can be
 * configured to have a button-like appearance.
 *
 * ## Usage
 *
 * To use the Radio component in your React application, import it from the appropriate directory
 * and include it in your JSX. You can customize its appearance and behavior using the provided props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { Radio } from '@peddleon/ped-ux-react-library';
 * ```
 *
 * Then, use the Radio component with your desired configuration:
 *
 * ```jsx
 * <Radio
 *   id="myRadio"
 *   label="Option 1"
 *   isChecked={true}
 *   isDisabled={false}
 *   onChange={handleChange}
 * />
 * ```
 *
 * This will render a styled radio input with the label "Option 1", which is initially checked
 * and enabled. The `onChange` prop allows you to handle changes to the radio state.
 */
const meta: Meta<typeof Radio> = {
  title: 'Components/Radio',
  tags: ['autodocs'],
  component: Radio,
};

export const RadioStory = ({ label, id, isChecked, ...restProp }) => {
  const [state, setState] = useState(isChecked);
  useEffect(() => setState(isChecked), [isChecked]);

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
      }}
    >
      <Radio
        id={id}
        isChecked={state}
        label={label}
        {...restProp}
        onChange={event => setState((event.target as HTMLInputElement).checked)}
      />
    </div>
  );
};
RadioStory.args = {
  label: 'Label',
  isChecked: false,
  isDisabled: false,
  isInButtonShape: false,
  onChange: () => {},
  id: 'story-standard',
  name: '',
  value: '',
};

export const RadioSheet = ({ label }: { label: string }) => {
  const [state, setState] = useState({
    standard: false,
    withLabel: false,
  });

  return (
    <div
      style={{
        padding: variableStyles.fiveSpace,
        display: 'flex',
        flexDirection: 'column',
        rowGap: '30px',
      }}
    >
      <ComponentGrid countColumns={2}>
        <ComponentTile label="Standard">
          <Radio
            id="sheet-standard"
            isChecked={state.standard}
            onChange={event =>
              setState({
                ...state,
                standard: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Disabled">
          <Radio
            id="sheet-standard-disabled"
            isChecked={state.standard}
            isDisabled
            onChange={event =>
              setState({
                ...state,
                standard: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Labelled">
          <Radio
            id="sheet-standard-labelled"
            label={label}
            isChecked={state.withLabel}
            onChange={event =>
              setState({
                ...state,
                withLabel: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="Standard / Labelled / Disabled">
          <Radio
            id="sheet-standard-labelled-disabled"
            label={label}
            isChecked={state.withLabel}
            onChange={event =>
              setState({
                ...state,
                withLabel: (event.target as HTMLInputElement).checked,
              })
            }
            isDisabled
          />
        </ComponentTile>
      </ComponentGrid>
      <ComponentGrid countColumns={2}>
        <ComponentTile label="IsInButtonShape / Standard">
          <Radio
            id="sheet-standard"
            isChecked={state.standard}
            isInButtonShape
            onChange={event =>
              setState({
                ...state,
                standard: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="IsInButtonShape / Standard / Disabled">
          <Radio
            id="sheet-standard-disabled"
            isChecked={state.standard}
            isDisabled
            isInButtonShape
            onChange={event =>
              setState({
                ...state,
                standard: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="IsInButtonShape / Standard / Labelled">
          <Radio
            id="sheet-standard-labelled"
            label={label}
            isInButtonShape
            isChecked={state.withLabel}
            onChange={event =>
              setState({
                ...state,
                withLabel: (event.target as HTMLInputElement).checked,
              })
            }
          />
        </ComponentTile>
        <ComponentTile label="IsInButtonShape / Standard / Labelled / Disabled">
          <Radio
            id="sheet-standard-labelled-disabled"
            label={label}
            isInButtonShape
            isChecked={state.withLabel}
            onChange={event =>
              setState({
                ...state,
                withLabel: (event.target as HTMLInputElement).checked,
              })
            }
            isDisabled
          />
        </ComponentTile>
      </ComponentGrid>
    </div>
  );
};
RadioSheet.args = {
  label: 'Label',
};

export default meta;
