import { ReactNode } from 'react';
import { TextInputProps } from '@components/TextInput/TextInput.types';

export interface ZIPInputPropTypes {
  /**
   * Unique identifier for the input field.
   */
  id?: string;
  /**
   * Name of the input field.
   */
  name?: string;
  /**
   * Current value of the input field.
   */
  value: string;
  /**
   * Callback function triggered when the value of the input field changes.
   * @param formattedValue - The formatted value of the input field.
   */
  onChange: (formattedValue: string) => void;
  /**
   * Callback function triggered when the input field receives focus.
   */
  onFocus?: () => void;
  /**
   * Callback function triggered when the input field loses focus.
   */
  onBlur?: () => void;
  /**
   * Callback function triggered when the input field is invalid.
   */
  onInvalid?: TextInputProps['onInvalid'];
  /**
   * Indicates whether the input field is currently fetching data.
   */
  isFetching?: boolean;
  /**
   * Indicates whether the input field should have a square shape.
   */
  isSquare?: boolean;
  /**
   * Indicates whether the input field should be borderless.
   */
  isBorderless?: boolean;
  /**
   * React node to be rendered on the left side of the input field.
   */
  left?: ReactNode;
  /**
   * Additional CSS class name for the input field.
   */
  className?: string;
  /**
   * Label text for the input field.
   */
  label?: string;
  /**
   * Indicates whether the input field has succeeded.
   */
  hasSucceeded?: boolean;
  /**
   * Error message to be displayed for the input field.
   */
  error?: string;
  /**
   * Indicates whether the error message should be hidden.
   */
  shouldHideErrorMessage?: boolean;
  /**
   * Indicates whether the input field is required.
   */
  required?: boolean;
  /**
   * React node to be rendered below the input field.
   */
  caption?: ReactNode;
  /**
   * Indicates whether the input field is disabled.
   */
  isDisabled?: boolean;
  /**
   * Input mode for the input field.
   */
  inputMode?: TextInputProps['inputMode'];
  /**
   * Maximum length of the input field value.
   */
  maxLength?: number;
}
