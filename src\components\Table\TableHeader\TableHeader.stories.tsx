import React from 'react';
import { Meta } from '@storybook/react';

import TableHeader from './TableHeader';

/**
 * TableHeader component is used to render the header of a table.
 * It accepts an array of columns, each defining the properties of a column header.
 * Each column can be sorted if specified by the `sortingEnabled` prop.
 * The `isInverted` prop can be used to apply an inverted style to the table header.
 *
 * ## Usage
 *
 * To use the TableHeader component in your React application, import it from the appropriate directory and render it with the desired props.
 *
 * Import the component into your React application:
 *
 * ```jsx
 * import { TableHeader } from '@peddleon/ped-ux-react-library';
 * ```
 */
export default {
  title: 'Components/Table/TableHeader',
  component: TableHeader,
  argTypes: {
    isInverted: { control: 'boolean' },
  },
  tags: ['autodocs'],
} as Meta;

const Template = args => <TableHeader {...args} />;

/**
 * Default TableHeader story with regular styling.
 */
export const Default = Template.bind({});

Default.args = {
  columns: [
    {
      header: 'Status',
      accessor: 'Status',
      sorted: true,
      isDesc: false,
      sortingEnabled: true,
    },
    {
      header: 'Updated',
      accessor: 'Updated',
      sorted: false,
      isDesc: true,
      sortingEnabled: true,
    },
    {
      header: 'OfferId',
      accessor: 'OfferId',
      sorted: false,
      isDesc: true,
      sortingEnabled: true,
    },
    {
      header: 'Ref Id',
      accessor: 'ref',
      sorted: false,
      isDesc: true,
      sortingEnabled: true,
    },
    {
      header: 'Donor',
      accessor: 'Donor',
      sorted: false,
      isDesc: true,
      sortingEnabled: true,
    },
  ],
};
