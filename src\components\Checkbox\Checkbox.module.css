@value variables: "../../styles/variables.module.css";
@value black, black10, black35, black50, primary, primaryDark, white from variables;
@value twoSpace, bodyBaseFontSize from variables;
@value ease-out-expo from variables;

.checkbox {
  cursor: pointer;
  display: inline-flex;
  flex-flow: row;
  user-select: none;

  &.top {
    align-items: flex-start;
  }

  &.middle {
    align-items: center;
  }

  &.bottom {
    align-items: flex-end;
  }
}

.disabledPointerEvents {
  pointer-events: none;
}

.disabled {
  composes: disabledPointerEvents;
}

.primaryTheme {
  & .indicator {
    &:before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0);
      transform-origin: center center;
      background: primary;
      width: 100%;
      height: 100%;
      transition: transform 0.2s ease-out-expo;
    }
  }

  &.checked {
    & .indicator {
      border-color: primary;

      &:before {
        transform: translate(-50%, -50%) scale(1);
      }
    }

    & .checkIconWrapper {
      display: block;
    }
  }

  &.disabled {
    & .label {
      color: black35;
    }

    & .indicator {
      border-color: black10;

      &:before {
        background: black10;
      }
    }

    & .checkIconWrapper {
      color: black35;
    }
  }
}
.secondary {
  & .indicator {
    &:after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) scale(0);
      transform-origin: center center;
      background: primary;
      width: 0.5em;
      height: 0.5em;
      transition: transform 0.2s ease-out-expo;
    }
  }

  &.checked {
    & .indicator {
      border-color: primary;

      &:after {
        transform: translate(-50%, -50%) scale(1);
      }
    }
  }

  &.disabled {
    & .label {
      color: black35;
    }

    & .indicator {
      border-color: black10;

      &:after {
        background: black35;
      }
    }
  }
}

.indicator {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 2px;
  border: solid 2px black50;
  position: relative;
  transition: border-color 0.5s ease-out-expo;
  flex: 0 0 auto;
}

.checkIconWrapper {
  display: none;
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  line-height: 0;

  & svg {
    stroke-width: 4px;
  }
}

.label {
  margin-left: twoSpace;
  font-size: bodyBaseFontSize;
  line-height: 1.5;
  letter-spacing: 0.01em;
  color: black;
}
