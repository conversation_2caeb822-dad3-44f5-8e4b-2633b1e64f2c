import {
  Controls,
  Description,
  Primary,
  Subtitle,
  Title,
} from '@storybook/blocks';
import { Meta } from '@storybook/react';
import variableStyles from '@styles/variables.module.css';

import Divider from './Divider';
import { DividerProps } from './Divider.types';

/**
 *
 * <br/>

## Overview


The Divider component is a UI element used to visually separate content within a user interface. It typically renders as a horizontal line, visually indicating a division between sections or elements. In the provided code, the Divider component accepts a theme prop, allowing customization of its appearance based on predefined themes. 
To construct the divider from an integration perspective, 
developers need to provide props as shown in the Storybook and as described in the props documentation.


## Usage

Import the component into your React application:

```jsx
import { Divider } from '@peddleon/ped-ux-react-library';```
 

 */

const meta: Meta<typeof Divider> = {
  component: Divider,
  title: 'Core/Divider',
  tags: ['autodocs'],
  parameters: {
    docs: {
      toc: true,
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <Controls />
        </>
      ),
    },
  },
};

export const DividerStory = ({ theme }: DividerProps) => (
  <div
    style={{
      padding: variableStyles.fourSpace,
      border: `1px solid ${variableStyles.neutralLight}`,
      margin: variableStyles.fourSpace,
      borderRadius: '15px',
    }}
  >
    <Divider theme={theme} />
  </div>
);

DividerStory.argTypes = {
  theme: {
    defaultValue: 'dark',
  },
};
export default meta;
