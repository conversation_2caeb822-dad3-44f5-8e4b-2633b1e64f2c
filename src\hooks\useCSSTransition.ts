import { useCallback, useEffect, useState } from 'react';
import useIsomorphicLayoutEffect from '@hooks/useIsomorphicLayoutEffect';

const useCSSTransition = ({
  isActive,
  enterRef = null,
  exitRef = null,
  enterPropertyName = null,
  exitPropertyName = null,
}) => {
  const [state, setState] = useState({
    isExited: true,
    isExiting: false,
    isEntered: false,
    isEntering: false,
    isReady: false,
  });
  const { isReady, isExited, isExiting, isEntered, isEntering } = state;

  const onEnterTransitionEnd = useCallback(
    event => {
      const propertyNameMatch = enterPropertyName
        ? enterPropertyName.match(event?.propertyName)
        : true;

      if (isEntering && propertyNameMatch) {
        setState(previousState => ({
          ...previousState,
          isEntering: false,
          isEntered: true,
        }));
      }
    },
    [isEntering, enterPropertyName]
  );

  const onExitTransitionEnd = useCallback(
    event => {
      const propertyNameMatch = exitPropertyName
        ? exitPropertyName.match(event?.propertyName)
        : true;

      if (isExiting && propertyNameMatch) {
        setState(previousState => ({
          ...previousState,
          isEntered: false,
          isEntering: false,
          isExiting: false,
          isExited: true,
          isReady: false,
        }));
      }
    },
    [isExiting, exitPropertyName]
  );

  useEffect(() => {
    const localEnterRef = enterRef?.current;
    const localExitRef = exitRef?.current;

    if (isReady) {
      localEnterRef?.addEventListener(
        'transitionend',
        onEnterTransitionEnd,
        false
      );
      if (localExitRef) {
        localExitRef?.addEventListener(
          'transitionend',
          onExitTransitionEnd,
          false
        );
      } else {
        localEnterRef?.addEventListener(
          'transitionend',
          onExitTransitionEnd,
          false
        );
      }
    }

    return () => {
      localEnterRef?.removeEventListener('transitionend', onEnterTransitionEnd);
      localEnterRef?.removeEventListener('transitionend', onExitTransitionEnd);
      localExitRef?.removeEventListener('transitionend', onExitTransitionEnd);
    };
  }, [isReady, onEnterTransitionEnd, onExitTransitionEnd, enterRef, exitRef]);

  useEffect(() => {
    if (isReady) {
      if (isActive) {
        setState(previousState => ({
          ...previousState,
          isExited: false,
          isEntering: true,
        }));
      } else {
        setState(previousState => ({
          ...previousState,
          isEntered: false,
          isExiting: true,
        }));
      }
    }
  }, [isActive, isReady]);

  useIsomorphicLayoutEffect(() => {
    if (isActive && !isReady) {
      setState(previousState => ({
        ...previousState,
        isReady: true,
      }));
    }
  }, [isActive]);

  return {
    isReady,
    isEntering,
    isEntered,
    isExiting,
    isExited,
  };
};

export default useCSSTransition;
