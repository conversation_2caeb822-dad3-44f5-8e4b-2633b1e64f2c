// import React, { FC, useEffect, useRef, useState } from 'react';
// import { createPortal } from 'react-dom';
// import { useCombobox, UseComboboxStateChange } from 'downshift';
// import { AlertTriangleIcon, CheckIcon } from '@peddleon/ped-ux-react-icons';
// import AnimatedLoader from '@components/AnimatedLoader';
// import SelectInputMenu from '@components/SelectInputMenu/SelectInputMenu';
// import SelectInputMenuItem from '@components/SelectInputMenu/SelectInputMenuItem';
// import { SelectItemType } from '@libtypes/common';
// import useDebouncedState from '@hooks/useDebouncedState';
// import classNames from '@utils/classNames';
// import findScrollableParent from '@utils/findScrollableParent';

// import styles from './AutoComplete.module.css';
// import { AutoCompletePropTypes } from './AutoComplete.types';

// function escapeRegExp(str) {
//   return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
// }

// function highlightMatchingCharacters(str1, str2, displayLabel = undefined) {
//   // Escape special characters in the first string to create a valid regular expression
//   if (!str1?.trim()) return str2;
//   const escapedStr1 = escapeRegExp(str1);
//   // Create a regular expression to match all occurrences of the first string in the second string
//   let result = str2;

//   if (
//     displayLabel &&
//     displayLabel === str1 &&
//     str1.toLowerCase().includes(str2.toLowerCase())
//   ) {
//     result = `<span className={${styles.optionListMatchedCharacters}} style="font-weight: bold;">${str2}</span>`;
//   } else {
//     try {
//       result = str2.replace(
//         new RegExp(escapedStr1, 'gi'),
//         match =>
//           `<span className={${styles.optionListMatchedCharacters}} style="font-weight: bold;">${match}</span>`,
//       );
//     } catch (error) {
//       // eslint-disable-next-line no-console
//       console.error('[Ped-ux-react-library]: regex error', error);
//     }
//   }

//   // ** We'll be only getting these records from backend so doesn't require to have sanitize
//   // eslint-disable-next-line react/no-danger
//   return <div dangerouslySetInnerHTML={{ __html: result }} />;
// }

// const AutoComplete: FC<AutoCompletePropTypes> = ({
//   id = null,
//   label = '',
//   error = '',
//   caption = '',
//   left = null,
//   right = null,
//   items: propsItem,
//   inputValue,
//   value,
//   hasSucceeded = false,
//   isLoading = false,
//   isDisabled = false,
//   isBorderless = false,
//   onInputValueChange,
//   onChange,
//   onFocus = () => {},
//   onBlur = () => {},
//   onInvalid = () => {},
//   required = false,
//   maxLength = 50,
//   name,
//   isPoweredByGoogle = false,
//   isFixedMenu = false,
//   isGrouped = true,
// }) => {
//   const items = [];

//   propsItem.forEach(item => {
//     if (item.options && item.options.length > 0) {
//       item.options.forEach((option, index) => {
//         if (!index) {
//           items.push({
//             isFirst: true,
//             menuLabel: item.label,
//             ...option,
//           });
//         } else items.push(option);
//       });
//     } else {
//       items.push(item);
//     }
//   });

//   const selectContainerRef = useRef<HTMLDivElement>(null);
//   const menuListRef = useRef<HTMLDivElement>(null);
//   const selectWrapperRef = useRef<HTMLDivElement>(null);
//   const menuList = useRef(null);
//   const dropdownRef = useRef(null);

//   const selectedItem = value && items.find(item => item.value === value);

//   const [isFocused, setIsFocused] = useState(false);
//   const inputRef = useRef<HTMLInputElement>(null);
//   const {
//     isOpen: comboboxIsOpen,
//     getComboboxProps,
//     getInputProps,
//     getLabelProps,
//     getMenuProps,
//     highlightedIndex,
//     getItemProps,
//     setInputValue,
//   } = useCombobox({
//     id,
//     items,
//     itemToString: item => item?.label,
//     selectedItem,
//     onSelectedItemChange: ({ selectedItem: updatedSelectedItem }) => {
//       onChange(updatedSelectedItem);
//     },
//     onInputValueChange: (
//       event: UseComboboxStateChange<SelectItemType> & { name: string },
//     ) => {
//       /* eslint-disable */
//       event.name = name;
//       /* eslint-enable */
//       onInputValueChange(event);
//     },
//   });

//   useEffect(() => {
//     const handleDropdown = () => {
//       if (!selectWrapperRef.current || !menuListRef.current) return;

//       const {
//         top: wrapperTop,
//         left: wrapperLeft,
//         bottom: wrapperBottom,
//         width: selectWidth,
//       } = selectWrapperRef.current.getBoundingClientRect();
//       const { height: menuListHeight } =
//         menuListRef.current.getBoundingClientRect();

//       requestAnimationFrame(() => {
//         if (!menuListHeight) return;
//         if (selectWidth > 0) {
//           menuListRef.current.style.maxWidth = `${selectWidth}px`;
//         }

//         menuListRef.current.style.left = `${wrapperLeft}px`;
//         let top = 0;
//         if (window.innerHeight < wrapperBottom + menuListHeight + 16) {
//           top = wrapperTop - menuListHeight - 8;
//         } else {
//           if (!selectContainerRef?.current?.closest('.dialogModalWrapper')) {
//             top += 8;
//           } else {
//             top += caption ? 16 : 8;
//           }
//           top += wrapperBottom;
//         }
//         menuListRef.current.style.top = `${top}px`;
//       });
//     };

//     if (isFixedMenu) {
//       let scrollableAncestor;
//       if (selectWrapperRef.current) {
//         scrollableAncestor = findScrollableParent(selectWrapperRef.current);
//       }
//       if (comboboxIsOpen) {
//         handleDropdown();
//         if (scrollableAncestor) {
//           scrollableAncestor.addEventListener('scroll', handleDropdown);
//         }
//         window.addEventListener('scroll', handleDropdown);
//         window.addEventListener('resize', handleDropdown);
//       } else {
//         if (scrollableAncestor) {
//           scrollableAncestor.addEventListener('scroll', handleDropdown);
//         }
//         window.removeEventListener('scroll', handleDropdown);
//         window.removeEventListener('resize', handleDropdown);
//       }
//     }

//     return () => {
//       if (selectWrapperRef.current) {
//         const scrollableAncestor = findScrollableParent(
//           selectWrapperRef.current,
//         );
//         scrollableAncestor.removeEventListener('scroll', handleDropdown);
//       }
//       window.removeEventListener('scroll', handleDropdown);
//       window.removeEventListener('resize', handleDropdown);
//     };
//   }, [isFixedMenu, comboboxIsOpen, inputValue, items]);

//   const hasValue = inputValue?.length > 0;
//   const hasError = error.length > 0;
//   const hasCaption = caption.length > 0;
//   const isOpen = comboboxIsOpen && items.length > 0;

//   let statusRightIcon = null;
//   if (hasSucceeded) {
//     statusRightIcon = (
//       <span className={styles.successIconWrapper}>
//         <CheckIcon height={24} width={24} />
//       </span>
//     );
//   } else if (hasError) {
//     statusRightIcon = (
//       <span className={styles.errorIconWrapper}>
//         <AlertTriangleIcon width={24} height={24} />
//       </span>
//     );
//   } else if (isLoading) {
//     statusRightIcon = (
//       <span className={styles.fetchingIconWrapper}>
//         <AnimatedLoader />
//       </span>
//     );
//   }

//   const debouncedInput = useDebouncedState(inputValue, 500);

//   /* eslint-disable react-hooks/exhaustive-deps */
//   useEffect(() => {
//     setInputValue(debouncedInput);
//   }, [debouncedInput]);
//   /* eslint-enable react-hooks/exhaustive-deps */

//   const inputMenu = (
//     <SelectInputMenu
//       isOpen={isOpen}
//       getMenuProps={getMenuProps}
//       isPoweredByGoogle={isPoweredByGoogle}
//       className={classNames(
//         styles.menuSelectInput,
//         isFixedMenu && styles.fixedListWrapper,
//       )}
//       ref={menuListRef}
//     >
//       <div ref={dropdownRef} className="pt-2">
//         {items.length === 0
//           ? 'no records'
//           : items.map((item, index) => (
//               <SelectInputMenuItem
//                 isGroupTypeLayout={isGrouped}
//                 key={`${item.value || index}`} // Add index to make the key unique
//                 isHighlighted={highlightedIndex === index}
//                 secondaryLabel={item.secondaryLabel}
//                 isFirst={!index && item?.isFirst}
//                 item={item}
//                 {...getItemProps({ item, index })}
//               >
//                 {highlightMatchingCharacters(inputValue?.trim(), item.label)}
//               </SelectInputMenuItem>
//             ))}
//       </div>
//     </SelectInputMenu>
//   );

//   return (
//     <div
//       className={classNames(
//         styles.combobox,
//         isDisabled && styles.disabled,
//         isFocused && styles.focused,
//         isBorderless && styles.borderless,
//         isOpen && styles.active,
//         hasValue && styles.filled,
//         hasError && styles.errored,
//         hasCaption && styles.captioned,
//       )}
//       ref={selectContainerRef}
//     >
//       <div ref={selectWrapperRef}>
//         <div className={styles.container} {...getComboboxProps()}>
//           {left && <span className={styles.leftWrapper}>{left}</span>}

//           <div className={styles.inputWrapper}>
//             {label.length > 0 && (
//               // eslint-disable-next-line jsx-a11y/label-has-associated-control
//               <label {...getLabelProps()} className={styles.label}>
//                 {label}
//               </label>
//             )}

//             <input
//               {...getInputProps({ ref: inputRef, type: 'text' })}
//               name={name || id}
//               className={classNames(
//                 styles.input,
//                 label.length > 0 && styles.hasLabel,
//                 isDisabled && styles.disabled,
//               )}
//               onFocus={() => {
//                 setIsFocused(true);
//                 onFocus();
//               }}
//               onBlur={() => {
//                 setIsFocused(false);
//                 inputRef.current.checkValidity();
//                 onBlur();
//               }}
//               onInvalid={() => {
//                 if (onInvalid) {
//                   onInvalid({
//                     id,
//                     label,
//                     value,
//                     validity: inputRef.current.validity,
//                     name,
//                   });
//                 }
//               }}
//               required={required}
//               disabled={isDisabled}
//               maxLength={maxLength}
//               autoComplete="off"
//               autoCorrect="off"
//             />
//           </div>

//           {(right || hasError || hasSucceeded || isLoading) && (
//             <span className={styles.rightWrapper}>
//               {right}
//               {statusRightIcon}
//             </span>
//           )}
//         </div>
//       </div>
//       {/* <div className={styles.container} ref={selectWrapperRef}>
//         <div className={styles.menuWrapper}>{inputMenu}</div>
//       </div> */}

//       <div className={styles.active}>
//         <div className={styles.menuWrapper} ref={menuList}>
//           {isFixedMenu
//             ? createPortal(
//                 inputMenu,
//                 selectContainerRef?.current?.closest('.dialogModalWrapper') ??
//                   document.body,
//               )
//             : inputMenu}
//         </div>
//       </div>

//       {(hasError || hasCaption) && (
//         <div className={styles.footer}>
//           {hasError && <span className={styles.error}>{error}</span>}
//           {hasCaption && <span className={styles.caption}>{caption}</span>}
//         </div>
//       )}
//     </div>
//   );
// };

// export default AutoComplete;
