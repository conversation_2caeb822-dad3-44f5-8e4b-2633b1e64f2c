/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {
  FC,
  forwardRef,
  ReactNode,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Cell,
  Column,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  Header,
  PaginationState,
  Row,
  SortingFn,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import Checkbox from '@components/Checkbox';
import Scrims from '@components/Scrims';
import { NO_RECORD_FOUND } from '@constants/components';
import useMediaQueryState from '@hooks/useMediaQueryState';
import useWindowSize from '@hooks/useWindowSize';
// utils
import classNames from '@utils/classNames';
import { isInViewport } from '@utils/isInViewport';

import { AnimatedLoader, DetailedCTA, Typography } from '..';
import TableFooterRow from './TableFooter/TableFooterRow';
// Table components & styles
import styles from './Table.module.css';
import TableCard from './TableCard';
import TableHeader from './TableHeader';
import TablePagination from './TablePagination';
import TableRow from './TableRow';
import TableText from './TableText';

type TData = any;
type TValue = any;

interface ColumnType {
  id?: string;
  accessorKey?: string;
  accessorFn?: (originalRow: any, index: number) => any;
  header?:
    | string
    | ((props: {
        table: any;
        header: Header<TData, TValue>;
        column: Column<TData>;
      }) => unknown);
  footer?:
    | string
    | ((props: {
        table: any;
        header: Header<TData, TValue>;
        column: Column<TData>;
      }) => unknown);
  cell?:
    | string
    | ((props: {
        table: any;
        row: Row<TData>;
        column: Column<TData>;
        cell: Cell<TData, TValue>;
        getValue: () => any;
        renderValue: () => any;
      }) => unknown);
  enableSorting?: boolean;
  size?: string | number;
  maxSize?: string | number;
  minSize?: string | number;
  isUsedForStatistics?: boolean;
  grandTotal?: string;
}

interface RowType {
  id: string;
  depth: number;
  index: number;
  original: TData;
  parentId?: string;
  getValue: (columnId: string) => TValue;
  renderValue: (columnId: string) => TValue;
  getUniqueValues: (columnId: string) => TValue[];
}
export type RowSelectionState = Record<string, boolean>;

export const MIN_PAGE_SIZE = 20;

export type FooterConfig = {
  left?: ReactNode | string;
  right?: ReactNode | string;
};

/**
 * Props for the Table component.
 */
export interface TableProps {
  /** Array of column configurations for the table */
  columns: ColumnType[] | any;
  /** Array of row data objects */
  rowData: RowType[] | any;
  /** Flag to indicate if the table is inverted */
  isInverted?: boolean;
  /** Additional class name for styling */
  className?: string;
  /** Callback function for handling pagination */
  handlePagination?: (page: PaginationState) => void;
  /** Callback function for handling sorting */
  onChangeSort?: (sort: SortingState) => void;
  /** Flag to indicate if the first column is pinned */
  hasFirstColumnPinned?: boolean;
  /** Details for pagination */
  paginationDetails?: PaginationState;
  /** Total count of items */
  totalCount?: number;
  /** Height of the table body */
  bodyHeight?: string;
  /** Callback function for row click event */
  onRowClick?: (data: any) => void;
  /** sorting funcation */
  sortingFns?: Record<string, SortingFn<any>>;
  /** To select a row when clicking checkbox */
  onRowsSelection?: (rows) => void;
  /** Pass the selected rows from here */
  rowSelection?: RowSelectionState;
  /** To allow Row selection with checkbox */
  hasRowSelection?: boolean;
  /**
   * to map the data with the mobile card component
   */
  mobileCardProps?: any;
  /**
   * show card view for mobile
   */
  hasCardView?: boolean;
  /**
   * serverSorting
   */
  serverSorting?: SortingState;
  /**
   * Initial default sorting
   */
  defaultSorting?: SortingState;
  /**
   * client search filter
   */
  clientSearchFilter?: string;
  /**
   *
   */
  hasManualPagination?: boolean;
  /**
   * onRowCountChange
   */
  onRowCountChange?: (rowCount: number) => void;
  /**
   *
   */
  onColumnOptionClick?: (columnId: string, optionId: string) => void;
  /**
   * searchable columns
   */
  searchableColumns?: string[];
  /**
   * Footer config where you can set left and right content
   */
  footerConfig?: FooterConfig[];
  /**
   * Flag to indicate if the data is currently being fetched
   */
  isDataFetching?: boolean;
  /**
   * Column visibility
   */
  columnVisibility?: Record<string, boolean>;
  /**
   * Should auto scroll
   */
  shouldAutoScroll?: boolean;
  /**
   * callback on handleAllRowsSelection
   */
  handleAllRowsSelection?: (value: boolean) => void;

  /**
   * No record configuration object expects an illustration, a heading, and a body.
   */
  noRecordConfig?: {
    illustration: ReactNode;
    heading: string;
    body: string;
  };

  /**
   * This will be used to show the no record found message.
   * This props will be kept for the backward compatibility once the noRecordConfig is rollout this will be removed from the component.
   */
  noRecordFound?: string;
}
const Table: FC<TableProps> = forwardRef(
  (
    {
      columns,
      rowData,
      isInverted,
      className,
      handlePagination = () => {},
      onChangeSort = () => {},
      hasFirstColumnPinned = false,
      paginationDetails = {
        pageIndex: 0,
        pageSize: 20,
      },
      totalCount,
      bodyHeight = 'calc(100vh - 220px)',
      onRowClick = () => {},
      sortingFns = {},
      rowSelection = {},
      hasRowSelection = false,
      footerConfig = [],
      mobileCardProps = () => <div />,
      hasCardView,
      noRecordFound = NO_RECORD_FOUND,
      serverSorting = [],
      defaultSorting = [{ id: 'Description', desc: true }],
      clientSearchFilter = '',
      hasManualPagination = false,
      onRowCountChange = () => {},
      onColumnOptionClick = () => {},
      searchableColumns = [],
      isDataFetching = false,
      onRowsSelection = () => {},
      columnVisibility = {},
      shouldAutoScroll = false,
      handleAllRowsSelection = () => {},
      noRecordConfig,
    },
    ref,
  ) => {
    const data = rowData;
    const isMobile = useMediaQueryState({ query: '(max-width: 480px)' });
    const [rowsSelection, setRowsSelection] =
      useState<RowSelectionState>(rowSelection);
    const [allRowsSelected, setAllRowsSelected] = useState(false);
    const localBodyWrapperMobileRef = useRef(null);
    const windowsWidth = useWindowSize();
    const paginationRef = useRef(null);

    const [sorting, setSorting] = useState<SortingState>(serverSorting);
    const lastCheckedIndex = useRef<number | null>(null);
    const [pagination, setPagination] = useState<PaginationState>({
      pageIndex: paginationDetails.pageIndex,
      pageSize: paginationDetails.pageSize,
    });

    const [tableComponentHeights, setTableComponentHeights] = useState({
      header: '70px',
      footer: '70px',
      pagination: '0px',
    });

    const localBodyWrapperRef = useRef(null);
    const tableHeaderRef = useRef(null);
    const tableFooterRef = useRef(null);

    useEffect(() => {
      if (sorting?.length && serverSorting?.length) {
        const [sortingDetails] = sorting;
        const { id, desc } = sortingDetails;
        const [serverSortingDetails] = serverSorting;
        const { id: serverId, desc: serverDesc } = serverSortingDetails;

        if (id !== serverId || desc !== serverDesc) {
          setSorting(serverSorting);
        }
      }
    }, [hasManualPagination, serverSorting, sorting]);

    useEffect(() => {
      if (defaultSorting.length) {
        setSorting(defaultSorting);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    let table;
    const handlePaginationChange = (value: PaginationState) => {
      if (table) table?.resetRowSelection(true);
      handlePagination(value);
      setPagination(value);
    };

    const handleSortingChange = (value: SortingState) => {
      onChangeSort(value);
      setSorting(value);
    };

    const handleRowSelection = (
      rowIndex: number,
      isChecked: boolean,
      event: any,
    ) => {
      if (event?.nativeEvent?.shiftKey && table) {
        const sortedRows = table?.getRowModel()?.rows;
        const sortedRowIndices = sortedRows.map(row => row?.id); // Get the current order of row indices after sorting
        if (lastCheckedIndex.current !== null) {
          const startIndex = Math.min(
            sortedRowIndices.indexOf(lastCheckedIndex.current),
            sortedRowIndices.indexOf(rowIndex),
          );
          const endIndex = Math.max(
            sortedRowIndices.indexOf(lastCheckedIndex.current),
            sortedRowIndices.indexOf(rowIndex),
          );

          const newSelection: RowSelectionState = { ...rowsSelection };

          // Loop over sorted row indices, and select them all within the range
          for (let i = startIndex; i <= endIndex; i += 1) {
            newSelection[sortedRows[i]?.id] = isChecked;
          }
          setRowsSelection(() => newSelection);
        } else {
          setRowsSelection((prev: RowSelectionState) => ({
            ...prev,
            [rowIndex]: isChecked,
          }));
        }
      } else {
        setRowsSelection((prev: RowSelectionState) => ({
          ...prev,
          [rowIndex]: isChecked,
        }));
      }

      lastCheckedIndex.current = rowIndex;
    };

    let tableColumns = columns;
    if (hasRowSelection) {
      tableColumns = [
        {
          id: 'select',
          maxSize: '40px',
          size: '40px',
          enableSorting: false,
          // eslint-disable-next-line react/no-unstable-nested-components
          header: ({ table: tableProps }) => (
            <Checkbox
              {...{
                isChecked: tableProps.getIsAllPageRowsSelected(),
                indeterminate: tableProps.getIsSomePageRowsSelected(),
                onChange: tableProps.getToggleAllPageRowsSelectedHandler(),
              }}
            />
          ),
          // eslint-disable-next-line react/no-unstable-nested-components
          cell: ({ row: rowProps }) => (
            <TableText variant="default">
              <Checkbox
                {...{
                  isChecked: rowProps.getIsSelected(),
                  disabled: !rowProps.getCanSelect(),
                  indeterminate: rowProps.getIsSomeSelected(),
                  onChange: (event: any) => {
                    // Call the default handler for single selection/deselection
                    rowProps.getToggleSelectedHandler()(event);

                    // Add custom Shift key selection handling
                    handleRowSelection(
                      rowProps.id,
                      event.target.checked,
                      event,
                    );
                  },
                }}
              />
            </TableText>
          ),
        },
        ...tableColumns,
      ];
    }

    table = useReactTable({
      data,
      columns: tableColumns,
      enableHiding: true,
      getRowId: (row: RowType) => row.id,
      getCoreRowModel: getCoreRowModel(),
      enableColumnResizing: true,
      columnResizeMode: 'onChange',
      enableSortingRemoval: false,
      getSortedRowModel: getSortedRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
      onPaginationChange: handlePaginationChange,
      // if pagination is manual then we'll consider that as the server sorting
      manualSorting: hasManualPagination,
      onSortingChange: handleSortingChange,
      enableColumnPinning: true,
      manualPagination: hasManualPagination,
      state: {
        columnVisibility,
        sorting,
        columnPinning: {
          left: hasFirstColumnPinned ? [tableColumns[0]?.id] : [],
        },
        pagination: hasManualPagination ? paginationDetails : pagination,
        rowSelection: rowsSelection,
        globalFilter: clientSearchFilter,
      },
      initialState: {
        //  initial state
        globalFilter: clientSearchFilter,
      },
      rowCount: totalCount,
      sortingFns,
      onRowSelectionChange: setRowsSelection,
      // autoResetAll: false,
      globalFilterFn: (row, columnId, filterValue) => {
        if (!searchableColumns.includes(columnId)) return false;

        return String(row.getValue(columnId))
          .toLowerCase()
          .includes(String(filterValue).toLowerCase());
      },
      getFilteredRowModel: getFilteredRowModel(),
      autoResetAll: false,
    });

    useImperativeHandle(ref, () => ({
      getIsAllPageRowsSelected() {
        return allRowsSelected;
      },

      getToggleAllPageRowsSelectedHandler() {
        table.getRowModel().rows.forEach(row => {
          handleRowSelection(row.id, !allRowsSelected, {});
        });
      },
    }));

    useEffect(() => {
      handleAllRowsSelection(allRowsSelected);
    }, [allRowsSelected, handleAllRowsSelection]);

    useEffect(() => {
      const selectedRows = table.getSelectedRowModel().rows.length;
      const totalRows = table.getRowModel().rows.length;
      if (selectedRows === totalRows && totalRows > 0) {
        setAllRowsSelected(true);
      } else {
        setAllRowsSelected(false);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [table.getSelectedRowModel().rows.length]);

    const statisticsColumn = useMemo(
      () =>
        table &&
        table
          .getAllColumns()
          .find(column => column?.columnDef?.isUsedForStatistics),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [table?.getAllColumns()],
    );

    let hasPaginationVisible;
    if (hasManualPagination) {
      hasPaginationVisible =
        totalCount > (statisticsColumn ? 1 : MIN_PAGE_SIZE);
    } else {
      hasPaginationVisible =
        data.length > (statisticsColumn ? 1 : MIN_PAGE_SIZE);
    }

    const { pageSize, pageIndex } = table.getState().pagination;

    useEffect(() => {
      setTableComponentHeights({
        header: tableHeaderRef.current?.clientHeight,
        footer: tableFooterRef.current?.clientHeight,
        pagination: paginationRef.current?.clientHeight,
      });
    }, [isMobile, pageSize, windowsWidth, pageIndex]);

    useEffect(() => {
      onRowCountChange(table.getRowCount());
    }, [onRowCountChange, table]);

    const selectedRowsData = useMemo(
      () => table?.getSelectedRowModel()?.rows?.map(row => row?.original),
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [rowSelection],
    );

    useEffect(() => {
      if (
        tableHeaderRef.current &&
        !isInViewport(tableHeaderRef.current) &&
        shouldAutoScroll
      ) {
        tableHeaderRef.current.scrollIntoView({ behavior: 'smooth' });
      }
    }, [pageIndex, shouldAutoScroll]);

    useEffect(() => {
      if (table) {
        onRowsSelection(selectedRowsData);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [rowsSelection]);

    const noRecords = table.getRowModel().rows.length === 0;

    const noRecordCTADetails = noRecordConfig?.body && (
      <DetailedCTA
        illustration={noRecordConfig?.illustration}
        footer={null}
        heading={
          noRecordConfig.heading && (
            <Typography tag="h3" strong className={styles.noRecordHeader}>
              {noRecordConfig.heading}
            </Typography>
          )
        }
        body={
          noRecordConfig.body && (
            <Typography
              tag="p"
              variant="bodyBase"
              className={styles.noRecordBody}
            >
              {noRecordConfig.body}
            </Typography>
          )
        }
        size="medium"
        position="center"
      />
    );

    hasPaginationVisible = noRecords ? false : hasPaginationVisible;

    const mobileFooter = footerConfig && footerConfig.length > 0 && (
      <div className={classNames('footerWrapper', styles.footerWrapper)}>
        {footerConfig.map((config, index) => (
          <TableFooterRow
            left={config.left}
            right={config.right}
            needToAddDevider={
              footerConfig.length > 1 && index < footerConfig.length - 1
            }
          />
        ))}
      </div>
    );

    if (hasCardView && isMobile)
      return (
        <div
          style={{
            '--tableHeader': `${tableComponentHeights.header}px`,
            '--tableFooter': `-${tableComponentHeights.footer}px`,
            '--tablePagination': ` ${Number(hasPaginationVisible ? tableComponentHeights.pagination : 0)}px`,
          }}
          className={styles.tableMobileRoot}
        >
          <div
            ref={localBodyWrapperMobileRef}
            style={{
              maxHeight: bodyHeight,
            }}
            className={styles.tableMobileBody}
          >
            {noRecords && !isDataFetching && (
              <div className={styles.centerContent}>
                {noRecordConfig?.body ? noRecordCTADetails : noRecordFound}
              </div>
            )}
            {isDataFetching ? (
              <div>
                <div className={styles.centerContent}>
                  <AnimatedLoader />
                </div>
              </div>
            ) : (
              <>
                {table.getRowModel().rows.map(item => (
                  <TableCard
                    {...mobileCardProps(item.original, {})}
                    key={mobileCardProps(item.original, {}).id}
                    onClick={() => onRowClick(item.original)}
                    index={item.id}
                    handleRowSelection={handleRowSelection}
                    rowSelection={rowsSelection}
                    hasRowSelection={hasRowSelection}
                  />
                ))}
                {mobileFooter}
              </>
            )}
          </div>

          {bodyHeight !== '100%' && (
            <div>
              <Scrims
                overflowRef={localBodyWrapperMobileRef}
                isInverted={isInverted}
                bottomClassName={styles.bottomClassName}
              />
            </div>
          )}

          {hasPaginationVisible && !isDataFetching && (
            <TablePagination
              table={table}
              isInverted={isInverted}
              ref={paginationRef}
              selectedRows={selectedRowsData}
            />
          )}
        </div>
      );

    const [columnsLength] = table
      .getHeaderGroups()
      .map(item => item?.headers?.length);

    return (
      <div
        className={styles.tableMain}
        style={{
          '--tableHeader': `${tableComponentHeights.header}px`,
          '--tableFooter': `-${tableComponentHeights.footer}px`,
          '--tablePagination': ` ${Number(hasPaginationVisible ? tableComponentHeights.pagination : 0)}px`,
        }}
      >
        <div className={styles.tableRoot} ref={localBodyWrapperRef}>
          <div
            className={classNames(
              styles.table,
              isInverted && `${styles.isInverted} isInverted`,
              className,
            )}
            style={{
              maxHeight: bodyHeight,
            }}
          >
            <table
            // if sticky column
            // style={{ width: table.getTotalSize()}}
            >
              <TableHeader
                ref={tableHeaderRef}
                columns={table.getHeaderGroups()}
                isInverted={isInverted}
                hasFirstColumnPinned={hasFirstColumnPinned}
                setSorting={setSorting}
                onColumnOptionClick={onColumnOptionClick}
              />

              {noRecords && !isDataFetching && (
                <tbody>
                  <tr>
                    <td
                      colSpan={columnsLength}
                      className={classNames(
                        styles.centerRecord,
                        noRecordConfig?.body && styles.hasNoRecordConfig,
                      )}
                    >
                      {noRecordConfig?.body
                        ? noRecordCTADetails
                        : noRecordFound}
                    </td>
                  </tr>
                </tbody>
              )}
              {isDataFetching ? (
                <tbody>
                  <tr>
                    {/* eslint-disable-next-line jsx-a11y/control-has-associated-label */}
                    <td colSpan={columnsLength} className={styles.centerRecord}>
                      <AnimatedLoader />
                    </td>
                  </tr>
                </tbody>
              ) : (
                <tbody>
                  {table.getRowModel().rows.map(row => (
                    <TableRow
                      column={columns}
                      key={row.id}
                      row={row}
                      onClick={onRowClick}
                    />
                  ))}
                </tbody>
              )}

              {/* To be implemented if needed in the future. */}
              {/* <tfoot ref={tableFooterRef}>{footer}</tfoot> */}
            </table>
          </div>
        </div>
        {bodyHeight !== '100%' && (
          <Scrims
            overflowRef={localBodyWrapperRef}
            isInverted={isInverted}
            topClassName={styles.topClassName}
            bottomClassName={hasPaginationVisible && styles.bottomClassName}
          />
        )}

        {hasPaginationVisible && !isDataFetching && (
          <TablePagination
            table={table}
            isInverted={isInverted}
            ref={paginationRef}
            selectedRows={selectedRowsData}
          />
        )}
      </div>
    );
  },
);

export default Table;
