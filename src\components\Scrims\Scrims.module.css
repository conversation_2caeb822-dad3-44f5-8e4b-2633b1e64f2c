@value variables: '../../styles/variables.module.css';
@value ease-out-expo, black from variables;

.gradient {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
  border-radius: inherit;
  transition: opacity 0.5s ease-out-expo;
  opacity: 0;
}

.topGradient {
  composes: gradient;
  background: linear-gradient(
    180deg,
    #ffffff 0%,
    rgba(255, 255, 255, 0) 20.31%
  );

  &.isInverted {
    background: linear-gradient(
      180deg,
      black 0%,
      rgba(255, 255, 255, 0) 20.31%
    );
  }
}

.bottomGradient {
  composes: gradient;
  background: linear-gradient(rgba(255, 255, 255, 0) 79.69%, #ffffff 100%);
  &.isInverted {
    background: linear-gradient(rgba(255, 255, 255, 0) 79.69%, black 100%);
  }
}

.leftGradient {
  composes: gradient;
  background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0) 20.31%);
  &.isInverted {
    background: linear-gradient(90deg, black 0%, rgba(255, 255, 255, 0) 20.31%);
  }
}

.rightGradient {
  composes: gradient;
  background: linear-gradient(
    -90deg,
    #ffffff 0%,
    rgba(255, 255, 255, 0) 20.31%
  );
  &.isInverted {
    background: linear-gradient(
      -90deg,
      black 0%,
      rgba(255, 255, 255, 0) 20.31%
    );
  }
}

.visible {
  opacity: 1;
}
